import * as PUPPET from 'wechaty-puppet';
import { Options } from 'p-throttle';
import * as wechaty from 'wechaty';
import { WechatyPlugin } from 'wechaty';
import { matchers } from 'wechaty-plugin-contrib';
import { Driver } from 'unstorage';

interface SafeModeOptions {
    /** 全局节流配置 */
    globalThrottleOptions?: Options;
    /** 会话节流配置 */
    throttleOptions?: Options;
    /**
     * 不同会话最小间隔
     * @default 3000
     */
    minIntervalBetweenConversations?: number;
    /**
     *  不同会话最大间隔
     * @default 5000
     */
    maxIntervalBetweenConversations?: number;
}
declare function createSafeModePuppet<T extends PUPPET.Puppet>(puppet: T, options?: SafeModeOptions): T;

type WechatyPluginFactory<T> = (opts: T) => WechatyPlugin;
declare function defineWechatyPlugin<T = any>(factory: WechatyPluginFactory<T>): WechatyPluginFactory<T>;

interface RoomMuteOptions {
    /** 启用的群聊 */
    room: matchers.RoomMatcherOptions;
    /** 管理员，管理员不能互相禁言 */
    admin: matchers.ContactMatcherOptions;
    /** 可选，你可以传递一个 redis/fs 进来，这样机器人重启后也能保持封禁 */
    driver?: Driver;
}
declare const wechatyPluginRoomMute: (opts: RoomMuteOptions) => wechaty.WechatyPlugin;

interface RoomKickOptions {
    /** 启用的群聊 */
    room: matchers.RoomMatcherOptions;
    /** 管理员，管理员不能互相踢 */
    admin: matchers.ContactMatcherOptions;
    /** 黑名单消息匹配器 */
    blackListMessage?: matchers.MessageMatcherOptions;
}
declare const wechatyPluginRoomKick: (opts: RoomKickOptions) => wechaty.WechatyPlugin;

export { type RoomKickOptions, type RoomMuteOptions, createSafeModePuppet, defineWechatyPlugin, wechatyPluginRoomKick, wechatyPluginRoomMute };
