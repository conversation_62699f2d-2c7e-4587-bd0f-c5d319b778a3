{"swagger": "2.0", "info": {"description": "基于 WeChatFerry RPC 实现的微信接口，使用 Go 语言编写，无第三方运行时依赖，易于对接任意编程语言。", "title": "<PERSON>rest Chat Api", "contact": {"name": "WeChatRest", "url": "https://github.com/opentdp/wrest-chat"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "v0.10.0"}, "basePath": "/", "paths": {"/wcf/accept_new_friend": {"post": {"produces": ["application/json"], "tags": ["WCF::联系人管理"], "summary": "接受好友请求", "parameters": [{"description": "接受好友参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.AcceptNewFriendRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/add_chatroom_members": {"post": {"produces": ["application/json"], "tags": ["WCF::群聊管理"], "summary": "添加群成员", "parameters": [{"description": "管理群成员参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.ChatroomMembersRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/alias_in_chatroom": {"post": {"produces": ["application/json"], "tags": ["WCF::群聊管理"], "summary": "获取群成员昵称", "parameters": [{"description": "获取群成员昵称参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.GetAliasInChatRoomRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/wcf/audio_msg": {"post": {"produces": ["application/json"], "tags": ["WCF::消息收取"], "summary": "获取语音消息", "parameters": [{"description": "获取语音消息参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.GetAudioMsgRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/avatars": {"post": {"produces": ["application/json"], "tags": ["WCF::联系人管理"], "summary": "获取头像列表", "parameters": [{"description": "获取头像列表参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.GetAvatarsRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/wcfrest.AvatarPayload"}}}}}}, "/wcf/chatroom_members": {"post": {"produces": ["application/json"], "tags": ["WCF::群聊管理"], "summary": "获取群成员列表", "parameters": [{"description": "获取群成员列表参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.GetChatRoomMembersRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/wcfrest.ContactPayload"}}}}}}, "/wcf/chatrooms": {"post": {"produces": ["application/json"], "tags": ["WCF::群聊管理"], "summary": "获取群列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/wcfrest.ContactPayload"}}}}}}, "/wcf/contacts": {"post": {"produces": ["application/json"], "tags": ["WCF::联系人管理"], "summary": "获取完整通讯录", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/wcfrest.ContactPayload"}}}}}}, "/wcf/db_names": {"post": {"produces": ["application/json"], "tags": ["WCF::数据库查询"], "summary": "获取数据库列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/wcf/db_query_sql": {"post": {"produces": ["application/json"], "tags": ["WCF::数据库查询"], "summary": "执行数据库查询", "parameters": [{"description": "数据库查询参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.DbSqlQueryRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "object", "additionalProperties": true}}}}}}, "/wcf/db_tables": {"post": {"produces": ["application/json"], "tags": ["WCF::数据库查询"], "summary": "获取数据库表列表", "parameters": [{"description": "获取数据库表列表参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.GetDbTablesRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/wcfrest.DbTablePayload"}}}}}}, "/wcf/del_chatroom_members": {"post": {"produces": ["application/json"], "tags": ["WCF::群聊管理"], "summary": "删除群成员", "parameters": [{"description": "管理群成员参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.ChatroomMembersRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/disable_receiver": {"post": {"produces": ["application/json"], "tags": ["WCF::消息推送"], "summary": "关闭推送消息到URL", "parameters": [{"description": "推送消息到URL参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.ReceiverRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/download_attach": {"post": {"produces": ["application/json"], "tags": ["WCF::消息收取"], "summary": "下载附件", "parameters": [{"description": "下载附件参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.DownloadAttachRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/download_image": {"post": {"produces": ["application/json"], "tags": ["WCF::消息收取"], "summary": "下载图片", "parameters": [{"description": "下载图片参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.DownloadImageRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/enable_receiver": {"post": {"produces": ["application/json"], "tags": ["WCF::消息推送"], "summary": "开启推送消息到URL", "parameters": [{"description": "推送消息到URL参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.ReceiverRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/forward_msg": {"post": {"produces": ["application/json"], "tags": ["WCF::消息发送"], "summary": "转发消息", "parameters": [{"description": "转发消息参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.ForwardMsgRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/friends": {"post": {"produces": ["application/json"], "tags": ["WCF::联系人管理"], "summary": "获取好友列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/wcfrest.ContactPayload"}}}}}}, "/wcf/invite_chatroom_members": {"post": {"produces": ["application/json"], "tags": ["WCF::群聊管理"], "summary": "邀请群成员", "parameters": [{"description": "管理群成员参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.ChatroomMembersRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/is_login": {"post": {"produces": ["application/json"], "tags": ["WCF::其他"], "summary": "检查登录状态", "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/wcf/login_qr": {"post": {"produces": ["application/json"], "tags": ["WCF::其他"], "summary": "登录二维码", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/msg_types": {"post": {"produces": ["application/json"], "tags": ["WCF::其他"], "summary": "获取所有消息类型", "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/wcf/ocr_result": {"post": {"produces": ["application/json"], "tags": ["WCF::消息收取"], "summary": "获取OCR识别结果", "parameters": [{"description": "获取OCR识别结果参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.GetOcrRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/receive_transfer": {"post": {"produces": ["application/json"], "tags": ["WCF::消息收取"], "summary": "接受转账", "parameters": [{"description": "接受转账参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.ReceiveTransferRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/refresh_pyq": {"post": {"produces": ["application/json"], "tags": ["WCF::其他"], "summary": "刷新朋友圈", "parameters": [{"description": "刷新朋友圈参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.RefreshPyqRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/revoke_msg": {"post": {"produces": ["application/json"], "tags": ["WCF::消息发送"], "summary": "撤回消息", "parameters": [{"description": "撤回消息参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.RevokeMsgRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/self_info": {"post": {"produces": ["application/json"], "tags": ["WCF::其他"], "summary": "获取登录账号个人信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.UserInfoPayload"}}}}}, "/wcf/self_wxid": {"post": {"produces": ["application/json"], "tags": ["WCF::其他"], "summary": "获取登录账号wxid", "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/wcf/send_file": {"post": {"produces": ["application/json"], "tags": ["WCF::消息发送"], "summary": "发送文件消息", "parameters": [{"description": "发送文件消息参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.SendFileRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/send_img": {"post": {"produces": ["application/json"], "tags": ["WCF::消息发送"], "summary": "发送图片消息", "parameters": [{"description": "发送图片消息参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.SendFileRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/send_pat_msg": {"post": {"produces": ["application/json"], "tags": ["WCF::消息发送"], "summary": "拍一拍群友", "parameters": [{"description": "拍一拍群友参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.SendPatMsgRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/send_rich_text": {"post": {"produces": ["application/json"], "tags": ["WCF::消息发送"], "summary": "发送卡片消息", "parameters": [{"description": "发送卡片消息参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.SendRichTextRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/send_txt": {"post": {"produces": ["application/json"], "tags": ["WCF::消息发送"], "summary": "发送文本消息", "parameters": [{"description": "发送文本消息参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.SendTxtRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.CommonPayload"}}}}}, "/wcf/socket_receiver": {"get": {"produces": ["application/json"], "tags": ["WCF::消息推送"], "summary": "推送消息到Socket", "responses": {"101": {"description": "Switching Protocols 响应", "schema": {"type": "string"}}}}}, "/wcf/user_info": {"post": {"produces": ["application/json"], "tags": ["WCF::联系人管理"], "summary": "根据wxid获取个人信息", "parameters": [{"description": "根据wxid获取个人信息参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/wcfrest.GetInfoByWxidRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/wcfrest.ContactPayload"}}}}}}, "definitions": {"aichat.MsgHistory": {"type": "object", "properties": {"content": {"description": "消息内容", "type": "string"}, "role": {"description": "user,assistant", "type": "string"}}}, "aichat.UserConfig": {"type": "object", "properties": {"endpoint": {"description": "接口地址", "type": "string"}, "family": {"description": "族类描述", "type": "string"}, "model": {"description": "模型", "type": "string"}, "msg_history_max": {"description": "消息记录最大条数", "type": "integer"}, "msg_historys": {"description": "消息历史记录", "type": "array", "items": {"$ref": "#/definitions/aichat.MsgHistory"}}, "provider": {"description": "供应商", "type": "string"}, "role_context": {"description": "角色设定", "type": "string"}, "secret": {"description": "密钥格式\n科大讯飞 APP-ID,API-KEY,API-SECRET\n文心一言 API-KEY,API-SECRET\n腾讯混元 APP-ID,API-KEY,API-SECRET\n阿里百炼（通义千问） APP-ID,AGENT-KEY,ACCESS_KEY_ID,ACCESS_KEY_SECRET\n其他服务商 API-KEY", "type": "string"}}}, "chatroom.CreateParam": {"type": "object", "required": ["roomid"], "properties": {"join_argot": {"type": "string"}, "level": {"type": "integer"}, "model_context": {"type": "string"}, "model_default": {"type": "string"}, "model_history": {"type": "integer"}, "name": {"type": "string"}, "pat_return": {"type": "string"}, "rd": {"type": "integer"}, "remark": {"type": "string"}, "revoke_msg": {"type": "string"}, "roomid": {"type": "string"}, "welcome_msg": {"type": "string"}}}, "chatroom.DeleteParam": {"type": "object", "properties": {"rd": {"type": "integer"}, "roomid": {"type": "string"}}}, "chatroom.FetchAllParam": {"type": "object", "properties": {"level": {"type": "integer"}}}, "chatroom.FetchParam": {"type": "object", "properties": {"rd": {"type": "integer"}, "roomid": {"type": "string"}}}, "chatroom.UpdateParam": {"type": "object", "required": ["roomid"], "properties": {"join_argot": {"type": "string"}, "level": {"type": "integer"}, "model_context": {"type": "string"}, "model_default": {"type": "string"}, "model_history": {"type": "integer"}, "name": {"type": "string"}, "pat_return": {"type": "string"}, "rd": {"type": "integer"}, "remark": {"type": "string"}, "revoke_msg": {"type": "string"}, "roomid": {"type": "string"}, "welcome_msg": {"type": "string"}}}, "cronjob.CreateParam": {"type": "object", "required": ["content", "day_of_month", "day_of_week", "directory", "hour", "minute", "month", "name", "second", "timeout", "type"], "properties": {"content": {"type": "string"}, "day_of_month": {"type": "string"}, "day_of_week": {"type": "string"}, "deliver": {"type": "string"}, "directory": {"type": "string"}, "entry_id": {"type": "integer"}, "hour": {"type": "string"}, "minute": {"type": "string"}, "month": {"type": "string"}, "name": {"type": "string"}, "rd": {"type": "integer"}, "second": {"type": "string"}, "timeout": {"type": "integer"}, "type": {"type": "string"}}}, "cronjob.DeleteParam": {"type": "object", "properties": {"entry_id": {"type": "integer"}, "rd": {"type": "integer"}}}, "cronjob.FetchAllParam": {"type": "object", "properties": {"type": {"type": "string"}}}, "cronjob.FetchParam": {"type": "object", "properties": {"entry_id": {"type": "integer"}, "rd": {"type": "integer"}}}, "cronjob.UpdateParam": {"type": "object", "required": ["content", "day_of_month", "day_of_week", "directory", "hour", "minute", "month", "name", "second", "timeout", "type"], "properties": {"content": {"type": "string"}, "day_of_month": {"type": "string"}, "day_of_week": {"type": "string"}, "deliver": {"type": "string"}, "directory": {"type": "string"}, "entry_id": {"type": "integer"}, "hour": {"type": "string"}, "minute": {"type": "string"}, "month": {"type": "string"}, "name": {"type": "string"}, "rd": {"type": "integer"}, "second": {"type": "string"}, "timeout": {"type": "integer"}, "type": {"type": "string"}}}, "keyword.CreateParam": {"type": "object", "required": ["group", "phrase", "roomid"], "properties": {"group": {"type": "string"}, "level": {"type": "integer"}, "phrase": {"type": "string"}, "rd": {"type": "integer"}, "remark": {"type": "string"}, "roomid": {"type": "string"}, "target": {"type": "string"}}}, "keyword.DeleteParam": {"type": "object", "properties": {"group": {"type": "string"}, "phrase": {"type": "string"}, "rd": {"type": "integer"}, "roomid": {"type": "string"}, "target": {"type": "string"}}}, "keyword.FetchAllParam": {"type": "object", "properties": {"group": {"type": "string"}, "roomid": {"type": "string"}}}, "keyword.FetchParam": {"type": "object", "properties": {"group": {"type": "string"}, "phrase": {"type": "string"}, "rd": {"type": "integer"}, "roomid": {"type": "string"}, "target": {"type": "string"}}}, "keyword.UpdateParam": {"type": "object", "required": ["group", "phrase", "roomid"], "properties": {"group": {"type": "string"}, "level": {"type": "integer"}, "phrase": {"type": "string"}, "rd": {"type": "integer"}, "remark": {"type": "string"}, "roomid": {"type": "string"}, "target": {"type": "string"}}}, "llmodel.CreateParam": {"type": "object", "required": ["family", "mid", "model", "provider", "secret"], "properties": {"endpoint": {"type": "string"}, "family": {"type": "string"}, "level": {"type": "integer"}, "mid": {"type": "string"}, "model": {"type": "string"}, "provider": {"type": "string"}, "rd": {"type": "integer"}, "secret": {"type": "string"}}}, "llmodel.DeleteParam": {"type": "object", "properties": {"mid": {"type": "string"}, "rd": {"type": "integer"}}}, "llmodel.FetchAllParam": {"type": "object", "properties": {"family": {"type": "string"}, "level": {"type": "integer"}, "model": {"type": "string"}, "provider": {"type": "string"}}}, "llmodel.FetchParam": {"type": "object", "properties": {"mid": {"type": "string"}, "rd": {"type": "integer"}}}, "llmodel.UpdateParam": {"type": "object", "required": ["family", "mid", "model", "provider", "secret"], "properties": {"endpoint": {"type": "string"}, "family": {"type": "string"}, "level": {"type": "integer"}, "mid": {"type": "string"}, "model": {"type": "string"}, "provider": {"type": "string"}, "rd": {"type": "integer"}, "secret": {"type": "string"}}}, "plugin.CronjobPlugin": {"type": "object", "properties": {"config": {"$ref": "#/definitions/cronjob.CreateParam"}, "error": {"type": "string"}, "file": {"type": "string"}}}, "plugin.KeywordPlugin": {"type": "object", "properties": {"config": {"$ref": "#/definitions/keyword.CreateParam"}, "error": {"type": "string"}, "file": {"type": "string"}}}, "profile.CreateParam": {"type": "object", "required": ["wxid"], "properties": {"ai_model": {"type": "string"}, "ban_expire": {"type": "integer"}, "level": {"type": "integer"}, "rd": {"type": "integer"}, "remark": {"type": "string"}, "roomid": {"type": "string"}, "wxid": {"type": "string"}}}, "profile.DeleteParam": {"type": "object", "properties": {"rd": {"type": "integer"}, "roomid": {"type": "string"}, "wxid": {"type": "string"}}}, "profile.FetchAllParam": {"type": "object", "properties": {"level": {"type": "integer"}, "roomid": {"type": "string"}, "wxid": {"type": "string"}}}, "profile.FetchParam": {"type": "object", "properties": {"rd": {"type": "integer"}, "roomid": {"type": "string"}, "wxid": {"type": "string"}}}, "profile.UpdateParam": {"type": "object", "required": ["wxid"], "properties": {"ai_model": {"type": "string"}, "ban_expire": {"type": "integer"}, "level": {"type": "integer"}, "rd": {"type": "integer"}, "remark": {"type": "string"}, "roomid": {"type": "string"}, "wxid": {"type": "string"}}}, "setting.CreateParam": {"type": "object", "required": ["name"], "properties": {"group": {"type": "string"}, "name": {"type": "string"}, "rd": {"type": "integer"}, "remark": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "setting.DeleteParam": {"type": "object", "properties": {"name": {"type": "string"}, "rd": {"type": "integer"}}}, "setting.FetchAllParam": {"type": "object", "properties": {"group": {"type": "string"}}}, "setting.FetchParam": {"type": "object", "properties": {"name": {"type": "string"}, "rd": {"type": "integer"}}}, "setting.UpdateParam": {"type": "object", "required": ["name"], "properties": {"group": {"type": "string"}, "name": {"type": "string"}, "rd": {"type": "integer"}, "remark": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "sundry.AiChatParam": {"type": "object", "properties": {"message": {"type": "string"}, "wxid": {"type": "string"}}}, "sundry.Handler": {"type": "object", "properties": {"command": {"description": "指令", "type": "string"}, "describe": {"description": "指令的描述信息", "type": "string"}, "level": {"description": "0:不限制 7:群管理 9:创始人", "type": "integer"}, "order": {"description": "排序，越小越靠前", "type": "integer"}, "roomid": {"description": "使用场景 [*:所有,-:私聊,+:群聊,其他:群聊]", "type": "string"}}}, "sundry.HandlersParam": {"type": "object", "properties": {"reset": {"description": "是否重置机器人指令", "type": "boolean"}}}, "sundry.SystemVersion": {"type": "object", "properties": {"build_version": {"description": "系统编译版本", "type": "string"}, "version": {"description": "系统版本", "type": "string"}, "wcf_version": {"description": "wc<PERSON><PERSON>本", "type": "string"}, "wechat_version": {"description": "wechat <PERSON>", "type": "string"}}}, "tables.Chatroom": {"type": "object", "properties": {"created_at": {"description": "创建时间戳", "type": "integer"}, "join_argot": {"description": "加群指令", "type": "string"}, "level": {"description": "等级 [1:待验证, 2:已注册]", "type": "integer"}, "model_context": {"description": "定义模型扮演的身份", "type": "string"}, "model_default": {"description": "定义默认模型", "type": "string"}, "model_history": {"description": "定义历史消息数量", "type": "integer"}, "name": {"description": "群聊名称", "type": "string"}, "pat_return": {"description": "响应拍拍我", "type": "string"}, "rd": {"description": "主键", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "revoke_msg": {"description": "防撤回消息", "type": "string"}, "roomid": {"description": "群聊 id", "type": "string"}, "updated_at": {"description": "最后更新时间戳", "type": "integer"}, "welcome_msg": {"description": "欢迎消息", "type": "string"}}}, "tables.Cronjob": {"type": "object", "properties": {"content": {"description": "命令内容", "type": "string"}, "created_at": {"description": "创建时间戳", "type": "integer"}, "day_of_month": {"description": "日", "type": "string"}, "day_of_week": {"description": "周", "type": "string"}, "deliver": {"description": "执行结果交付方式", "type": "string"}, "directory": {"description": "工作目录", "type": "string"}, "entry_id": {"description": "任务运行时 Id", "type": "integer"}, "hour": {"description": "时", "type": "string"}, "minute": {"description": "分", "type": "string"}, "month": {"description": "月", "type": "string"}, "name": {"description": "名称", "type": "string"}, "rd": {"description": "主键", "type": "integer"}, "second": {"description": "秒", "type": "string"}, "timeout": {"description": "超时时间（秒）", "type": "integer"}, "type": {"description": "命令类型 [CMD, POWERSHELL, SHELL]", "type": "string"}, "updated_at": {"description": "最后更新时间戳", "type": "integer"}}}, "tables.Keyword": {"type": "object", "properties": {"created_at": {"description": "创建时间戳", "type": "integer"}, "group": {"description": "分组", "type": "string"}, "level": {"description": "等级", "type": "integer"}, "phrase": {"description": "短语", "type": "string"}, "rd": {"description": "主键", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "roomid": {"description": "群聊 id", "type": "string"}, "target": {"description": "目标", "type": "string"}, "updated_at": {"description": "最后更新时间戳", "type": "integer"}}}, "tables.LLModel": {"type": "object", "properties": {"created_at": {"description": "创建时间戳", "type": "integer"}, "endpoint": {"description": "仅 google 和 openai 支持自定义", "type": "string"}, "family": {"description": "模型家族，用于在指令中描述模型类型", "type": "string"}, "level": {"description": "等级，用于限制用户访问", "type": "integer"}, "mid": {"description": "模型 Id，用于生成模型切换指令", "type": "string"}, "model": {"description": "模型，必须和服务商对应", "type": "string"}, "provider": {"description": "服务商 [google, openai, xunfei, baidu, tencent]", "type": "string"}, "rd": {"description": "主键", "type": "integer"}, "secret": {"description": "密钥，必须和服务商对应，多个字段按指定顺序填写并用逗号隔开", "type": "string"}, "updated_at": {"description": "最后更新时间戳", "type": "integer"}}}, "tables.Profile": {"type": "object", "properties": {"ai_model": {"description": "会话模型", "type": "string"}, "ban_expire": {"description": "拉黑截止时间", "type": "integer"}, "created_at": {"description": "创建时间戳", "type": "integer"}, "level": {"description": "等级 [1:待验证, 2:已注册, 7:管理员, 9:创始人]", "type": "integer"}, "rd": {"description": "主键", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "roomid": {"description": "群聊 id", "type": "string"}, "updated_at": {"description": "最后更新时间戳", "type": "integer"}, "wxid": {"description": "微信 id", "type": "string"}}}, "tables.Setting": {"type": "object", "properties": {"created_at": {"description": "创建时间戳", "type": "integer"}, "group": {"description": "分组", "type": "string"}, "name": {"description": "键", "type": "string"}, "rd": {"description": "主键", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "title": {"description": "标题", "type": "string"}, "type": {"description": "类型", "type": "string"}, "updated_at": {"description": "最后更新时间戳", "type": "integer"}, "value": {"description": "值", "type": "string"}}}, "tables.Webhook": {"type": "object", "properties": {"created_at": {"description": "创建时间戳", "type": "integer"}, "rd": {"description": "主键", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "target_id": {"description": "群聊/私聊ID", "type": "string"}, "token": {"description": "webhook 标识（GUID）", "type": "string"}, "updated_at": {"description": "最后更新时间戳", "type": "integer"}}}, "wcfrest.AcceptNewFriendRequest": {"type": "object", "properties": {"scene": {"description": "添加方式：17 名片，30 扫码", "type": "integer"}, "v3": {"description": "加密的用户名", "type": "string"}, "v4": {"description": "验证信息 Ticket", "type": "string"}}}, "wcfrest.AvatarPayload": {"type": "object", "properties": {"big_head_img_url": {"description": "大头像 url", "type": "string"}, "small_head_img_url": {"description": "小头像 url", "type": "string"}, "usr_name": {"description": "用户 id", "type": "string"}}}, "wcfrest.ChatroomMembersRequest": {"type": "object", "properties": {"roomid": {"description": "群聊 id", "type": "string"}, "wxids": {"description": "用户 id 列表", "type": "array", "items": {"type": "string"}}}}, "wcfrest.CommonPayload": {"type": "object", "properties": {"error": {"description": "错误信息"}, "result": {"description": "返回结果", "type": "string"}, "success": {"description": "是否成功", "type": "boolean"}}}, "wcfrest.ContactPayload": {"type": "object", "properties": {"city": {"description": "城市", "type": "string"}, "code": {"description": "微信号", "type": "string"}, "country": {"description": "国家", "type": "string"}, "gender": {"description": "性别", "type": "integer"}, "name": {"description": "昵称", "type": "string"}, "province": {"description": "省/州", "type": "string"}, "remark": {"description": "备注", "type": "string"}, "wxid": {"description": "用户 id", "type": "string"}}}, "wcfrest.DbSqlQueryRequest": {"type": "object", "properties": {"db": {"description": "数据库名称", "type": "string"}, "sql": {"description": "待执行的 SQL", "type": "string"}}}, "wcfrest.DbTablePayload": {"type": "object", "properties": {"name": {"description": "表名", "type": "string"}, "sql": {"description": "建表 SQL", "type": "string"}}}, "wcfrest.DownloadAttachRequest": {"type": "object", "properties": {"extra": {"description": "消息中的 extra 字段", "type": "string"}, "msgid": {"description": "消息 id", "type": "integer"}, "thumb": {"description": "消息中的 thumb 字段", "type": "string"}}}, "wcfrest.DownloadImageRequest": {"type": "object", "properties": {"dir": {"description": "存储路径", "type": "string"}, "extra": {"description": "消息中的 extra 字段", "type": "string"}, "msgid": {"description": "消息 id", "type": "integer"}, "timeout": {"description": "超时重试次数", "type": "integer"}}}, "wcfrest.ForwardMsgRequest": {"type": "object", "properties": {"id": {"description": "待转发消息 id", "type": "integer"}, "receiver": {"description": "转发接收人或群的 id 列表", "type": "array", "items": {"type": "string"}}}}, "wcfrest.GetAliasInChatRoomRequest": {"type": "object", "properties": {"roomid": {"description": "群聊 id", "type": "string"}, "wxid": {"description": "用户 id", "type": "string"}}}, "wcfrest.GetAudioMsgRequest": {"type": "object", "properties": {"msgid": {"description": "消息 id", "type": "integer"}, "path": {"description": "存储路径", "type": "string"}, "timeout": {"description": "超时重试次数", "type": "integer"}}}, "wcfrest.GetAvatarsRequest": {"type": "object", "properties": {"wxids": {"description": "用户 id 列表", "type": "array", "items": {"type": "string"}}}}, "wcfrest.GetChatRoomMembersRequest": {"type": "object", "properties": {"roomid": {"description": "群聊 id", "type": "string"}}}, "wcfrest.GetDbTablesRequest": {"type": "object", "properties": {"db": {"description": "数据库名称", "type": "string"}}}, "wcfrest.GetInfoByWxidRequest": {"type": "object", "properties": {"wxid": {"description": "用户 id", "type": "string"}}}, "wcfrest.GetOcrRequest": {"type": "object", "properties": {"extra": {"description": "消息中的 extra 字段", "type": "string"}, "timeout": {"description": "超时重试次数", "type": "integer"}}}, "wcfrest.ReceiveTransferRequest": {"type": "object", "properties": {"taid": {"description": "Transaction id", "type": "string"}, "tfid": {"description": "转账id transferid", "type": "string"}, "wxid": {"description": "转账人", "type": "string"}}}, "wcfrest.ReceiverRequest": {"type": "object", "properties": {"url": {"description": "接收推送消息的 url", "type": "string"}}}, "wcfrest.RefreshPyqRequest": {"type": "object", "properties": {"id": {"description": "分页 id", "type": "integer"}}}, "wcfrest.RevokeMsgRequest": {"type": "object", "properties": {"msgid": {"description": "消息 id", "type": "integer"}}}, "wcfrest.SendFileRequest": {"type": "object", "properties": {"base64": {"description": "文件 base64 数据", "type": "string"}, "path": {"description": "文件路径，若提供 base64 则写入此路径", "type": "string"}, "receiver": {"description": "接收人或群的 id", "type": "string"}}}, "wcfrest.SendPatMsgRequest": {"type": "object", "properties": {"roomid": {"description": "群 id", "type": "string"}, "wxid": {"description": "用户 id", "type": "string"}}}, "wcfrest.SendRichTextRequest": {"type": "object", "properties": {"account": {"description": "填公众号 id 可以显示对应的头像（gh_ 开头的）", "type": "string"}, "digest": {"description": "摘要，三行", "type": "string"}, "name": {"description": "左下显示的名字", "type": "string"}, "receiver": {"description": "接收人或群的 id", "type": "string"}, "thumburl": {"description": "缩略图的链接", "type": "string"}, "title": {"description": "标题，最多两行", "type": "string"}, "url": {"description": "点击后跳转的链接", "type": "string"}}}, "wcfrest.SendTxtRequest": {"type": "object", "properties": {"aters": {"description": "需要 At 的用户 id 列表", "type": "array", "items": {"type": "string"}}, "msg": {"description": "消息内容", "type": "string"}, "receiver": {"description": "接收人或群的 id", "type": "string"}}}, "wcfrest.UserInfoPayload": {"type": "object", "properties": {"home": {"description": "文件/图片等父路径", "type": "string"}, "mobile": {"description": "手机号", "type": "string"}, "name": {"description": "昵称", "type": "string"}, "wxid": {"description": "用户 id", "type": "string"}}}, "webhook.CreateWebhookParam": {"type": "object", "required": ["target_id"], "properties": {"remark": {"type": "string"}, "target_id": {"type": "string"}}}, "webhook.DeleteWebhookParam": {"type": "object", "properties": {"rd": {"type": "integer"}, "target_id": {"type": "string"}, "token": {"type": "string"}}}, "webhook.FetchWebhookParam": {"type": "object", "properties": {"rd": {"type": "integer"}, "target_id": {"type": "string"}, "token": {"type": "string"}}}}}