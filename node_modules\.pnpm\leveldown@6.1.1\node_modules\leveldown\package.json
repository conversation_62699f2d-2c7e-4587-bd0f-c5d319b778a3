{"name": "leveldown", "version": "6.1.1", "description": "A low-level Node.js LevelDB binding", "license": "MIT", "main": "leveldown.js", "scripts": {"install": "node-gyp-build", "test": "standard && (nyc -s tape test/*-test.js | faucet) && nyc report", "test-gc": "node --expose-gc test/gc.js", "test-electron": "electron test/electron.js", "test-prebuild": "cross-env PREBUILDS_ONLY=1 npm t", "coverage": "nyc report -r lcovonly", "rebuild": "npm run install --build-from-source", "prebuild": "prebuildify -t 8.14.0 --napi --strip", "download-prebuilds": "prebuildify-ci download", "hallmark": "hallmark --fix", "dependency-check": "dependency-check --no-dev -i napi-macros . test/*.js", "prepublishOnly": "npm run dependency-check", "prebuild-linux-arm": "prebuildify-cross -i linux-armv6 -i linux-armv7 -i linux-arm64 -t 8.14.0 --napi --strip", "prebuild-android-arm": "prebuildify-cross -i android-armv7 -i android-arm64 -t 8.14.0 --napi --strip", "prebuild-linux-x64": "prebuildify-cross -i centos7-devtoolset7 -i alpine -t 8.14.0 --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify -t 8.14.0 --napi --strip --arch x64+arm64", "prebuild-win32-x86": "prebuildify -t 8.14.0 --napi --strip", "prebuild-win32-x64": "prebuildify -t 8.14.0 --napi --strip"}, "dependencies": {"abstract-leveldown": "^7.2.0", "napi-macros": "~2.0.0", "node-gyp-build": "^4.3.0"}, "devDependencies": {"async-each": "^1.0.3", "cross-env": "^7.0.3", "delayed": "^2.0.0", "dependency-check": "^4.1.0", "du": "^1.0.0", "electron": "^17.0.0", "faucet": "^0.0.1", "glob": "^7.1.3", "hallmark": "^4.0.0", "level-concat-iterator": "^3.0.0", "mkfiletree": "^2.0.0", "node-gyp": "^8.4.1", "nyc": "^15.0.0", "prebuildify": "^5.0.0", "prebuildify-ci": "^1.0.4", "prebuildify-cross": "^5.0.0", "readfiletree": "^1.0.0", "rimraf": "^3.0.0", "standard": "^16.0.3", "tape": "^5.0.1", "tempy": "^1.0.1"}, "gypfile": true, "repository": {"type": "git", "url": "https://github.com/Level/leveldown.git"}, "homepage": "https://github.com/Level/leveldown", "keywords": ["leveldb", "level"], "engines": {"node": ">=10.12.0"}}