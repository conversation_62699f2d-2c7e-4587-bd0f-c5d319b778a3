'use strict';

const pb_1 = require('google-protobuf');

function _interopNamespaceCompat(e) {
    if (e && typeof e === 'object' && 'default' in e) return e;
    const n = Object.create(null);
    if (e) {
        for (const k in e) {
            n[k] = e[k];
        }
    }
    n.default = e;
    return n;
}

const pb_1__namespace = /*#__PURE__*/_interopNamespaceCompat(pb_1);

var __accessCheck = (obj, member, msg) => {
  if (!member.has(obj))
    throw TypeError("Cannot " + msg);
};
var __privateGet = (obj, member, getter) => {
  __accessCheck(obj, member, "read from private field");
  return getter ? getter.call(obj) : member.get(obj);
};
var __privateAdd = (obj, member, value) => {
  if (member.has(obj))
    throw TypeError("Cannot add the same private member more than once");
  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
};
exports.wcf = void 0;
((wcf2) => {
  var _one_of_decls, _one_of_decls2, _one_of_decls3, _one_of_decls4, _one_of_decls5, _one_of_decls6, _one_of_decls7, _one_of_decls8, _one_of_decls9, _one_of_decls10, _one_of_decls11, _one_of_decls12, _one_of_decls13, _one_of_decls14, _one_of_decls15, _one_of_decls16, _one_of_decls17, _one_of_decls18, _one_of_decls19, _one_of_decls20, _one_of_decls21, _one_of_decls22, _one_of_decls23, _one_of_decls24, _one_of_decls25, _one_of_decls26, _one_of_decls27, _one_of_decls28, _one_of_decls29;
  ((Functions2) => {
    Functions2[Functions2["FUNC_RESERVED"] = 0] = "FUNC_RESERVED";
    Functions2[Functions2["FUNC_IS_LOGIN"] = 1] = "FUNC_IS_LOGIN";
    Functions2[Functions2["FUNC_GET_SELF_WXID"] = 16] = "FUNC_GET_SELF_WXID";
    Functions2[Functions2["FUNC_GET_MSG_TYPES"] = 17] = "FUNC_GET_MSG_TYPES";
    Functions2[Functions2["FUNC_GET_CONTACTS"] = 18] = "FUNC_GET_CONTACTS";
    Functions2[Functions2["FUNC_GET_DB_NAMES"] = 19] = "FUNC_GET_DB_NAMES";
    Functions2[Functions2["FUNC_GET_DB_TABLES"] = 20] = "FUNC_GET_DB_TABLES";
    Functions2[Functions2["FUNC_GET_USER_INFO"] = 21] = "FUNC_GET_USER_INFO";
    Functions2[Functions2["FUNC_GET_AUDIO_MSG"] = 22] = "FUNC_GET_AUDIO_MSG";
    Functions2[Functions2["FUNC_SEND_TXT"] = 32] = "FUNC_SEND_TXT";
    Functions2[Functions2["FUNC_SEND_IMG"] = 33] = "FUNC_SEND_IMG";
    Functions2[Functions2["FUNC_SEND_FILE"] = 34] = "FUNC_SEND_FILE";
    Functions2[Functions2["FUNC_SEND_XML"] = 35] = "FUNC_SEND_XML";
    Functions2[Functions2["FUNC_SEND_EMOTION"] = 36] = "FUNC_SEND_EMOTION";
    Functions2[Functions2["FUNC_SEND_RICH_TXT"] = 37] = "FUNC_SEND_RICH_TXT";
    Functions2[Functions2["FUNC_SEND_PAT_MSG"] = 38] = "FUNC_SEND_PAT_MSG";
    Functions2[Functions2["FUNC_FORWARD_MSG"] = 39] = "FUNC_FORWARD_MSG";
    Functions2[Functions2["FUNC_ENABLE_RECV_TXT"] = 48] = "FUNC_ENABLE_RECV_TXT";
    Functions2[Functions2["FUNC_DISABLE_RECV_TXT"] = 64] = "FUNC_DISABLE_RECV_TXT";
    Functions2[Functions2["FUNC_EXEC_DB_QUERY"] = 80] = "FUNC_EXEC_DB_QUERY";
    Functions2[Functions2["FUNC_ACCEPT_FRIEND"] = 81] = "FUNC_ACCEPT_FRIEND";
    Functions2[Functions2["FUNC_RECV_TRANSFER"] = 82] = "FUNC_RECV_TRANSFER";
    Functions2[Functions2["FUNC_REFRESH_PYQ"] = 83] = "FUNC_REFRESH_PYQ";
    Functions2[Functions2["FUNC_DOWNLOAD_ATTACH"] = 84] = "FUNC_DOWNLOAD_ATTACH";
    Functions2[Functions2["FUNC_GET_CONTACT_INFO"] = 85] = "FUNC_GET_CONTACT_INFO";
    Functions2[Functions2["FUNC_REVOKE_MSG"] = 86] = "FUNC_REVOKE_MSG";
    Functions2[Functions2["FUNC_REFRESH_QRCODE"] = 87] = "FUNC_REFRESH_QRCODE";
    Functions2[Functions2["FUNC_DECRYPT_IMAGE"] = 96] = "FUNC_DECRYPT_IMAGE";
    Functions2[Functions2["FUNC_EXEC_OCR"] = 97] = "FUNC_EXEC_OCR";
    Functions2[Functions2["FUNC_ADD_ROOM_MEMBERS"] = 112] = "FUNC_ADD_ROOM_MEMBERS";
    Functions2[Functions2["FUNC_DEL_ROOM_MEMBERS"] = 113] = "FUNC_DEL_ROOM_MEMBERS";
    Functions2[Functions2["FUNC_INV_ROOM_MEMBERS"] = 114] = "FUNC_INV_ROOM_MEMBERS";
  })(wcf2.Functions || (wcf2.Functions = {}));
  const _Request = class _Request extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls, [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]]);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("func" in data && data.func != void 0) {
          this.func = data.func;
        }
        if ("empty" in data && data.empty != void 0) {
          this.empty = data.empty;
        }
        if ("str" in data && data.str != void 0) {
          this.str = data.str;
        }
        if ("txt" in data && data.txt != void 0) {
          this.txt = data.txt;
        }
        if ("file" in data && data.file != void 0) {
          this.file = data.file;
        }
        if ("query" in data && data.query != void 0) {
          this.query = data.query;
        }
        if ("v" in data && data.v != void 0) {
          this.v = data.v;
        }
        if ("m" in data && data.m != void 0) {
          this.m = data.m;
        }
        if ("xml" in data && data.xml != void 0) {
          this.xml = data.xml;
        }
        if ("dec" in data && data.dec != void 0) {
          this.dec = data.dec;
        }
        if ("tf" in data && data.tf != void 0) {
          this.tf = data.tf;
        }
        if ("ui64" in data && data.ui64 != void 0) {
          this.ui64 = data.ui64;
        }
        if ("flag" in data && data.flag != void 0) {
          this.flag = data.flag;
        }
        if ("att" in data && data.att != void 0) {
          this.att = data.att;
        }
        if ("am" in data && data.am != void 0) {
          this.am = data.am;
        }
        if ("rt" in data && data.rt != void 0) {
          this.rt = data.rt;
        }
        if ("pm" in data && data.pm != void 0) {
          this.pm = data.pm;
        }
        if ("fm" in data && data.fm != void 0) {
          this.fm = data.fm;
        }
      }
    }
    get func() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, 0 /* FUNC_RESERVED */);
    }
    set func(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get empty() {
      return pb_1__namespace.Message.getWrapperField(this, Empty, 2);
    }
    set empty(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 2, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_empty() {
      return pb_1__namespace.Message.getField(this, 2) != null;
    }
    get str() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set str(value) {
      pb_1__namespace.Message.setOneofField(this, 3, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_str() {
      return pb_1__namespace.Message.getField(this, 3) != null;
    }
    get txt() {
      return pb_1__namespace.Message.getWrapperField(this, TextMsg, 4);
    }
    set txt(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 4, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_txt() {
      return pb_1__namespace.Message.getField(this, 4) != null;
    }
    get file() {
      return pb_1__namespace.Message.getWrapperField(this, PathMsg, 5);
    }
    set file(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 5, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_file() {
      return pb_1__namespace.Message.getField(this, 5) != null;
    }
    get query() {
      return pb_1__namespace.Message.getWrapperField(this, DbQuery, 6);
    }
    set query(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 6, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_query() {
      return pb_1__namespace.Message.getField(this, 6) != null;
    }
    get v() {
      return pb_1__namespace.Message.getWrapperField(this, Verification, 7);
    }
    set v(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 7, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_v() {
      return pb_1__namespace.Message.getField(this, 7) != null;
    }
    get m() {
      return pb_1__namespace.Message.getWrapperField(this, MemberMgmt, 8);
    }
    set m(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 8, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_m() {
      return pb_1__namespace.Message.getField(this, 8) != null;
    }
    get xml() {
      return pb_1__namespace.Message.getWrapperField(this, XmlMsg, 9);
    }
    set xml(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 9, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_xml() {
      return pb_1__namespace.Message.getField(this, 9) != null;
    }
    get dec() {
      return pb_1__namespace.Message.getWrapperField(this, DecPath, 10);
    }
    set dec(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 10, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_dec() {
      return pb_1__namespace.Message.getField(this, 10) != null;
    }
    get tf() {
      return pb_1__namespace.Message.getWrapperField(this, Transfer, 11);
    }
    set tf(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 11, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_tf() {
      return pb_1__namespace.Message.getField(this, 11) != null;
    }
    get ui64() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 12, "0");
    }
    set ui64(value) {
      pb_1__namespace.Message.setOneofField(this, 12, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_ui64() {
      return pb_1__namespace.Message.getField(this, 12) != null;
    }
    get flag() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 13, false);
    }
    set flag(value) {
      pb_1__namespace.Message.setOneofField(this, 13, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_flag() {
      return pb_1__namespace.Message.getField(this, 13) != null;
    }
    get att() {
      return pb_1__namespace.Message.getWrapperField(this, AttachMsg, 14);
    }
    set att(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 14, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_att() {
      return pb_1__namespace.Message.getField(this, 14) != null;
    }
    get am() {
      return pb_1__namespace.Message.getWrapperField(this, AudioMsg, 15);
    }
    set am(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 15, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_am() {
      return pb_1__namespace.Message.getField(this, 15) != null;
    }
    get rt() {
      return pb_1__namespace.Message.getWrapperField(this, RichText, 16);
    }
    set rt(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 16, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_rt() {
      return pb_1__namespace.Message.getField(this, 16) != null;
    }
    get pm() {
      return pb_1__namespace.Message.getWrapperField(this, PatMsg, 17);
    }
    set pm(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 17, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_pm() {
      return pb_1__namespace.Message.getField(this, 17) != null;
    }
    get fm() {
      return pb_1__namespace.Message.getWrapperField(this, ForwardMsg, 18);
    }
    set fm(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 18, __privateGet(this, _one_of_decls)[0], value);
    }
    get has_fm() {
      return pb_1__namespace.Message.getField(this, 18) != null;
    }
    get msg() {
      const cases = {
        0: "none",
        2: "empty",
        3: "str",
        4: "txt",
        5: "file",
        6: "query",
        7: "v",
        8: "m",
        9: "xml",
        10: "dec",
        11: "tf",
        12: "ui64",
        13: "flag",
        14: "att",
        15: "am",
        16: "rt",
        17: "pm",
        18: "fm"
      };
      return cases[pb_1__namespace.Message.computeOneofCase(this, [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18])];
    }
    static fromObject(data) {
      const message = new _Request({});
      if (data.func != null) {
        message.func = data.func;
      }
      if (data.empty != null) {
        message.empty = Empty.fromObject(data.empty);
      }
      if (data.str != null) {
        message.str = data.str;
      }
      if (data.txt != null) {
        message.txt = TextMsg.fromObject(data.txt);
      }
      if (data.file != null) {
        message.file = PathMsg.fromObject(data.file);
      }
      if (data.query != null) {
        message.query = DbQuery.fromObject(data.query);
      }
      if (data.v != null) {
        message.v = Verification.fromObject(data.v);
      }
      if (data.m != null) {
        message.m = MemberMgmt.fromObject(data.m);
      }
      if (data.xml != null) {
        message.xml = XmlMsg.fromObject(data.xml);
      }
      if (data.dec != null) {
        message.dec = DecPath.fromObject(data.dec);
      }
      if (data.tf != null) {
        message.tf = Transfer.fromObject(data.tf);
      }
      if (data.ui64 != null) {
        message.ui64 = data.ui64;
      }
      if (data.flag != null) {
        message.flag = data.flag;
      }
      if (data.att != null) {
        message.att = AttachMsg.fromObject(data.att);
      }
      if (data.am != null) {
        message.am = AudioMsg.fromObject(data.am);
      }
      if (data.rt != null) {
        message.rt = RichText.fromObject(data.rt);
      }
      if (data.pm != null) {
        message.pm = PatMsg.fromObject(data.pm);
      }
      if (data.fm != null) {
        message.fm = ForwardMsg.fromObject(data.fm);
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.func != null) {
        data.func = this.func;
      }
      if (this.empty != null) {
        data.empty = this.empty.toObject();
      }
      if (this.str != null) {
        data.str = this.str;
      }
      if (this.txt != null) {
        data.txt = this.txt.toObject();
      }
      if (this.file != null) {
        data.file = this.file.toObject();
      }
      if (this.query != null) {
        data.query = this.query.toObject();
      }
      if (this.v != null) {
        data.v = this.v.toObject();
      }
      if (this.m != null) {
        data.m = this.m.toObject();
      }
      if (this.xml != null) {
        data.xml = this.xml.toObject();
      }
      if (this.dec != null) {
        data.dec = this.dec.toObject();
      }
      if (this.tf != null) {
        data.tf = this.tf.toObject();
      }
      if (this.ui64 != null) {
        data.ui64 = this.ui64;
      }
      if (this.flag != null) {
        data.flag = this.flag;
      }
      if (this.att != null) {
        data.att = this.att.toObject();
      }
      if (this.am != null) {
        data.am = this.am.toObject();
      }
      if (this.rt != null) {
        data.rt = this.rt.toObject();
      }
      if (this.pm != null) {
        data.pm = this.pm.toObject();
      }
      if (this.fm != null) {
        data.fm = this.fm.toObject();
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.func != 0 /* FUNC_RESERVED */)
        writer.writeEnum(1, this.func);
      if (this.has_empty)
        writer.writeMessage(2, this.empty, () => this.empty.serialize(writer));
      if (this.has_str)
        writer.writeString(3, this.str);
      if (this.has_txt)
        writer.writeMessage(4, this.txt, () => this.txt.serialize(writer));
      if (this.has_file)
        writer.writeMessage(5, this.file, () => this.file.serialize(writer));
      if (this.has_query)
        writer.writeMessage(6, this.query, () => this.query.serialize(writer));
      if (this.has_v)
        writer.writeMessage(7, this.v, () => this.v.serialize(writer));
      if (this.has_m)
        writer.writeMessage(8, this.m, () => this.m.serialize(writer));
      if (this.has_xml)
        writer.writeMessage(9, this.xml, () => this.xml.serialize(writer));
      if (this.has_dec)
        writer.writeMessage(10, this.dec, () => this.dec.serialize(writer));
      if (this.has_tf)
        writer.writeMessage(11, this.tf, () => this.tf.serialize(writer));
      if (this.has_ui64)
        writer.writeUint64String(12, this.ui64);
      if (this.has_flag)
        writer.writeBool(13, this.flag);
      if (this.has_att)
        writer.writeMessage(14, this.att, () => this.att.serialize(writer));
      if (this.has_am)
        writer.writeMessage(15, this.am, () => this.am.serialize(writer));
      if (this.has_rt)
        writer.writeMessage(16, this.rt, () => this.rt.serialize(writer));
      if (this.has_pm)
        writer.writeMessage(17, this.pm, () => this.pm.serialize(writer));
      if (this.has_fm)
        writer.writeMessage(18, this.fm, () => this.fm.serialize(writer));
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _Request();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.func = reader.readEnum();
            break;
          case 2:
            reader.readMessage(message.empty, () => message.empty = Empty.deserialize(reader));
            break;
          case 3:
            message.str = reader.readString();
            break;
          case 4:
            reader.readMessage(message.txt, () => message.txt = TextMsg.deserialize(reader));
            break;
          case 5:
            reader.readMessage(message.file, () => message.file = PathMsg.deserialize(reader));
            break;
          case 6:
            reader.readMessage(message.query, () => message.query = DbQuery.deserialize(reader));
            break;
          case 7:
            reader.readMessage(message.v, () => message.v = Verification.deserialize(reader));
            break;
          case 8:
            reader.readMessage(message.m, () => message.m = MemberMgmt.deserialize(reader));
            break;
          case 9:
            reader.readMessage(message.xml, () => message.xml = XmlMsg.deserialize(reader));
            break;
          case 10:
            reader.readMessage(message.dec, () => message.dec = DecPath.deserialize(reader));
            break;
          case 11:
            reader.readMessage(message.tf, () => message.tf = Transfer.deserialize(reader));
            break;
          case 12:
            message.ui64 = reader.readUint64String();
            break;
          case 13:
            message.flag = reader.readBool();
            break;
          case 14:
            reader.readMessage(message.att, () => message.att = AttachMsg.deserialize(reader));
            break;
          case 15:
            reader.readMessage(message.am, () => message.am = AudioMsg.deserialize(reader));
            break;
          case 16:
            reader.readMessage(message.rt, () => message.rt = RichText.deserialize(reader));
            break;
          case 17:
            reader.readMessage(message.pm, () => message.pm = PatMsg.deserialize(reader));
            break;
          case 18:
            reader.readMessage(message.fm, () => message.fm = ForwardMsg.deserialize(reader));
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _Request.deserialize(bytes);
    }
  };
  _one_of_decls = new WeakMap();
  wcf2.Request = _Request;
  const _Response = class _Response extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls2, [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11]]);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls2));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("func" in data && data.func != void 0) {
          this.func = data.func;
        }
        if ("status" in data && data.status != void 0) {
          this.status = data.status;
        }
        if ("str" in data && data.str != void 0) {
          this.str = data.str;
        }
        if ("wxmsg" in data && data.wxmsg != void 0) {
          this.wxmsg = data.wxmsg;
        }
        if ("types" in data && data.types != void 0) {
          this.types = data.types;
        }
        if ("contacts" in data && data.contacts != void 0) {
          this.contacts = data.contacts;
        }
        if ("dbs" in data && data.dbs != void 0) {
          this.dbs = data.dbs;
        }
        if ("tables" in data && data.tables != void 0) {
          this.tables = data.tables;
        }
        if ("rows" in data && data.rows != void 0) {
          this.rows = data.rows;
        }
        if ("ui" in data && data.ui != void 0) {
          this.ui = data.ui;
        }
        if ("ocr" in data && data.ocr != void 0) {
          this.ocr = data.ocr;
        }
      }
    }
    get func() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, 0 /* FUNC_RESERVED */);
    }
    set func(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get status() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, 0);
    }
    set status(value) {
      pb_1__namespace.Message.setOneofField(this, 2, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_status() {
      return pb_1__namespace.Message.getField(this, 2) != null;
    }
    get str() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set str(value) {
      pb_1__namespace.Message.setOneofField(this, 3, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_str() {
      return pb_1__namespace.Message.getField(this, 3) != null;
    }
    get wxmsg() {
      return pb_1__namespace.Message.getWrapperField(this, WxMsg, 4);
    }
    set wxmsg(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 4, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_wxmsg() {
      return pb_1__namespace.Message.getField(this, 4) != null;
    }
    get types() {
      return pb_1__namespace.Message.getWrapperField(this, MsgTypes, 5);
    }
    set types(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 5, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_types() {
      return pb_1__namespace.Message.getField(this, 5) != null;
    }
    get contacts() {
      return pb_1__namespace.Message.getWrapperField(this, RpcContacts, 6);
    }
    set contacts(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 6, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_contacts() {
      return pb_1__namespace.Message.getField(this, 6) != null;
    }
    get dbs() {
      return pb_1__namespace.Message.getWrapperField(this, DbNames, 7);
    }
    set dbs(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 7, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_dbs() {
      return pb_1__namespace.Message.getField(this, 7) != null;
    }
    get tables() {
      return pb_1__namespace.Message.getWrapperField(this, DbTables, 8);
    }
    set tables(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 8, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_tables() {
      return pb_1__namespace.Message.getField(this, 8) != null;
    }
    get rows() {
      return pb_1__namespace.Message.getWrapperField(this, DbRows, 9);
    }
    set rows(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 9, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_rows() {
      return pb_1__namespace.Message.getField(this, 9) != null;
    }
    get ui() {
      return pb_1__namespace.Message.getWrapperField(this, UserInfo, 10);
    }
    set ui(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 10, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_ui() {
      return pb_1__namespace.Message.getField(this, 10) != null;
    }
    get ocr() {
      return pb_1__namespace.Message.getWrapperField(this, OcrMsg, 11);
    }
    set ocr(value) {
      pb_1__namespace.Message.setOneofWrapperField(this, 11, __privateGet(this, _one_of_decls2)[0], value);
    }
    get has_ocr() {
      return pb_1__namespace.Message.getField(this, 11) != null;
    }
    get msg() {
      const cases = {
        0: "none",
        2: "status",
        3: "str",
        4: "wxmsg",
        5: "types",
        6: "contacts",
        7: "dbs",
        8: "tables",
        9: "rows",
        10: "ui",
        11: "ocr"
      };
      return cases[pb_1__namespace.Message.computeOneofCase(this, [2, 3, 4, 5, 6, 7, 8, 9, 10, 11])];
    }
    static fromObject(data) {
      const message = new _Response({});
      if (data.func != null) {
        message.func = data.func;
      }
      if (data.status != null) {
        message.status = data.status;
      }
      if (data.str != null) {
        message.str = data.str;
      }
      if (data.wxmsg != null) {
        message.wxmsg = WxMsg.fromObject(data.wxmsg);
      }
      if (data.types != null) {
        message.types = MsgTypes.fromObject(data.types);
      }
      if (data.contacts != null) {
        message.contacts = RpcContacts.fromObject(data.contacts);
      }
      if (data.dbs != null) {
        message.dbs = DbNames.fromObject(data.dbs);
      }
      if (data.tables != null) {
        message.tables = DbTables.fromObject(data.tables);
      }
      if (data.rows != null) {
        message.rows = DbRows.fromObject(data.rows);
      }
      if (data.ui != null) {
        message.ui = UserInfo.fromObject(data.ui);
      }
      if (data.ocr != null) {
        message.ocr = OcrMsg.fromObject(data.ocr);
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.func != null) {
        data.func = this.func;
      }
      if (this.status != null) {
        data.status = this.status;
      }
      if (this.str != null) {
        data.str = this.str;
      }
      if (this.wxmsg != null) {
        data.wxmsg = this.wxmsg.toObject();
      }
      if (this.types != null) {
        data.types = this.types.toObject();
      }
      if (this.contacts != null) {
        data.contacts = this.contacts.toObject();
      }
      if (this.dbs != null) {
        data.dbs = this.dbs.toObject();
      }
      if (this.tables != null) {
        data.tables = this.tables.toObject();
      }
      if (this.rows != null) {
        data.rows = this.rows.toObject();
      }
      if (this.ui != null) {
        data.ui = this.ui.toObject();
      }
      if (this.ocr != null) {
        data.ocr = this.ocr.toObject();
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.func != 0 /* FUNC_RESERVED */)
        writer.writeEnum(1, this.func);
      if (this.has_status)
        writer.writeInt32(2, this.status);
      if (this.has_str)
        writer.writeString(3, this.str);
      if (this.has_wxmsg)
        writer.writeMessage(4, this.wxmsg, () => this.wxmsg.serialize(writer));
      if (this.has_types)
        writer.writeMessage(5, this.types, () => this.types.serialize(writer));
      if (this.has_contacts)
        writer.writeMessage(6, this.contacts, () => this.contacts.serialize(writer));
      if (this.has_dbs)
        writer.writeMessage(7, this.dbs, () => this.dbs.serialize(writer));
      if (this.has_tables)
        writer.writeMessage(8, this.tables, () => this.tables.serialize(writer));
      if (this.has_rows)
        writer.writeMessage(9, this.rows, () => this.rows.serialize(writer));
      if (this.has_ui)
        writer.writeMessage(10, this.ui, () => this.ui.serialize(writer));
      if (this.has_ocr)
        writer.writeMessage(11, this.ocr, () => this.ocr.serialize(writer));
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _Response();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.func = reader.readEnum();
            break;
          case 2:
            message.status = reader.readInt32();
            break;
          case 3:
            message.str = reader.readString();
            break;
          case 4:
            reader.readMessage(message.wxmsg, () => message.wxmsg = WxMsg.deserialize(reader));
            break;
          case 5:
            reader.readMessage(message.types, () => message.types = MsgTypes.deserialize(reader));
            break;
          case 6:
            reader.readMessage(message.contacts, () => message.contacts = RpcContacts.deserialize(reader));
            break;
          case 7:
            reader.readMessage(message.dbs, () => message.dbs = DbNames.deserialize(reader));
            break;
          case 8:
            reader.readMessage(message.tables, () => message.tables = DbTables.deserialize(reader));
            break;
          case 9:
            reader.readMessage(message.rows, () => message.rows = DbRows.deserialize(reader));
            break;
          case 10:
            reader.readMessage(message.ui, () => message.ui = UserInfo.deserialize(reader));
            break;
          case 11:
            reader.readMessage(message.ocr, () => message.ocr = OcrMsg.deserialize(reader));
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _Response.deserialize(bytes);
    }
  };
  _one_of_decls2 = new WeakMap();
  wcf2.Response = _Response;
  const _Empty = class _Empty extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls3, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls3));
    }
    static fromObject(data) {
      const message = new _Empty({});
      return message;
    }
    toObject() {
      const data = {};
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _Empty();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _Empty.deserialize(bytes);
    }
  };
  _one_of_decls3 = new WeakMap();
  let Empty = _Empty;
  wcf2.Empty = _Empty;
  const _WxMsg = class _WxMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls4, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls4));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("is_self" in data && data.is_self != void 0) {
          this.is_self = data.is_self;
        }
        if ("is_group" in data && data.is_group != void 0) {
          this.is_group = data.is_group;
        }
        if ("id" in data && data.id != void 0) {
          this.id = data.id;
        }
        if ("type" in data && data.type != void 0) {
          this.type = data.type;
        }
        if ("ts" in data && data.ts != void 0) {
          this.ts = data.ts;
        }
        if ("roomid" in data && data.roomid != void 0) {
          this.roomid = data.roomid;
        }
        if ("content" in data && data.content != void 0) {
          this.content = data.content;
        }
        if ("sender" in data && data.sender != void 0) {
          this.sender = data.sender;
        }
        if ("sign" in data && data.sign != void 0) {
          this.sign = data.sign;
        }
        if ("thumb" in data && data.thumb != void 0) {
          this.thumb = data.thumb;
        }
        if ("extra" in data && data.extra != void 0) {
          this.extra = data.extra;
        }
        if ("xml" in data && data.xml != void 0) {
          this.xml = data.xml;
        }
      }
    }
    get is_self() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, false);
    }
    set is_self(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get is_group() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, false);
    }
    set is_group(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get id() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "0");
    }
    set id(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    get type() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 4, 0);
    }
    set type(value) {
      pb_1__namespace.Message.setField(this, 4, value);
    }
    get ts() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 5, 0);
    }
    set ts(value) {
      pb_1__namespace.Message.setField(this, 5, value);
    }
    get roomid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 6, "");
    }
    set roomid(value) {
      pb_1__namespace.Message.setField(this, 6, value);
    }
    get content() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 7, "");
    }
    set content(value) {
      pb_1__namespace.Message.setField(this, 7, value);
    }
    get sender() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 8, "");
    }
    set sender(value) {
      pb_1__namespace.Message.setField(this, 8, value);
    }
    get sign() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 9, "");
    }
    set sign(value) {
      pb_1__namespace.Message.setField(this, 9, value);
    }
    get thumb() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 10, "");
    }
    set thumb(value) {
      pb_1__namespace.Message.setField(this, 10, value);
    }
    get extra() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 11, "");
    }
    set extra(value) {
      pb_1__namespace.Message.setField(this, 11, value);
    }
    get xml() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 12, "");
    }
    set xml(value) {
      pb_1__namespace.Message.setField(this, 12, value);
    }
    static fromObject(data) {
      const message = new _WxMsg({});
      if (data.is_self != null) {
        message.is_self = data.is_self;
      }
      if (data.is_group != null) {
        message.is_group = data.is_group;
      }
      if (data.id != null) {
        message.id = data.id;
      }
      if (data.type != null) {
        message.type = data.type;
      }
      if (data.ts != null) {
        message.ts = data.ts;
      }
      if (data.roomid != null) {
        message.roomid = data.roomid;
      }
      if (data.content != null) {
        message.content = data.content;
      }
      if (data.sender != null) {
        message.sender = data.sender;
      }
      if (data.sign != null) {
        message.sign = data.sign;
      }
      if (data.thumb != null) {
        message.thumb = data.thumb;
      }
      if (data.extra != null) {
        message.extra = data.extra;
      }
      if (data.xml != null) {
        message.xml = data.xml;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.is_self != null) {
        data.is_self = this.is_self;
      }
      if (this.is_group != null) {
        data.is_group = this.is_group;
      }
      if (this.id != null) {
        data.id = this.id;
      }
      if (this.type != null) {
        data.type = this.type;
      }
      if (this.ts != null) {
        data.ts = this.ts;
      }
      if (this.roomid != null) {
        data.roomid = this.roomid;
      }
      if (this.content != null) {
        data.content = this.content;
      }
      if (this.sender != null) {
        data.sender = this.sender;
      }
      if (this.sign != null) {
        data.sign = this.sign;
      }
      if (this.thumb != null) {
        data.thumb = this.thumb;
      }
      if (this.extra != null) {
        data.extra = this.extra;
      }
      if (this.xml != null) {
        data.xml = this.xml;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.is_self != false)
        writer.writeBool(1, this.is_self);
      if (this.is_group != false)
        writer.writeBool(2, this.is_group);
      if (this.id != "0")
        writer.writeUint64String(3, this.id);
      if (this.type != 0)
        writer.writeUint32(4, this.type);
      if (this.ts != 0)
        writer.writeUint32(5, this.ts);
      if (this.roomid.length)
        writer.writeString(6, this.roomid);
      if (this.content.length)
        writer.writeString(7, this.content);
      if (this.sender.length)
        writer.writeString(8, this.sender);
      if (this.sign.length)
        writer.writeString(9, this.sign);
      if (this.thumb.length)
        writer.writeString(10, this.thumb);
      if (this.extra.length)
        writer.writeString(11, this.extra);
      if (this.xml.length)
        writer.writeString(12, this.xml);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _WxMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.is_self = reader.readBool();
            break;
          case 2:
            message.is_group = reader.readBool();
            break;
          case 3:
            message.id = reader.readUint64String();
            break;
          case 4:
            message.type = reader.readUint32();
            break;
          case 5:
            message.ts = reader.readUint32();
            break;
          case 6:
            message.roomid = reader.readString();
            break;
          case 7:
            message.content = reader.readString();
            break;
          case 8:
            message.sender = reader.readString();
            break;
          case 9:
            message.sign = reader.readString();
            break;
          case 10:
            message.thumb = reader.readString();
            break;
          case 11:
            message.extra = reader.readString();
            break;
          case 12:
            message.xml = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _WxMsg.deserialize(bytes);
    }
  };
  _one_of_decls4 = new WeakMap();
  let WxMsg = _WxMsg;
  wcf2.WxMsg = _WxMsg;
  const _TextMsg = class _TextMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls5, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls5));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("msg" in data && data.msg != void 0) {
          this.msg = data.msg;
        }
        if ("receiver" in data && data.receiver != void 0) {
          this.receiver = data.receiver;
        }
        if ("aters" in data && data.aters != void 0) {
          this.aters = data.aters;
        }
      }
    }
    get msg() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set msg(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get receiver() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set receiver(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get aters() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set aters(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    static fromObject(data) {
      const message = new _TextMsg({});
      if (data.msg != null) {
        message.msg = data.msg;
      }
      if (data.receiver != null) {
        message.receiver = data.receiver;
      }
      if (data.aters != null) {
        message.aters = data.aters;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.msg != null) {
        data.msg = this.msg;
      }
      if (this.receiver != null) {
        data.receiver = this.receiver;
      }
      if (this.aters != null) {
        data.aters = this.aters;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.msg.length)
        writer.writeString(1, this.msg);
      if (this.receiver.length)
        writer.writeString(2, this.receiver);
      if (this.aters.length)
        writer.writeString(3, this.aters);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _TextMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.msg = reader.readString();
            break;
          case 2:
            message.receiver = reader.readString();
            break;
          case 3:
            message.aters = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _TextMsg.deserialize(bytes);
    }
  };
  _one_of_decls5 = new WeakMap();
  let TextMsg = _TextMsg;
  wcf2.TextMsg = _TextMsg;
  const _PathMsg = class _PathMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls6, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls6));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("path" in data && data.path != void 0) {
          this.path = data.path;
        }
        if ("receiver" in data && data.receiver != void 0) {
          this.receiver = data.receiver;
        }
      }
    }
    get path() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set path(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get receiver() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set receiver(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _PathMsg({});
      if (data.path != null) {
        message.path = data.path;
      }
      if (data.receiver != null) {
        message.receiver = data.receiver;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.path != null) {
        data.path = this.path;
      }
      if (this.receiver != null) {
        data.receiver = this.receiver;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.path.length)
        writer.writeString(1, this.path);
      if (this.receiver.length)
        writer.writeString(2, this.receiver);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _PathMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.path = reader.readString();
            break;
          case 2:
            message.receiver = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _PathMsg.deserialize(bytes);
    }
  };
  _one_of_decls6 = new WeakMap();
  let PathMsg = _PathMsg;
  wcf2.PathMsg = _PathMsg;
  const _XmlMsg = class _XmlMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls7, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls7));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("receiver" in data && data.receiver != void 0) {
          this.receiver = data.receiver;
        }
        if ("content" in data && data.content != void 0) {
          this.content = data.content;
        }
        if ("path" in data && data.path != void 0) {
          this.path = data.path;
        }
        if ("type" in data && data.type != void 0) {
          this.type = data.type;
        }
      }
    }
    get receiver() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set receiver(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get content() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set content(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get path() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set path(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    get type() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 4, 0);
    }
    set type(value) {
      pb_1__namespace.Message.setField(this, 4, value);
    }
    static fromObject(data) {
      const message = new _XmlMsg({});
      if (data.receiver != null) {
        message.receiver = data.receiver;
      }
      if (data.content != null) {
        message.content = data.content;
      }
      if (data.path != null) {
        message.path = data.path;
      }
      if (data.type != null) {
        message.type = data.type;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.receiver != null) {
        data.receiver = this.receiver;
      }
      if (this.content != null) {
        data.content = this.content;
      }
      if (this.path != null) {
        data.path = this.path;
      }
      if (this.type != null) {
        data.type = this.type;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.receiver.length)
        writer.writeString(1, this.receiver);
      if (this.content.length)
        writer.writeString(2, this.content);
      if (this.path.length)
        writer.writeString(3, this.path);
      if (this.type != 0)
        writer.writeUint64(4, this.type);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _XmlMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.receiver = reader.readString();
            break;
          case 2:
            message.content = reader.readString();
            break;
          case 3:
            message.path = reader.readString();
            break;
          case 4:
            message.type = reader.readUint64();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _XmlMsg.deserialize(bytes);
    }
  };
  _one_of_decls7 = new WeakMap();
  let XmlMsg = _XmlMsg;
  wcf2.XmlMsg = _XmlMsg;
  const _MsgTypes = class _MsgTypes extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls8, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls8));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("types" in data && data.types != void 0) {
          this.types = data.types;
        }
      }
      if (!this.types)
        this.types = /* @__PURE__ */ new Map();
    }
    get types() {
      return pb_1__namespace.Message.getField(this, 1);
    }
    set types(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    static fromObject(data) {
      const message = new _MsgTypes({});
      if (typeof data.types == "object") {
        message.types = new Map(Object.entries(data.types).map(([key, value]) => [Number(key), value]));
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.types != null) {
        data.types = Object.fromEntries(this.types);
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      for (const [key, value] of this.types) {
        writer.writeMessage(1, this.types, () => {
          writer.writeInt32(1, key);
          writer.writeString(2, value);
        });
      }
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _MsgTypes();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            reader.readMessage(message, () => pb_1__namespace.Map.deserializeBinary(message.types, reader, reader.readInt32, reader.readString));
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _MsgTypes.deserialize(bytes);
    }
  };
  _one_of_decls8 = new WeakMap();
  let MsgTypes = _MsgTypes;
  wcf2.MsgTypes = _MsgTypes;
  const _RpcContact = class _RpcContact extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls9, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls9));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("wxid" in data && data.wxid != void 0) {
          this.wxid = data.wxid;
        }
        if ("code" in data && data.code != void 0) {
          this.code = data.code;
        }
        if ("remark" in data && data.remark != void 0) {
          this.remark = data.remark;
        }
        if ("name" in data && data.name != void 0) {
          this.name = data.name;
        }
        if ("country" in data && data.country != void 0) {
          this.country = data.country;
        }
        if ("province" in data && data.province != void 0) {
          this.province = data.province;
        }
        if ("city" in data && data.city != void 0) {
          this.city = data.city;
        }
        if ("gender" in data && data.gender != void 0) {
          this.gender = data.gender;
        }
      }
    }
    get wxid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set wxid(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get code() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set code(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get remark() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set remark(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    get name() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 4, "");
    }
    set name(value) {
      pb_1__namespace.Message.setField(this, 4, value);
    }
    get country() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 5, "");
    }
    set country(value) {
      pb_1__namespace.Message.setField(this, 5, value);
    }
    get province() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 6, "");
    }
    set province(value) {
      pb_1__namespace.Message.setField(this, 6, value);
    }
    get city() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 7, "");
    }
    set city(value) {
      pb_1__namespace.Message.setField(this, 7, value);
    }
    get gender() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 8, 0);
    }
    set gender(value) {
      pb_1__namespace.Message.setField(this, 8, value);
    }
    static fromObject(data) {
      const message = new _RpcContact({});
      if (data.wxid != null) {
        message.wxid = data.wxid;
      }
      if (data.code != null) {
        message.code = data.code;
      }
      if (data.remark != null) {
        message.remark = data.remark;
      }
      if (data.name != null) {
        message.name = data.name;
      }
      if (data.country != null) {
        message.country = data.country;
      }
      if (data.province != null) {
        message.province = data.province;
      }
      if (data.city != null) {
        message.city = data.city;
      }
      if (data.gender != null) {
        message.gender = data.gender;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.wxid != null) {
        data.wxid = this.wxid;
      }
      if (this.code != null) {
        data.code = this.code;
      }
      if (this.remark != null) {
        data.remark = this.remark;
      }
      if (this.name != null) {
        data.name = this.name;
      }
      if (this.country != null) {
        data.country = this.country;
      }
      if (this.province != null) {
        data.province = this.province;
      }
      if (this.city != null) {
        data.city = this.city;
      }
      if (this.gender != null) {
        data.gender = this.gender;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.wxid.length)
        writer.writeString(1, this.wxid);
      if (this.code.length)
        writer.writeString(2, this.code);
      if (this.remark.length)
        writer.writeString(3, this.remark);
      if (this.name.length)
        writer.writeString(4, this.name);
      if (this.country.length)
        writer.writeString(5, this.country);
      if (this.province.length)
        writer.writeString(6, this.province);
      if (this.city.length)
        writer.writeString(7, this.city);
      if (this.gender != 0)
        writer.writeInt32(8, this.gender);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _RpcContact();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.wxid = reader.readString();
            break;
          case 2:
            message.code = reader.readString();
            break;
          case 3:
            message.remark = reader.readString();
            break;
          case 4:
            message.name = reader.readString();
            break;
          case 5:
            message.country = reader.readString();
            break;
          case 6:
            message.province = reader.readString();
            break;
          case 7:
            message.city = reader.readString();
            break;
          case 8:
            message.gender = reader.readInt32();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _RpcContact.deserialize(bytes);
    }
  };
  _one_of_decls9 = new WeakMap();
  let RpcContact = _RpcContact;
  wcf2.RpcContact = _RpcContact;
  const _RpcContacts = class _RpcContacts extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls10, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], __privateGet(this, _one_of_decls10));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("contacts" in data && data.contacts != void 0) {
          this.contacts = data.contacts;
        }
      }
    }
    get contacts() {
      return pb_1__namespace.Message.getRepeatedWrapperField(this, RpcContact, 1);
    }
    set contacts(value) {
      pb_1__namespace.Message.setRepeatedWrapperField(this, 1, value);
    }
    static fromObject(data) {
      const message = new _RpcContacts({});
      if (data.contacts != null) {
        message.contacts = data.contacts.map((item) => RpcContact.fromObject(item));
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.contacts != null) {
        data.contacts = this.contacts.map((item) => item.toObject());
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.contacts.length)
        writer.writeRepeatedMessage(1, this.contacts, (item) => item.serialize(writer));
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _RpcContacts();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            reader.readMessage(message.contacts, () => pb_1__namespace.Message.addToRepeatedWrapperField(message, 1, RpcContact.deserialize(reader), RpcContact));
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _RpcContacts.deserialize(bytes);
    }
  };
  _one_of_decls10 = new WeakMap();
  let RpcContacts = _RpcContacts;
  wcf2.RpcContacts = _RpcContacts;
  const _DbNames = class _DbNames extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls11, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], __privateGet(this, _one_of_decls11));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("names" in data && data.names != void 0) {
          this.names = data.names;
        }
      }
    }
    get names() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, []);
    }
    set names(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    static fromObject(data) {
      const message = new _DbNames({});
      if (data.names != null) {
        message.names = data.names;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.names != null) {
        data.names = this.names;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.names.length)
        writer.writeRepeatedString(1, this.names);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _DbNames();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            pb_1__namespace.Message.addToRepeatedField(message, 1, reader.readString());
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _DbNames.deserialize(bytes);
    }
  };
  _one_of_decls11 = new WeakMap();
  let DbNames = _DbNames;
  wcf2.DbNames = _DbNames;
  const _DbTable = class _DbTable extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls12, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls12));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("name" in data && data.name != void 0) {
          this.name = data.name;
        }
        if ("sql" in data && data.sql != void 0) {
          this.sql = data.sql;
        }
      }
    }
    get name() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set name(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get sql() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set sql(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _DbTable({});
      if (data.name != null) {
        message.name = data.name;
      }
      if (data.sql != null) {
        message.sql = data.sql;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.name != null) {
        data.name = this.name;
      }
      if (this.sql != null) {
        data.sql = this.sql;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.name.length)
        writer.writeString(1, this.name);
      if (this.sql.length)
        writer.writeString(2, this.sql);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _DbTable();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.name = reader.readString();
            break;
          case 2:
            message.sql = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _DbTable.deserialize(bytes);
    }
  };
  _one_of_decls12 = new WeakMap();
  let DbTable = _DbTable;
  wcf2.DbTable = _DbTable;
  const _DbTables = class _DbTables extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls13, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], __privateGet(this, _one_of_decls13));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("tables" in data && data.tables != void 0) {
          this.tables = data.tables;
        }
      }
    }
    get tables() {
      return pb_1__namespace.Message.getRepeatedWrapperField(this, DbTable, 1);
    }
    set tables(value) {
      pb_1__namespace.Message.setRepeatedWrapperField(this, 1, value);
    }
    static fromObject(data) {
      const message = new _DbTables({});
      if (data.tables != null) {
        message.tables = data.tables.map((item) => DbTable.fromObject(item));
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.tables != null) {
        data.tables = this.tables.map((item) => item.toObject());
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.tables.length)
        writer.writeRepeatedMessage(1, this.tables, (item) => item.serialize(writer));
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _DbTables();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            reader.readMessage(message.tables, () => pb_1__namespace.Message.addToRepeatedWrapperField(message, 1, DbTable.deserialize(reader), DbTable));
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _DbTables.deserialize(bytes);
    }
  };
  _one_of_decls13 = new WeakMap();
  let DbTables = _DbTables;
  wcf2.DbTables = _DbTables;
  const _DbQuery = class _DbQuery extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls14, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls14));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("db" in data && data.db != void 0) {
          this.db = data.db;
        }
        if ("sql" in data && data.sql != void 0) {
          this.sql = data.sql;
        }
      }
    }
    get db() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set db(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get sql() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set sql(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _DbQuery({});
      if (data.db != null) {
        message.db = data.db;
      }
      if (data.sql != null) {
        message.sql = data.sql;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.db != null) {
        data.db = this.db;
      }
      if (this.sql != null) {
        data.sql = this.sql;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.db.length)
        writer.writeString(1, this.db);
      if (this.sql.length)
        writer.writeString(2, this.sql);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _DbQuery();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.db = reader.readString();
            break;
          case 2:
            message.sql = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _DbQuery.deserialize(bytes);
    }
  };
  _one_of_decls14 = new WeakMap();
  let DbQuery = _DbQuery;
  wcf2.DbQuery = _DbQuery;
  const _DbField = class _DbField extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls15, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls15));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("type" in data && data.type != void 0) {
          this.type = data.type;
        }
        if ("column" in data && data.column != void 0) {
          this.column = data.column;
        }
        if ("content" in data && data.content != void 0) {
          this.content = data.content;
        }
      }
    }
    get type() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, 0);
    }
    set type(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get column() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set column(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get content() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, new Uint8Array(0));
    }
    set content(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    static fromObject(data) {
      const message = new _DbField({});
      if (data.type != null) {
        message.type = data.type;
      }
      if (data.column != null) {
        message.column = data.column;
      }
      if (data.content != null) {
        message.content = data.content;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.type != null) {
        data.type = this.type;
      }
      if (this.column != null) {
        data.column = this.column;
      }
      if (this.content != null) {
        data.content = this.content;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.type != 0)
        writer.writeInt32(1, this.type);
      if (this.column.length)
        writer.writeString(2, this.column);
      if (this.content.length)
        writer.writeBytes(3, this.content);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _DbField();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.type = reader.readInt32();
            break;
          case 2:
            message.column = reader.readString();
            break;
          case 3:
            message.content = reader.readBytes();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _DbField.deserialize(bytes);
    }
  };
  _one_of_decls15 = new WeakMap();
  let DbField = _DbField;
  wcf2.DbField = _DbField;
  const _DbRow = class _DbRow extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls16, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], __privateGet(this, _one_of_decls16));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("fields" in data && data.fields != void 0) {
          this.fields = data.fields;
        }
      }
    }
    get fields() {
      return pb_1__namespace.Message.getRepeatedWrapperField(this, DbField, 1);
    }
    set fields(value) {
      pb_1__namespace.Message.setRepeatedWrapperField(this, 1, value);
    }
    static fromObject(data) {
      const message = new _DbRow({});
      if (data.fields != null) {
        message.fields = data.fields.map((item) => DbField.fromObject(item));
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.fields != null) {
        data.fields = this.fields.map((item) => item.toObject());
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.fields.length)
        writer.writeRepeatedMessage(1, this.fields, (item) => item.serialize(writer));
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _DbRow();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            reader.readMessage(message.fields, () => pb_1__namespace.Message.addToRepeatedWrapperField(message, 1, DbField.deserialize(reader), DbField));
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _DbRow.deserialize(bytes);
    }
  };
  _one_of_decls16 = new WeakMap();
  let DbRow = _DbRow;
  wcf2.DbRow = _DbRow;
  const _DbRows = class _DbRows extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls17, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], __privateGet(this, _one_of_decls17));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("rows" in data && data.rows != void 0) {
          this.rows = data.rows;
        }
      }
    }
    get rows() {
      return pb_1__namespace.Message.getRepeatedWrapperField(this, DbRow, 1);
    }
    set rows(value) {
      pb_1__namespace.Message.setRepeatedWrapperField(this, 1, value);
    }
    static fromObject(data) {
      const message = new _DbRows({});
      if (data.rows != null) {
        message.rows = data.rows.map((item) => DbRow.fromObject(item));
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.rows != null) {
        data.rows = this.rows.map((item) => item.toObject());
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.rows.length)
        writer.writeRepeatedMessage(1, this.rows, (item) => item.serialize(writer));
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _DbRows();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            reader.readMessage(message.rows, () => pb_1__namespace.Message.addToRepeatedWrapperField(message, 1, DbRow.deserialize(reader), DbRow));
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _DbRows.deserialize(bytes);
    }
  };
  _one_of_decls17 = new WeakMap();
  let DbRows = _DbRows;
  wcf2.DbRows = _DbRows;
  const _Verification = class _Verification extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls18, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls18));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("v3" in data && data.v3 != void 0) {
          this.v3 = data.v3;
        }
        if ("v4" in data && data.v4 != void 0) {
          this.v4 = data.v4;
        }
        if ("scene" in data && data.scene != void 0) {
          this.scene = data.scene;
        }
      }
    }
    get v3() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set v3(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get v4() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set v4(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get scene() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, 0);
    }
    set scene(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    static fromObject(data) {
      const message = new _Verification({});
      if (data.v3 != null) {
        message.v3 = data.v3;
      }
      if (data.v4 != null) {
        message.v4 = data.v4;
      }
      if (data.scene != null) {
        message.scene = data.scene;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.v3 != null) {
        data.v3 = this.v3;
      }
      if (this.v4 != null) {
        data.v4 = this.v4;
      }
      if (this.scene != null) {
        data.scene = this.scene;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.v3.length)
        writer.writeString(1, this.v3);
      if (this.v4.length)
        writer.writeString(2, this.v4);
      if (this.scene != 0)
        writer.writeInt32(3, this.scene);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _Verification();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.v3 = reader.readString();
            break;
          case 2:
            message.v4 = reader.readString();
            break;
          case 3:
            message.scene = reader.readInt32();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _Verification.deserialize(bytes);
    }
  };
  _one_of_decls18 = new WeakMap();
  let Verification = _Verification;
  wcf2.Verification = _Verification;
  const _MemberMgmt = class _MemberMgmt extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls19, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls19));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("roomid" in data && data.roomid != void 0) {
          this.roomid = data.roomid;
        }
        if ("wxids" in data && data.wxids != void 0) {
          this.wxids = data.wxids;
        }
      }
    }
    get roomid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set roomid(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get wxids() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set wxids(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _MemberMgmt({});
      if (data.roomid != null) {
        message.roomid = data.roomid;
      }
      if (data.wxids != null) {
        message.wxids = data.wxids;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.roomid != null) {
        data.roomid = this.roomid;
      }
      if (this.wxids != null) {
        data.wxids = this.wxids;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.roomid.length)
        writer.writeString(1, this.roomid);
      if (this.wxids.length)
        writer.writeString(2, this.wxids);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _MemberMgmt();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.roomid = reader.readString();
            break;
          case 2:
            message.wxids = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _MemberMgmt.deserialize(bytes);
    }
  };
  _one_of_decls19 = new WeakMap();
  let MemberMgmt = _MemberMgmt;
  wcf2.MemberMgmt = _MemberMgmt;
  const _UserInfo = class _UserInfo extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls20, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls20));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("wxid" in data && data.wxid != void 0) {
          this.wxid = data.wxid;
        }
        if ("name" in data && data.name != void 0) {
          this.name = data.name;
        }
        if ("mobile" in data && data.mobile != void 0) {
          this.mobile = data.mobile;
        }
        if ("home" in data && data.home != void 0) {
          this.home = data.home;
        }
      }
    }
    get wxid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set wxid(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get name() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set name(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get mobile() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set mobile(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    get home() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 4, "");
    }
    set home(value) {
      pb_1__namespace.Message.setField(this, 4, value);
    }
    static fromObject(data) {
      const message = new _UserInfo({});
      if (data.wxid != null) {
        message.wxid = data.wxid;
      }
      if (data.name != null) {
        message.name = data.name;
      }
      if (data.mobile != null) {
        message.mobile = data.mobile;
      }
      if (data.home != null) {
        message.home = data.home;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.wxid != null) {
        data.wxid = this.wxid;
      }
      if (this.name != null) {
        data.name = this.name;
      }
      if (this.mobile != null) {
        data.mobile = this.mobile;
      }
      if (this.home != null) {
        data.home = this.home;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.wxid.length)
        writer.writeString(1, this.wxid);
      if (this.name.length)
        writer.writeString(2, this.name);
      if (this.mobile.length)
        writer.writeString(3, this.mobile);
      if (this.home.length)
        writer.writeString(4, this.home);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _UserInfo();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.wxid = reader.readString();
            break;
          case 2:
            message.name = reader.readString();
            break;
          case 3:
            message.mobile = reader.readString();
            break;
          case 4:
            message.home = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _UserInfo.deserialize(bytes);
    }
  };
  _one_of_decls20 = new WeakMap();
  let UserInfo = _UserInfo;
  wcf2.UserInfo = _UserInfo;
  const _DecPath = class _DecPath extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls21, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls21));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("src" in data && data.src != void 0) {
          this.src = data.src;
        }
        if ("dst" in data && data.dst != void 0) {
          this.dst = data.dst;
        }
      }
    }
    get src() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set src(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get dst() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set dst(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _DecPath({});
      if (data.src != null) {
        message.src = data.src;
      }
      if (data.dst != null) {
        message.dst = data.dst;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.src != null) {
        data.src = this.src;
      }
      if (this.dst != null) {
        data.dst = this.dst;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.src.length)
        writer.writeString(1, this.src);
      if (this.dst.length)
        writer.writeString(2, this.dst);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _DecPath();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.src = reader.readString();
            break;
          case 2:
            message.dst = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _DecPath.deserialize(bytes);
    }
  };
  _one_of_decls21 = new WeakMap();
  let DecPath = _DecPath;
  wcf2.DecPath = _DecPath;
  const _Transfer = class _Transfer extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls22, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls22));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("wxid" in data && data.wxid != void 0) {
          this.wxid = data.wxid;
        }
        if ("tfid" in data && data.tfid != void 0) {
          this.tfid = data.tfid;
        }
        if ("taid" in data && data.taid != void 0) {
          this.taid = data.taid;
        }
      }
    }
    get wxid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set wxid(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get tfid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set tfid(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get taid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set taid(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    static fromObject(data) {
      const message = new _Transfer({});
      if (data.wxid != null) {
        message.wxid = data.wxid;
      }
      if (data.tfid != null) {
        message.tfid = data.tfid;
      }
      if (data.taid != null) {
        message.taid = data.taid;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.wxid != null) {
        data.wxid = this.wxid;
      }
      if (this.tfid != null) {
        data.tfid = this.tfid;
      }
      if (this.taid != null) {
        data.taid = this.taid;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.wxid.length)
        writer.writeString(1, this.wxid);
      if (this.tfid.length)
        writer.writeString(2, this.tfid);
      if (this.taid.length)
        writer.writeString(3, this.taid);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _Transfer();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.wxid = reader.readString();
            break;
          case 2:
            message.tfid = reader.readString();
            break;
          case 3:
            message.taid = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _Transfer.deserialize(bytes);
    }
  };
  _one_of_decls22 = new WeakMap();
  let Transfer = _Transfer;
  wcf2.Transfer = _Transfer;
  const _AttachMsg = class _AttachMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls23, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls23));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("id" in data && data.id != void 0) {
          this.id = data.id;
        }
        if ("thumb" in data && data.thumb != void 0) {
          this.thumb = data.thumb;
        }
        if ("extra" in data && data.extra != void 0) {
          this.extra = data.extra;
        }
      }
    }
    get id() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "0");
    }
    set id(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get thumb() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set thumb(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get extra() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set extra(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    static fromObject(data) {
      const message = new _AttachMsg({});
      if (data.id != null) {
        message.id = data.id;
      }
      if (data.thumb != null) {
        message.thumb = data.thumb;
      }
      if (data.extra != null) {
        message.extra = data.extra;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.id != null) {
        data.id = this.id;
      }
      if (this.thumb != null) {
        data.thumb = this.thumb;
      }
      if (this.extra != null) {
        data.extra = this.extra;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.id != "0")
        writer.writeUint64String(1, this.id);
      if (this.thumb.length)
        writer.writeString(2, this.thumb);
      if (this.extra.length)
        writer.writeString(3, this.extra);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _AttachMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.id = reader.readUint64String();
            break;
          case 2:
            message.thumb = reader.readString();
            break;
          case 3:
            message.extra = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _AttachMsg.deserialize(bytes);
    }
  };
  _one_of_decls23 = new WeakMap();
  let AttachMsg = _AttachMsg;
  wcf2.AttachMsg = _AttachMsg;
  const _AudioMsg = class _AudioMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls24, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls24));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("id" in data && data.id != void 0) {
          this.id = data.id;
        }
        if ("dir" in data && data.dir != void 0) {
          this.dir = data.dir;
        }
      }
    }
    get id() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "0");
    }
    set id(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get dir() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set dir(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _AudioMsg({});
      if (data.id != null) {
        message.id = data.id;
      }
      if (data.dir != null) {
        message.dir = data.dir;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.id != null) {
        data.id = this.id;
      }
      if (this.dir != null) {
        data.dir = this.dir;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.id != "0")
        writer.writeUint64String(1, this.id);
      if (this.dir.length)
        writer.writeString(2, this.dir);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _AudioMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.id = reader.readUint64String();
            break;
          case 2:
            message.dir = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _AudioMsg.deserialize(bytes);
    }
  };
  _one_of_decls24 = new WeakMap();
  let AudioMsg = _AudioMsg;
  wcf2.AudioMsg = _AudioMsg;
  const _RichText = class _RichText extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls25, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls25));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("name" in data && data.name != void 0) {
          this.name = data.name;
        }
        if ("account" in data && data.account != void 0) {
          this.account = data.account;
        }
        if ("title" in data && data.title != void 0) {
          this.title = data.title;
        }
        if ("digest" in data && data.digest != void 0) {
          this.digest = data.digest;
        }
        if ("url" in data && data.url != void 0) {
          this.url = data.url;
        }
        if ("thumburl" in data && data.thumburl != void 0) {
          this.thumburl = data.thumburl;
        }
        if ("receiver" in data && data.receiver != void 0) {
          this.receiver = data.receiver;
        }
      }
    }
    get name() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set name(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get account() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set account(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    get title() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, "");
    }
    set title(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    get digest() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 4, "");
    }
    set digest(value) {
      pb_1__namespace.Message.setField(this, 4, value);
    }
    get url() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 5, "");
    }
    set url(value) {
      pb_1__namespace.Message.setField(this, 5, value);
    }
    get thumburl() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 6, "");
    }
    set thumburl(value) {
      pb_1__namespace.Message.setField(this, 6, value);
    }
    get receiver() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 7, "");
    }
    set receiver(value) {
      pb_1__namespace.Message.setField(this, 7, value);
    }
    static fromObject(data) {
      const message = new _RichText({});
      if (data.name != null) {
        message.name = data.name;
      }
      if (data.account != null) {
        message.account = data.account;
      }
      if (data.title != null) {
        message.title = data.title;
      }
      if (data.digest != null) {
        message.digest = data.digest;
      }
      if (data.url != null) {
        message.url = data.url;
      }
      if (data.thumburl != null) {
        message.thumburl = data.thumburl;
      }
      if (data.receiver != null) {
        message.receiver = data.receiver;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.name != null) {
        data.name = this.name;
      }
      if (this.account != null) {
        data.account = this.account;
      }
      if (this.title != null) {
        data.title = this.title;
      }
      if (this.digest != null) {
        data.digest = this.digest;
      }
      if (this.url != null) {
        data.url = this.url;
      }
      if (this.thumburl != null) {
        data.thumburl = this.thumburl;
      }
      if (this.receiver != null) {
        data.receiver = this.receiver;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.name.length)
        writer.writeString(1, this.name);
      if (this.account.length)
        writer.writeString(2, this.account);
      if (this.title.length)
        writer.writeString(3, this.title);
      if (this.digest.length)
        writer.writeString(4, this.digest);
      if (this.url.length)
        writer.writeString(5, this.url);
      if (this.thumburl.length)
        writer.writeString(6, this.thumburl);
      if (this.receiver.length)
        writer.writeString(7, this.receiver);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _RichText();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.name = reader.readString();
            break;
          case 2:
            message.account = reader.readString();
            break;
          case 3:
            message.title = reader.readString();
            break;
          case 4:
            message.digest = reader.readString();
            break;
          case 5:
            message.url = reader.readString();
            break;
          case 6:
            message.thumburl = reader.readString();
            break;
          case 7:
            message.receiver = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _RichText.deserialize(bytes);
    }
  };
  _one_of_decls25 = new WeakMap();
  let RichText = _RichText;
  wcf2.RichText = _RichText;
  const _PatMsg = class _PatMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls26, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls26));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("roomid" in data && data.roomid != void 0) {
          this.roomid = data.roomid;
        }
        if ("wxid" in data && data.wxid != void 0) {
          this.wxid = data.wxid;
        }
      }
    }
    get roomid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
    }
    set roomid(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get wxid() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set wxid(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _PatMsg({});
      if (data.roomid != null) {
        message.roomid = data.roomid;
      }
      if (data.wxid != null) {
        message.wxid = data.wxid;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.roomid != null) {
        data.roomid = this.roomid;
      }
      if (this.wxid != null) {
        data.wxid = this.wxid;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.roomid.length)
        writer.writeString(1, this.roomid);
      if (this.wxid.length)
        writer.writeString(2, this.wxid);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _PatMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.roomid = reader.readString();
            break;
          case 2:
            message.wxid = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _PatMsg.deserialize(bytes);
    }
  };
  _one_of_decls26 = new WeakMap();
  let PatMsg = _PatMsg;
  wcf2.PatMsg = _PatMsg;
  const _OcrMsg = class _OcrMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls27, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls27));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("status" in data && data.status != void 0) {
          this.status = data.status;
        }
        if ("result" in data && data.result != void 0) {
          this.result = data.result;
        }
      }
    }
    get status() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, 0);
    }
    set status(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get result() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set result(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _OcrMsg({});
      if (data.status != null) {
        message.status = data.status;
      }
      if (data.result != null) {
        message.result = data.result;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.status != null) {
        data.status = this.status;
      }
      if (this.result != null) {
        data.result = this.result;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.status != 0)
        writer.writeInt32(1, this.status);
      if (this.result.length)
        writer.writeString(2, this.result);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _OcrMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.status = reader.readInt32();
            break;
          case 2:
            message.result = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _OcrMsg.deserialize(bytes);
    }
  };
  _one_of_decls27 = new WeakMap();
  let OcrMsg = _OcrMsg;
  wcf2.OcrMsg = _OcrMsg;
  const _ForwardMsg = class _ForwardMsg extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls28, []);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls28));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("id" in data && data.id != void 0) {
          this.id = data.id;
        }
        if ("receiver" in data && data.receiver != void 0) {
          this.receiver = data.receiver;
        }
      }
    }
    get id() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 1, "0");
    }
    set id(value) {
      pb_1__namespace.Message.setField(this, 1, value);
    }
    get receiver() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
    }
    set receiver(value) {
      pb_1__namespace.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _ForwardMsg({});
      if (data.id != null) {
        message.id = data.id;
      }
      if (data.receiver != null) {
        message.receiver = data.receiver;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.id != null) {
        data.id = this.id;
      }
      if (this.receiver != null) {
        data.receiver = this.receiver;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.id != "0")
        writer.writeUint64String(1, this.id);
      if (this.receiver.length)
        writer.writeString(2, this.receiver);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _ForwardMsg();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.id = reader.readUint64String();
            break;
          case 2:
            message.receiver = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _ForwardMsg.deserialize(bytes);
    }
  };
  _one_of_decls28 = new WeakMap();
  let ForwardMsg = _ForwardMsg;
  wcf2.ForwardMsg = _ForwardMsg;
  const _RoomData = class _RoomData extends pb_1__namespace.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls29, [[2], [4], [6]]);
      pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1, 9], __privateGet(this, _one_of_decls29));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("members" in data && data.members != void 0) {
          this.members = data.members;
        }
        if ("field_2" in data && data.field_2 != void 0) {
          this.field_2 = data.field_2;
        }
        if ("field_3" in data && data.field_3 != void 0) {
          this.field_3 = data.field_3;
        }
        if ("field_4" in data && data.field_4 != void 0) {
          this.field_4 = data.field_4;
        }
        if ("capacity" in data && data.capacity != void 0) {
          this.capacity = data.capacity;
        }
        if ("field_6" in data && data.field_6 != void 0) {
          this.field_6 = data.field_6;
        }
        if ("field_7" in data && data.field_7 != void 0) {
          this.field_7 = data.field_7;
        }
        if ("field_8" in data && data.field_8 != void 0) {
          this.field_8 = data.field_8;
        }
        if ("admins" in data && data.admins != void 0) {
          this.admins = data.admins;
        }
      }
    }
    get members() {
      return pb_1__namespace.Message.getRepeatedWrapperField(this, _RoomData.RoomMember, 1);
    }
    set members(value) {
      pb_1__namespace.Message.setRepeatedWrapperField(this, 1, value);
    }
    get field_2() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 2, 0);
    }
    set field_2(value) {
      pb_1__namespace.Message.setOneofField(this, 2, __privateGet(this, _one_of_decls29)[0], value);
    }
    get has_field_2() {
      return pb_1__namespace.Message.getField(this, 2) != null;
    }
    get field_3() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 3, 0);
    }
    set field_3(value) {
      pb_1__namespace.Message.setField(this, 3, value);
    }
    get field_4() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 4, 0);
    }
    set field_4(value) {
      pb_1__namespace.Message.setOneofField(this, 4, __privateGet(this, _one_of_decls29)[1], value);
    }
    get has_field_4() {
      return pb_1__namespace.Message.getField(this, 4) != null;
    }
    get capacity() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 5, 0);
    }
    set capacity(value) {
      pb_1__namespace.Message.setField(this, 5, value);
    }
    get field_6() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 6, "");
    }
    set field_6(value) {
      pb_1__namespace.Message.setOneofField(this, 6, __privateGet(this, _one_of_decls29)[2], value);
    }
    get has_field_6() {
      return pb_1__namespace.Message.getField(this, 6) != null;
    }
    get field_7() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 7, 0);
    }
    set field_7(value) {
      pb_1__namespace.Message.setField(this, 7, value);
    }
    get field_8() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 8, 0);
    }
    set field_8(value) {
      pb_1__namespace.Message.setField(this, 8, value);
    }
    get admins() {
      return pb_1__namespace.Message.getFieldWithDefault(this, 9, []);
    }
    set admins(value) {
      pb_1__namespace.Message.setField(this, 9, value);
    }
    get _field_2() {
      const cases = {
        0: "none",
        2: "field_2"
      };
      return cases[pb_1__namespace.Message.computeOneofCase(this, [2])];
    }
    get _field_4() {
      const cases = {
        0: "none",
        4: "field_4"
      };
      return cases[pb_1__namespace.Message.computeOneofCase(this, [4])];
    }
    get _field_6() {
      const cases = {
        0: "none",
        6: "field_6"
      };
      return cases[pb_1__namespace.Message.computeOneofCase(this, [6])];
    }
    static fromObject(data) {
      const message = new _RoomData({});
      if (data.members != null) {
        message.members = data.members.map((item) => _RoomData.RoomMember.fromObject(item));
      }
      if (data.field_2 != null) {
        message.field_2 = data.field_2;
      }
      if (data.field_3 != null) {
        message.field_3 = data.field_3;
      }
      if (data.field_4 != null) {
        message.field_4 = data.field_4;
      }
      if (data.capacity != null) {
        message.capacity = data.capacity;
      }
      if (data.field_6 != null) {
        message.field_6 = data.field_6;
      }
      if (data.field_7 != null) {
        message.field_7 = data.field_7;
      }
      if (data.field_8 != null) {
        message.field_8 = data.field_8;
      }
      if (data.admins != null) {
        message.admins = data.admins;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.members != null) {
        data.members = this.members.map((item) => item.toObject());
      }
      if (this.field_2 != null) {
        data.field_2 = this.field_2;
      }
      if (this.field_3 != null) {
        data.field_3 = this.field_3;
      }
      if (this.field_4 != null) {
        data.field_4 = this.field_4;
      }
      if (this.capacity != null) {
        data.capacity = this.capacity;
      }
      if (this.field_6 != null) {
        data.field_6 = this.field_6;
      }
      if (this.field_7 != null) {
        data.field_7 = this.field_7;
      }
      if (this.field_8 != null) {
        data.field_8 = this.field_8;
      }
      if (this.admins != null) {
        data.admins = this.admins;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1__namespace.BinaryWriter();
      if (this.members.length)
        writer.writeRepeatedMessage(1, this.members, (item) => item.serialize(writer));
      if (this.has_field_2)
        writer.writeInt32(2, this.field_2);
      if (this.field_3 != 0)
        writer.writeInt32(3, this.field_3);
      if (this.has_field_4)
        writer.writeInt32(4, this.field_4);
      if (this.capacity != 0)
        writer.writeInt32(5, this.capacity);
      if (this.has_field_6)
        writer.writeString(6, this.field_6);
      if (this.field_7 != 0)
        writer.writeInt32(7, this.field_7);
      if (this.field_8 != 0)
        writer.writeInt32(8, this.field_8);
      if (this.admins.length)
        writer.writeRepeatedString(9, this.admins);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _RoomData();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            reader.readMessage(message.members, () => pb_1__namespace.Message.addToRepeatedWrapperField(message, 1, _RoomData.RoomMember.deserialize(reader), _RoomData.RoomMember));
            break;
          case 2:
            message.field_2 = reader.readInt32();
            break;
          case 3:
            message.field_3 = reader.readInt32();
            break;
          case 4:
            message.field_4 = reader.readInt32();
            break;
          case 5:
            message.capacity = reader.readInt32();
            break;
          case 6:
            message.field_6 = reader.readString();
            break;
          case 7:
            message.field_7 = reader.readInt32();
            break;
          case 8:
            message.field_8 = reader.readInt32();
            break;
          case 9:
            pb_1__namespace.Message.addToRepeatedField(message, 9, reader.readString());
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _RoomData.deserialize(bytes);
    }
  };
  _one_of_decls29 = new WeakMap();
  wcf2.RoomData = _RoomData;
  ((RoomData2) => {
    var _one_of_decls30;
    const _RoomMember = class _RoomMember extends pb_1__namespace.Message {
      constructor(data) {
        super();
        __privateAdd(this, _one_of_decls30, [[2]]);
        pb_1__namespace.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls30));
        if (!Array.isArray(data) && typeof data == "object") {
          if ("wxid" in data && data.wxid != void 0) {
            this.wxid = data.wxid;
          }
          if ("name" in data && data.name != void 0) {
            this.name = data.name;
          }
          if ("state" in data && data.state != void 0) {
            this.state = data.state;
          }
        }
      }
      get wxid() {
        return pb_1__namespace.Message.getFieldWithDefault(this, 1, "");
      }
      set wxid(value) {
        pb_1__namespace.Message.setField(this, 1, value);
      }
      get name() {
        return pb_1__namespace.Message.getFieldWithDefault(this, 2, "");
      }
      set name(value) {
        pb_1__namespace.Message.setOneofField(this, 2, __privateGet(this, _one_of_decls30)[0], value);
      }
      get has_name() {
        return pb_1__namespace.Message.getField(this, 2) != null;
      }
      get state() {
        return pb_1__namespace.Message.getFieldWithDefault(this, 3, 0);
      }
      set state(value) {
        pb_1__namespace.Message.setField(this, 3, value);
      }
      get _name() {
        const cases = {
          0: "none",
          2: "name"
        };
        return cases[pb_1__namespace.Message.computeOneofCase(this, [2])];
      }
      static fromObject(data) {
        const message = new _RoomMember({});
        if (data.wxid != null) {
          message.wxid = data.wxid;
        }
        if (data.name != null) {
          message.name = data.name;
        }
        if (data.state != null) {
          message.state = data.state;
        }
        return message;
      }
      toObject() {
        const data = {};
        if (this.wxid != null) {
          data.wxid = this.wxid;
        }
        if (this.name != null) {
          data.name = this.name;
        }
        if (this.state != null) {
          data.state = this.state;
        }
        return data;
      }
      serialize(w) {
        const writer = w || new pb_1__namespace.BinaryWriter();
        if (this.wxid.length)
          writer.writeString(1, this.wxid);
        if (this.has_name)
          writer.writeString(2, this.name);
        if (this.state != 0)
          writer.writeInt32(3, this.state);
        if (!w)
          return writer.getResultBuffer();
      }
      static deserialize(bytes) {
        const reader = bytes instanceof pb_1__namespace.BinaryReader ? bytes : new pb_1__namespace.BinaryReader(bytes), message = new _RoomMember();
        while (reader.nextField()) {
          if (reader.isEndGroup())
            break;
          switch (reader.getFieldNumber()) {
            case 1:
              message.wxid = reader.readString();
              break;
            case 2:
              message.name = reader.readString();
              break;
            case 3:
              message.state = reader.readInt32();
              break;
            default:
              reader.skipField();
          }
        }
        return message;
      }
      serializeBinary() {
        return this.serialize();
      }
      static deserializeBinary(bytes) {
        return _RoomMember.deserialize(bytes);
      }
    };
    _one_of_decls30 = new WeakMap();
    RoomData2.RoomMember = _RoomMember;
  })(wcf2.RoomData || (wcf2.RoomData = {}));
})(exports.wcf || (exports.wcf = {}));
