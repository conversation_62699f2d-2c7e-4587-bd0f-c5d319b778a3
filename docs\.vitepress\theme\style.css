:root {
    --vp-c-brand: hsl(0 85% 57%);
    --vp-c-brand-light: hsl(0 85% 70%);
    --vp-c-brand-lighter: hsl(0 85% 80%);
    --vp-c-brand-dark: hsl(0 85% 57%);
    --vp-c-brand-darker: hsl(0 85% 40%);
    --vp-c-brand-1: hsl(0 85% 57%);
    --vp-c-brand-2: hsl(0 85% 70%);
    --vp-c-brand-3: hsl(0 85% 57%);
    --vp-home-hero-name-color: transparent;
    --vp-home-hero-name-background: -webkit-linear-gradient( 120deg, hsl(0 100% 60%), hsl(15 100% 60%) 35%, hsl(23 96% 62%) 45%, hsl(0 100% 60%) 65%, hsl(358 58% 47%) );
    --vp-home-hero-image-background-image: linear-gradient( -45deg, hsl(0 100% 60% / 80%), hsl(15 100% 60% / 80%) 40%, hsl(23 96% 62% / 80%) 45%, hsl(0 100% 60% / 80%) 60%, hsl(358 58% 47% / 80%) );
    --vp-home-hero-image-filter: blur(40px);
    --vp-c-gray-light-3: #d1d1d1;
    --vp-c-gray-light-5: #f2f2f2;
    --vp-c-gray-dark-2: #484848;
    --vp-c-gray-dark-3: #3a3a3a
}
.VPImage.image-src {
  width: 100%
}

.image-bg {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    width: 192px;
    height: 192px;
    background-image: var(--vp-home-hero-image-background-image);
    filter: var(--vp-home-hero-image-filter);
    transform: translate(-50%,-50%)
}
