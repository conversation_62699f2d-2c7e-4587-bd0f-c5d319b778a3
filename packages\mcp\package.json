{"name": "@wechatferry/mcp", "type": "module", "version": "0.0.26", "description": "A mcp server impl of wcferry", "author": "mrrhq <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/mrrhq", "homepage": "https://github.com/wechatferry/wechatferry#readme", "repository": {"type": "git", "url": "https://github.com/wechatferry/wechatferry"}, "bugs": "https://github.com/wechatferry/wechatferry/issues", "keywords": ["wechat", "wc<PERSON><PERSON>", "robot", "mcp"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "bin": {"mcp-server-wechat": "bin/mcp-server-wechat.js"}, "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist", "bin"], "scripts": {"build": "unbuild", "dev": "unbuild --stub", "mcp-server-wechat": "node bin/mcp-server-wechat.js", "inspector": "pnpx @modelcontextprotocol/inspector -e WECHATY_LOG=silent -e CONSOLA_LEVEL=-999 bin/mcp-server-wechat.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.9.0", "@wechatferry/logger": "workspace:*", "@wechatferry/puppet": "workspace:*", "@wechatferry/agent": "workspace:*", "wechaty": "^1.20.2", "zod": "^3.24.2"}}