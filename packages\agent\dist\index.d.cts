import EventEmitter from 'node:events';
import { BytesExtra, WxMsg, UserInfo, Wechatferry, wcf } from '@wechatferry/core';
import { FileBoxInterface, FileBox } from 'file-box';
import knex, { Knex } from 'knex';
import { <PERSON>uffer } from 'node:buffer';

declare function resolvedWechatferryAgentOptions(options: WechatferryAgentUserOptions): WechatferryAgentOptions;
/**
 * 解码 RoomData
 *
 * @param roomData roomData
 * @returns 解码后 JSON 对象
 */
declare function decodeRoomData(roomData: Buffer): {
    members?: ReturnType<() => {
        wxid?: string;
        name?: string;
        state?: number;
    }>[];
    field_2?: number;
    field_3?: number;
    field_4?: number;
    room_capacity?: number;
    field_7?: string;
    field_8?: string;
};
/**
 * 解码 BytesExtra
 *
 * @param bytesExtra bytesExtra
 * @returns 解码后 JSON 对象
 */
declare function decodeBytesExtra(bytesExtra: Buffer): {
    message1?: ReturnType<typeof BytesExtra.SubMessage1.prototype.toObject>;
    properties?: ReturnType<typeof BytesExtra.Property.prototype.toObject>[];
};
declare function parseBytesExtra(bytesExtra: ReturnType<typeof BytesExtra.prototype.toObject>): {
    extra: string;
    thumb: string;
    wxid: string;
    sign: string;
    xml: string;
};

interface WechatferryAgentEventMap {
    message: [WxMsg];
    login: [user: UserInfo];
    logout: [];
    error: [error: Error];
}
interface WechatferryAgentUserOptions {
    /**
     * wcf core instance
     */
    wcf?: Wechatferry;
    /**
     * 登录成功后是否继续检查登录状态
     * @description wcf 目前没有发出 `logout` 事件，且退出登录后 dll 会自动销毁
     * @description 如果你需要 `logout` 事件并且希望手动退出/切换账号后自动重新注入，请设置为 true
     * @description 开启后并在登录成功后，若每隔 30s 都没有收到事件或发送消息就检查一次存活
     * @description 你也可以设置一个数字（单位秒）来设置检查间隔，推荐 10 以上
     * @default false
     */
    keepalive?: boolean | number;
}
interface WechatferryAgentOptions extends Required<WechatferryAgentUserOptions> {
}
interface WechatferryAgentChatRoomMember {
    displayName: string;
    userName: string;
    nickName: string;
    remark: string;
    smallHeadImgUrl: string;
}
interface WechatferryAgentContact {
    tags: string[];
    userName: string;
    alias?: string | undefined;
    nickName: string;
    pinYinInitial: string;
    remark: string;
    remarkPinYinInitial: string;
    labelIdList: string;
    smallHeadImgUrl: string;
}
interface WechatferryAgentChatRoom {
    announcement: string;
    announcementEditor: string;
    announcementPublishTime: string;
    infoVersion: string;
    nickName: string;
    userName: string;
    ownerUserName: string;
    userNameList: string;
    smallHeadImgUrl: string;
    memberIdList: string[];
    displayNameList: string[];
    displayNameMap: Record<string, string>;
}
type WechatferryAgentEventMessage = WxMsg;
interface WechatferryAgentDBMessage {
    localId?: number;
    talkerId?: number;
    msgSvrId: number;
    type: number;
    subType: number;
    isSender: number;
    createTime: number;
    sequence?: number;
    statusEx?: number;
    flagEx: number;
    status: number;
    msgServerSeq: number;
    msgSequence: number;
    strTalker: string;
    strContent: string;
    bytesExtra: Buffer;
    parsedBytesExtra: ReturnType<typeof parseBytesExtra>;
    compressContent: Buffer;
    talkerWxid: string;
}
interface WechatferryAgentContactTag {
    labelId: string;
    labelName: string;
}

interface Contact {
    Alias?: string;
    NickName: string;
    PYInitial: string;
    QuanPin: string;
    UserName: string;
    Type: number;
    VerifyFlag: number;
    Remark: string;
    RemarkPYInitial: string;
    LabelIDList: string;
}
interface ChatRoomInfo {
    Announcement: string;
    AnnouncementEditor: string;
    AnnouncementPublishTime: string;
    ChatRoomName: string;
    InfoVersion: string;
}
interface ChatRoom {
    ChatRoomName: string;
    DisplayNameList: string;
    UserNameList: string;
    RoomData: Buffer;
    Reserved2: string;
}
interface ContactHeadImgUrl {
    bigHeadImgUrl: string;
    smallHeadImgUrl: string;
    usrName: string;
}
interface ContactLabel {
    LabelName: string;
    LabelID: string;
}
interface MSG {
    localId?: number;
    TalkerId?: number;
    MsgSvrID: number;
    Type: number;
    SubType: number;
    IsSender: number;
    CreateTime: number;
    Sequence?: number;
    StatusEx?: number;
    FlagEx: number;
    Status: number;
    MsgServerSeq: number;
    MsgSequence: number;
    StrTalker: string;
    StrContent: string;
    BytesExtra: Buffer;
    CompressContent: Buffer;
}
interface Name2ID {
    UsrName: string;
}
declare module 'knex/types/tables' {
    interface Tables {
        Contact: Contact;
        ChatRoomInfo: ChatRoomInfo;
        ChatRoom: ChatRoom;
        ContactHeadImgUrl: ContactHeadImgUrl;
        ContactLabel: ContactLabel;
        MSG: MSG;
        Name2ID: Name2ID;
    }
}
declare function useMicroMsgDbQueryBuilder(): {
    db: "MicroMsg.db";
    knex: knex.Knex<any, unknown[]>;
};
declare function useMSG0DbQueryBuilder(): {
    db: "MSG0.db";
    knex: knex.Knex<any, unknown[]>;
};

declare class WechatferryAgent extends EventEmitter<WechatferryAgentEventMap> {
    private intervalId;
    /** 是否登录 */
    private isLoggedIn;
    private isChecking;
    private aliveCounter;
    wcf: Wechatferry;
    private keepalive;
    constructor(options?: WechatferryAgentUserOptions);
    /**
     * 启动 wcf
     */
    start(): void;
    /**
     * 停止 wcf
     * @param error 要 emit 的错误对象
     */
    stop(error?: any): void;
    private onMessage;
    private onSended;
    private catchErrors;
    private checkLoginStatus;
    private startLoginCheck;
    private stopLoginCheck;
    /**
     * 执行 sql 查询
     *
     * @param db db 名称
     * @param sql sql 语句或 knex 查询构建器
     * @returns 查询结果
     */
    dbSqlQuery<T>(db: string, sql: string | Knex.QueryBuilder): T;
    getDbList(): string[];
    /**
     * 邀请联系人加群
     *
     * @param roomId 群id
     * @param contactId 联系人wxid
     */
    inviteChatRoomMembers(roomId: string, contactId: string | string[]): number;
    /**
     * 添加联系人加群
     *
     * @param roomId 群id
     * @param contactId 联系人wxid
     */
    addChatRoomMembers(roomId: string, contactId: string | string[]): number;
    /**
     * 踢出群聊
     *
     * @param roomId 群id
     * @param contactId 群成员wxid
     */
    removeChatRoomMembers(roomId: string, contactId: string | string[]): number;
    /**
     * 发送文本消息
     *
     * @param conversationId 会话id，可以是 wxid 或者 roomid
     * @param text 文本消息
     * @param mentionIdList 要 `@` 的 wxid 列表
     */
    sendText(conversationId: string, text: string, mentionIdList?: string[]): number;
    /**
     * 发送图片消息
     *
     * @param conversationId 会话id，可以是 wxid 或者 roomid
     * @param image 图片 fileBox
     */
    sendImage(conversationId: string, image: FileBoxInterface): Promise<number>;
    /**
     * 发送文件消息
     *
     * @param conversationId 会话id，可以是 wxid 或者 roomid
     * @param file 文件 fileBox
     */
    sendFile(conversationId: string, file: FileBoxInterface): Promise<number>;
    /**
     * 发送富文本消息
     *
     * @param conversationId 会话id，可以是 wxid 或者 roomid
     * @param desc 富文本内容
     */
    sendRichText(conversationId: string, desc: Omit<ReturnType<wcf.RichText['toObject']>, 'receiver'>): number;
    /**
     * 发送 xml 消息
     */
    sendXml(conversationId: string, xml: Omit<ReturnType<wcf.XmlMsg['toObject']>, 'receiver'>): void;
    /**
     * 转发消息
     *
     * @param conversationId 会话id，可以是 wxid 或者 roomid
     * @param messageId 要转发的消息 id
     */
    forwardMsg(conversationId: string, messageId: string): number;
    /**
     * 撤回消息
     *
     * @description 你只能撤回自己的消息
     * @param messageId 消息 ID
     */
    revokeMsg(messageId: string): number;
    /**
     * 下载文件
     * @description 下载消息中的视频、文件、语音
     * @param message 消息
     * @param timeout 超时
     */
    downloadFile(message: WechatferryAgentEventMessage, timeout?: number): Promise<FileBox>;
    /**
     * 群聊详细列表
     */
    getChatRoomList(): WechatferryAgentChatRoom[];
    /**
     * 群聊信息
     * @param userName roomId
     */
    getChatRoomInfo(userName: string): WechatferryAgentChatRoom | undefined;
    /**
     * 群聊成员
     * @param userName roomId
     */
    getChatRoomMembers(userName: string): WechatferryAgentChatRoomMember[] | undefined;
    /**
     * 群聊成员
     * @param memberIdList 群成员 wxid 列表
     * @param displayNameMap 群成员 wxid 与昵称对照表
     */
    getChatRoomMembersByMemberIdList(memberIdList: string[], displayNameMap?: Record<string, string>): WechatferryAgentChatRoomMember[];
    /**
     * 联系人信息
     * @param userName wxid 或 roomId
     */
    getContactInfo(userName: string): WechatferryAgentContact | undefined;
    /**
     * 联系人列表
     */
    getContactList(): WechatferryAgentContact[];
    getContactTagList(): WechatferryAgentContactTag[];
    /**
     * talkerId
     * @description 用于查询聊天记录
     * @deprecated 现在查询聊天记录不需要 talkerId 了
     * @param userName wxid 或 roomId
     * @param dbNumber 指定要查询 MSG 分表，默认为 0
     */
    getTalkerId(userName: string, dbNumber?: number): string | undefined;
    /**
     * 历史聊天记录
     *
     * @description 建议注入查询条件，不然非常的卡
     * @param userName wxid wxid 或 roomId
     * @param filter 注入查询条件
     * @param dbNumber 手动指定要查询 MSG 分表，默认为遍历查询所有的 MSG{x}.db，如果指定，但该分表不存在，则查询最后一个分表
     */
    getHistoryMessageList(userName: string, filter?: (sql: Knex.QueryBuilder<MSG>) => void, dbNumber?: number): WechatferryAgentDBMessage[];
    /**
     * 获取自己发送的最后一条消息
     */
    getLastSelfMessage(localId?: number): WechatferryAgentDBMessage;
    private formatChatRoomInfo;
    private formatHistoryMessage;
    private dbSqlQueryMSG;
    /**
     * 下载附件
     */
    private downloadAttach;
    /**
     * 下载图片
     */
    private downloadImage;
    /**
     * 下载语音
     */
    private downloadAudio;
}

export { type ChatRoom, type ChatRoomInfo, type Contact, type ContactHeadImgUrl, type ContactLabel, type MSG, type Name2ID, WechatferryAgent, type WechatferryAgentChatRoom, type WechatferryAgentChatRoomMember, type WechatferryAgentContact, type WechatferryAgentContactTag, type WechatferryAgentDBMessage, type WechatferryAgentEventMap, type WechatferryAgentEventMessage, type WechatferryAgentOptions, type WechatferryAgentUserOptions, decodeBytesExtra, decodeRoomData, parseBytesExtra, resolvedWechatferryAgentOptions, useMSG0DbQueryBuilder, useMicroMsgDbQueryBuilder };
