/**
 * A `<PERSON><PERSON><PERSON>` must satisfy the following in addition to `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>` and `MeetS<PERSON>ilattice` laws:
 *
 * - Absorbtion law for meet: `a ∧ (a ∨ b) <-> a`
 * - Absorbtion law for join: `a ∨ (a ∧ b) <-> a`
 *
 * @since 2.0.0
 */
import { Join<PERSON><PERSON><PERSON><PERSON><PERSON> } from './JoinSemilattice'
import { MeetS<PERSON>ilattice } from './MeetSemilattice'
/**
 * @category model
 * @since 2.0.0
 */
export interface Lattice<A> extends JoinSemilattice<A>, MeetSemilattice<A> {}
