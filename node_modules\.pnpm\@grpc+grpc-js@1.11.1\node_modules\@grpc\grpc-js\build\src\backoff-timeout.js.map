{"version": 3, "file": "backoff-timeout.js", "sourceRoot": "", "sources": ["../../src/backoff-timeout.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,MAAM,kBAAkB,GAAG,GAAG,CAAC;AAC/B,MAAM,cAAc,GAAG,MAAM,CAAC;AAC9B,MAAM,cAAc,GAAG,GAAG,CAAC;AAE3B;;;;GAIG;AACH,SAAS,aAAa,CAAC,GAAW,EAAE,GAAW;IAC7C,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AAC3C,CAAC;AASD,MAAa,cAAc;IAgDzB,YAAoB,QAAoB,EAAE,OAAwB;QAA9C,aAAQ,GAAR,QAAQ,CAAY;QA/CxC;;WAEG;QACc,iBAAY,GAAW,kBAAkB,CAAC;QAC3D;;WAEG;QACc,eAAU,GAAW,kBAAkB,CAAC;QACzD;;WAEG;QACc,aAAQ,GAAW,cAAc,CAAC;QACnD;;;WAGG;QACc,WAAM,GAAW,cAAc,CAAC;QAWjD;;WAEG;QACK,YAAO,GAAG,KAAK,CAAC;QACxB;;;WAGG;QACK,WAAM,GAAG,IAAI,CAAC;QACtB;;;WAGG;QACK,cAAS,GAAS,IAAI,IAAI,EAAE,CAAC;QACrC;;;WAGG;QACK,YAAO,GAAS,IAAI,IAAI,EAAE,CAAC;QAGjC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;YAC3C,CAAC;YACD,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YACvC,CAAC;YACD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAC/B,CAAC;YACD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YACnC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE,GAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAEO,QAAQ,CAAC,KAAa;;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,eAAe,CAC1B,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,SAAS,CAChD,CAAC;QACF,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;YAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC,CAAC;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,KAAK,kDAAI,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,EAChC,IAAI,CAAC,QAAQ,CACd,CAAC;QACF,MAAM,eAAe,GAAG,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;QAClD,IAAI,CAAC,SAAS;YACZ,WAAW,GAAG,aAAa,CAAC,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IACnE,CAAC;IAED;;;OAGG;IACH,IAAI;QACF,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;QACnC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;YAClC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1E,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,GAAG,GAAG,UAAU,EAAE,CAAC;gBACrB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,GAAG;;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,GAAG,kDAAI,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,KAAK;;QACH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,KAAK,kDAAI,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;CACF;AA/JD,wCA+JC"}