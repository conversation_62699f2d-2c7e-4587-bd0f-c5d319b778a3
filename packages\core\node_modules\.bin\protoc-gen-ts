#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/AIhub/wechat/node_modules/.pnpm/protoc-gen-ts@0.8.7/node_modules/protoc-gen-ts/node_modules:/mnt/d/AIhub/wechat/node_modules/.pnpm/protoc-gen-ts@0.8.7/node_modules:/mnt/d/AIhub/wechat/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/AIhub/wechat/node_modules/.pnpm/protoc-gen-ts@0.8.7/node_modules/protoc-gen-ts/node_modules:/mnt/d/AIhub/wechat/node_modules/.pnpm/protoc-gen-ts@0.8.7/node_modules:/mnt/d/AIhub/wechat/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../protoc-gen-ts/protoc-gen-ts.js" "$@"
else
  exec node  "$basedir/../protoc-gen-ts/protoc-gen-ts.js" "$@"
fi
