package com.wechat.ferry.entity.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请求出参-个微WCF发送XML消息
 *
 * <AUTHOR>
 * @date 2024/10/04 23:11
 */
@Data
@ApiModel(value = "wxPpWcfSendXmlMsgResp", description = "个微WCF发送XML消息请求出参")
public class WxPpWcfSendXmlMsgResp {

    /**
     * 类型编号
     */
    @ApiModelProperty(value = "类型编号")
    private Integer id;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String name;

}
