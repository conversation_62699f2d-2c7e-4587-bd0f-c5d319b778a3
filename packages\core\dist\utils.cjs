'use strict';

const node_os = require('node:os');
const node_buffer = require('node:buffer');
const node_fs = require('node:fs');
const pathe = require('pathe');
const fsExtra = require('fs-extra');

async function saveFileBox(file, dir = node_os.tmpdir()) {
  const dirPath = pathe.join(dir, "wechatferry");
  const filePath = pathe.join(dirPath, `${( new Date()).getTime()}-${file.name}`);
  if (!node_fs.existsSync(dirPath)) {
    await fsExtra.ensureDir(dirPath);
  }
  await file.toFile(filePath);
  return filePath;
}
function parseDbField(type, content) {
  switch (type) {
    case 1:
      return Number.parseInt(uint8Array2str(content), 10);
    case 2:
      return Number.parseFloat(uint8Array2str(content));
    case 4:
      return node_buffer.Buffer.from(content);
    case 5:
      return void 0;
    case 3:
    default:
      return uint8Array2str(content);
  }
}
function uint8Array2str(arr) {
  return node_buffer.Buffer.from(arr).toString();
}

exports.parseDbField = parseDbField;
exports.saveFileBox = saveFileBox;
exports.uint8Array2str = uint8Array2str;
