import * as pb_1 from 'google-protobuf';

var __accessCheck = (obj, member, msg) => {
  if (!member.has(obj))
    throw TypeError("Cannot " + msg);
};
var __privateGet = (obj, member, getter) => {
  __accessCheck(obj, member, "read from private field");
  return getter ? getter.call(obj) : member.get(obj);
};
var __privateAdd = (obj, member, value) => {
  if (member.has(obj))
    throw TypeError("Cannot add the same private member more than once");
  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
};
var _one_of_decls;
const _BytesExtra = class _BytesExtra extends pb_1.Message {
  constructor(data) {
    super();
    __privateAdd(this, _one_of_decls, []);
    pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [3], __privateGet(this, _one_of_decls));
    if (!Array.isArray(data) && typeof data == "object") {
      if ("message1" in data && data.message1 != void 0) {
        this.message1 = data.message1;
      }
      if ("properties" in data && data.properties != void 0) {
        this.properties = data.properties;
      }
    }
  }
  get message1() {
    return pb_1.Message.getWrapperField(this, _BytesExtra.SubMessage1, 1);
  }
  set message1(value) {
    pb_1.Message.setWrapperField(this, 1, value);
  }
  get has_message1() {
    return pb_1.Message.getField(this, 1) != null;
  }
  get properties() {
    return pb_1.Message.getRepeatedWrapperField(this, _BytesExtra.Property, 3);
  }
  set properties(value) {
    pb_1.Message.setRepeatedWrapperField(this, 3, value);
  }
  static fromObject(data) {
    const message = new _BytesExtra({});
    if (data.message1 != null) {
      message.message1 = _BytesExtra.SubMessage1.fromObject(data.message1);
    }
    if (data.properties != null) {
      message.properties = data.properties.map((item) => _BytesExtra.Property.fromObject(item));
    }
    return message;
  }
  toObject() {
    const data = {};
    if (this.message1 != null) {
      data.message1 = this.message1.toObject();
    }
    if (this.properties != null) {
      data.properties = this.properties.map((item) => item.toObject());
    }
    return data;
  }
  serialize(w) {
    const writer = w || new pb_1.BinaryWriter();
    if (this.has_message1)
      writer.writeMessage(1, this.message1, () => this.message1.serialize(writer));
    if (this.properties.length)
      writer.writeRepeatedMessage(3, this.properties, (item) => item.serialize(writer));
    if (!w)
      return writer.getResultBuffer();
  }
  static deserialize(bytes) {
    const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new _BytesExtra();
    while (reader.nextField()) {
      if (reader.isEndGroup())
        break;
      switch (reader.getFieldNumber()) {
        case 1:
          reader.readMessage(message.message1, () => message.message1 = _BytesExtra.SubMessage1.deserialize(reader));
          break;
        case 3:
          reader.readMessage(message.properties, () => pb_1.Message.addToRepeatedWrapperField(message, 3, _BytesExtra.Property.deserialize(reader), _BytesExtra.Property));
          break;
        default:
          reader.skipField();
      }
    }
    return message;
  }
  serializeBinary() {
    return this.serialize();
  }
  static deserializeBinary(bytes) {
    return _BytesExtra.deserialize(bytes);
  }
};
_one_of_decls = new WeakMap();
let BytesExtra = _BytesExtra;
((BytesExtra2) => {
  var _one_of_decls2, _one_of_decls3;
  ((PropertyKey2) => {
    PropertyKey2[PropertyKey2["FIELD0"] = 0] = "FIELD0";
    PropertyKey2[PropertyKey2["WXID"] = 1] = "WXID";
    PropertyKey2[PropertyKey2["SIGN"] = 2] = "SIGN";
    PropertyKey2[PropertyKey2["THUMB"] = 3] = "THUMB";
    PropertyKey2[PropertyKey2["EXTRA"] = 4] = "EXTRA";
    PropertyKey2[PropertyKey2["XML"] = 7] = "XML";
  })(BytesExtra2.PropertyKey || (BytesExtra2.PropertyKey = {}));
  const _Property = class _Property extends pb_1.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls2, []);
      pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls2));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("type" in data && data.type != void 0) {
          this.type = data.type;
        }
        if ("value" in data && data.value != void 0) {
          this.value = data.value;
        }
      }
    }
    get type() {
      return pb_1.Message.getFieldWithDefault(this, 1, 0 /* FIELD0 */);
    }
    set type(value) {
      pb_1.Message.setField(this, 1, value);
    }
    get value() {
      return pb_1.Message.getFieldWithDefault(this, 2, "");
    }
    set value(value) {
      pb_1.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _Property({});
      if (data.type != null) {
        message.type = data.type;
      }
      if (data.value != null) {
        message.value = data.value;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.type != null) {
        data.type = this.type;
      }
      if (this.value != null) {
        data.value = this.value;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1.BinaryWriter();
      if (this.type != 0 /* FIELD0 */)
        writer.writeEnum(1, this.type);
      if (this.value.length)
        writer.writeString(2, this.value);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new _Property();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.type = reader.readEnum();
            break;
          case 2:
            message.value = reader.readString();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _Property.deserialize(bytes);
    }
  };
  _one_of_decls2 = new WeakMap();
  BytesExtra2.Property = _Property;
  const _SubMessage1 = class _SubMessage1 extends pb_1.Message {
    constructor(data) {
      super();
      __privateAdd(this, _one_of_decls3, []);
      pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], __privateGet(this, _one_of_decls3));
      if (!Array.isArray(data) && typeof data == "object") {
        if ("field1" in data && data.field1 != void 0) {
          this.field1 = data.field1;
        }
        if ("field2" in data && data.field2 != void 0) {
          this.field2 = data.field2;
        }
      }
    }
    get field1() {
      return pb_1.Message.getFieldWithDefault(this, 1, 0);
    }
    set field1(value) {
      pb_1.Message.setField(this, 1, value);
    }
    get field2() {
      return pb_1.Message.getFieldWithDefault(this, 2, 0);
    }
    set field2(value) {
      pb_1.Message.setField(this, 2, value);
    }
    static fromObject(data) {
      const message = new _SubMessage1({});
      if (data.field1 != null) {
        message.field1 = data.field1;
      }
      if (data.field2 != null) {
        message.field2 = data.field2;
      }
      return message;
    }
    toObject() {
      const data = {};
      if (this.field1 != null) {
        data.field1 = this.field1;
      }
      if (this.field2 != null) {
        data.field2 = this.field2;
      }
      return data;
    }
    serialize(w) {
      const writer = w || new pb_1.BinaryWriter();
      if (this.field1 != 0)
        writer.writeInt32(1, this.field1);
      if (this.field2 != 0)
        writer.writeInt32(2, this.field2);
      if (!w)
        return writer.getResultBuffer();
    }
    static deserialize(bytes) {
      const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new _SubMessage1();
      while (reader.nextField()) {
        if (reader.isEndGroup())
          break;
        switch (reader.getFieldNumber()) {
          case 1:
            message.field1 = reader.readInt32();
            break;
          case 2:
            message.field2 = reader.readInt32();
            break;
          default:
            reader.skipField();
        }
      }
      return message;
    }
    serializeBinary() {
      return this.serialize();
    }
    static deserializeBinary(bytes) {
      return _SubMessage1.deserialize(bytes);
    }
  };
  _one_of_decls3 = new WeakMap();
  BytesExtra2.SubMessage1 = _SubMessage1;
})(BytesExtra || (BytesExtra = {}));

export { BytesExtra };
