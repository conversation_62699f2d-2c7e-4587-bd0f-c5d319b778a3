hoistPattern:
  - '*'
hoistedDependencies:
  '@algolia/autocomplete-core@1.17.7(@algolia/client-search@5.21.0)(algoliasearch@5.21.0)(search-insights@2.17.0)':
    '@algolia/autocomplete-core': public
  '@algolia/autocomplete-plugin-algolia-insights@1.17.7(@algolia/client-search@5.21.0)(algoliasearch@5.21.0)(search-insights@2.17.0)':
    '@algolia/autocomplete-plugin-algolia-insights': public
  '@algolia/autocomplete-preset-algolia@1.17.7(@algolia/client-search@5.21.0)(algoliasearch@5.21.0)':
    '@algolia/autocomplete-preset-algolia': public
  '@algolia/autocomplete-shared@1.17.7(@algolia/client-search@5.21.0)(algoliasearch@5.21.0)':
    '@algolia/autocomplete-shared': public
  '@algolia/client-abtesting@5.21.0':
    '@algolia/client-abtesting': public
  '@algolia/client-analytics@5.21.0':
    '@algolia/client-analytics': public
  '@algolia/client-common@5.21.0':
    '@algolia/client-common': public
  '@algolia/client-insights@5.21.0':
    '@algolia/client-insights': public
  '@algolia/client-personalization@5.21.0':
    '@algolia/client-personalization': public
  '@algolia/client-query-suggestions@5.21.0':
    '@algolia/client-query-suggestions': public
  '@algolia/client-search@5.21.0':
    '@algolia/client-search': public
  '@algolia/ingestion@1.21.0':
    '@algolia/ingestion': public
  '@algolia/monitoring@1.21.0':
    '@algolia/monitoring': public
  '@algolia/recommend@5.21.0':
    '@algolia/recommend': public
  '@algolia/requester-browser-xhr@5.21.0':
    '@algolia/requester-browser-xhr': public
  '@algolia/requester-fetch@5.21.0':
    '@algolia/requester-fetch': public
  '@algolia/requester-node-http@5.21.0':
    '@algolia/requester-node-http': public
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': public
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': public
  '@antfu/install-pkg@1.0.0':
    '@antfu/install-pkg': public
  '@antfu/ni@24.2.0':
    '@antfu/ni': public
  '@antfu/utils@0.7.10':
    '@antfu/utils': public
  '@babel/code-frame@7.24.7':
    '@babel/code-frame': public
  '@babel/compat-data@7.25.4':
    '@babel/compat-data': public
  '@babel/core@7.25.2':
    '@babel/core': public
  '@babel/generator@7.25.6':
    '@babel/generator': public
  '@babel/helper-annotate-as-pure@7.25.9':
    '@babel/helper-annotate-as-pure': public
  '@babel/helper-compilation-targets@7.25.2':
    '@babel/helper-compilation-targets': public
  '@babel/helper-create-class-features-plugin@7.26.9(@babel/core@7.26.10)':
    '@babel/helper-create-class-features-plugin': public
  '@babel/helper-member-expression-to-functions@7.25.9':
    '@babel/helper-member-expression-to-functions': public
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': public
  '@babel/helper-module-transforms@7.25.2(@babel/core@7.25.2)':
    '@babel/helper-module-transforms': public
  '@babel/helper-optimise-call-expression@7.25.9':
    '@babel/helper-optimise-call-expression': public
  '@babel/helper-plugin-utils@7.26.5':
    '@babel/helper-plugin-utils': public
  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.10)':
    '@babel/helper-replace-supers': public
  '@babel/helper-simple-access@7.24.7':
    '@babel/helper-simple-access': public
  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    '@babel/helper-skip-transparent-expression-wrappers': public
  '@babel/helper-string-parser@7.24.8':
    '@babel/helper-string-parser': public
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': public
  '@babel/helper-validator-option@7.24.8':
    '@babel/helper-validator-option': public
  '@babel/helpers@7.25.6':
    '@babel/helpers': public
  '@babel/highlight@7.24.7':
    '@babel/highlight': public
  '@babel/parser@7.26.10':
    '@babel/parser': public
  '@babel/plugin-proposal-decorators@7.24.7(@babel/core@7.26.10)':
    '@babel/plugin-proposal-decorators': public
  '@babel/plugin-syntax-decorators@7.24.7(@babel/core@7.26.10)':
    '@babel/plugin-syntax-decorators': public
  '@babel/plugin-syntax-import-attributes@7.25.6(@babel/core@7.26.10)':
    '@babel/plugin-syntax-import-attributes': public
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.26.10)':
    '@babel/plugin-syntax-import-meta': public
  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-syntax-jsx': public
  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-syntax-typescript': public
  '@babel/plugin-transform-typescript@7.26.8(@babel/core@7.26.10)':
    '@babel/plugin-transform-typescript': public
  '@babel/runtime@7.25.6':
    '@babel/runtime': public
  '@babel/standalone@7.25.6':
    '@babel/standalone': public
  '@babel/template@7.25.0':
    '@babel/template': public
  '@babel/traverse@7.25.6':
    '@babel/traverse': public
  '@babel/types@7.26.10':
    '@babel/types': public
  '@clack/core@0.4.1':
    '@clack/core': public
  '@clack/prompts@0.9.1':
    '@clack/prompts': public
  '@cloudflare/kv-asset-handler@0.3.4':
    '@cloudflare/kv-asset-handler': public
  '@docsearch/css@3.8.2':
    '@docsearch/css': public
  '@docsearch/js@3.8.2(@algolia/client-search@5.21.0)(search-insights@2.17.0)':
    '@docsearch/js': public
  '@docsearch/react@3.8.2(@algolia/client-search@5.21.0)(search-insights@2.17.0)':
    '@docsearch/react': public
  '@es-joy/jsdoccomment@0.50.0':
    '@es-joy/jsdoccomment': public
  '@esbuild/aix-ppc64@0.19.12':
    '@esbuild/aix-ppc64': public
  '@esbuild/android-arm64@0.19.12':
    '@esbuild/android-arm64': public
  '@esbuild/android-arm@0.19.12':
    '@esbuild/android-arm': public
  '@esbuild/android-x64@0.19.12':
    '@esbuild/android-x64': public
  '@esbuild/darwin-arm64@0.19.12':
    '@esbuild/darwin-arm64': public
  '@esbuild/darwin-x64@0.19.12':
    '@esbuild/darwin-x64': public
  '@esbuild/freebsd-arm64@0.19.12':
    '@esbuild/freebsd-arm64': public
  '@esbuild/freebsd-x64@0.19.12':
    '@esbuild/freebsd-x64': public
  '@esbuild/linux-arm64@0.19.12':
    '@esbuild/linux-arm64': public
  '@esbuild/linux-arm@0.19.12':
    '@esbuild/linux-arm': public
  '@esbuild/linux-ia32@0.19.12':
    '@esbuild/linux-ia32': public
  '@esbuild/linux-loong64@0.19.12':
    '@esbuild/linux-loong64': public
  '@esbuild/linux-mips64el@0.19.12':
    '@esbuild/linux-mips64el': public
  '@esbuild/linux-ppc64@0.19.12':
    '@esbuild/linux-ppc64': public
  '@esbuild/linux-riscv64@0.19.12':
    '@esbuild/linux-riscv64': public
  '@esbuild/linux-s390x@0.19.12':
    '@esbuild/linux-s390x': public
  '@esbuild/linux-x64@0.19.12':
    '@esbuild/linux-x64': public
  '@esbuild/netbsd-arm64@0.25.1':
    '@esbuild/netbsd-arm64': public
  '@esbuild/netbsd-x64@0.19.12':
    '@esbuild/netbsd-x64': public
  '@esbuild/openbsd-arm64@0.25.1':
    '@esbuild/openbsd-arm64': public
  '@esbuild/openbsd-x64@0.19.12':
    '@esbuild/openbsd-x64': public
  '@esbuild/sunos-x64@0.19.12':
    '@esbuild/sunos-x64': public
  '@esbuild/win32-arm64@0.19.12':
    '@esbuild/win32-arm64': public
  '@esbuild/win32-ia32@0.19.12':
    '@esbuild/win32-ia32': public
  '@esbuild/win32-x64@0.19.12':
    '@esbuild/win32-x64': public
  '@eslint-community/eslint-plugin-eslint-comments@4.4.1(eslint@9.22.0(jiti@2.4.2))':
    '@eslint-community/eslint-plugin-eslint-comments': public
  '@eslint-community/eslint-utils@4.5.1(eslint@9.22.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/compat@1.2.7(eslint@9.22.0(jiti@2.4.2))':
    '@eslint/compat': public
  '@eslint/config-array@0.19.2':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.1.0':
    '@eslint/config-helpers': public
  '@eslint/core@0.12.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.0':
    '@eslint/eslintrc': public
  '@eslint/js@9.22.0':
    '@eslint/js': public
  '@eslint/markdown@6.3.0':
    '@eslint/markdown': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.2.7':
    '@eslint/plugin-kit': public
  '@floating-ui/core@1.6.7':
    '@floating-ui/core': public
  '@floating-ui/dom@1.1.1':
    '@floating-ui/dom': public
  '@floating-ui/utils@0.2.7':
    '@floating-ui/utils': public
  '@grpc/grpc-js@1.11.1':
    '@grpc/grpc-js': public
  '@grpc/proto-loader@0.7.13':
    '@grpc/proto-loader': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': public
  '@humanfs/node@0.16.6':
    '@humanfs/node': public
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': public
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': public
  '@iconify-json/carbon@1.2.8':
    '@iconify-json/carbon': public
  '@iconify-json/logos@1.2.4':
    '@iconify-json/logos': public
  '@iconify-json/ri@1.2.5':
    '@iconify-json/ri': public
  '@iconify-json/simple-icons@1.2.28':
    '@iconify-json/simple-icons': public
  '@iconify-json/tabler@1.2.17':
    '@iconify-json/tabler': public
  '@iconify-json/vscode-icons@1.2.16':
    '@iconify-json/vscode-icons': public
  '@iconify/types@2.0.0':
    '@iconify/types': public
  '@iconify/utils@2.3.0':
    '@iconify/utils': public
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': public
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': public
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': public
  '@jimp/bmp@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/bmp': public
  '@jimp/core@0.16.13':
    '@jimp/core': public
  '@jimp/custom@0.16.13':
    '@jimp/custom': public
  '@jimp/gif@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/gif': public
  '@jimp/jpeg@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/jpeg': public
  '@jimp/plugin-blit@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-blit': public
  '@jimp/plugin-blur@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-blur': public
  '@jimp/plugin-circle@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-circle': public
  '@jimp/plugin-color@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-color': public
  '@jimp/plugin-contain@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-blit@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-scale@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13)))':
    '@jimp/plugin-contain': public
  '@jimp/plugin-cover@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-crop@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-scale@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13)))':
    '@jimp/plugin-cover': public
  '@jimp/plugin-crop@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-crop': public
  '@jimp/plugin-displace@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-displace': public
  '@jimp/plugin-dither@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-dither': public
  '@jimp/plugin-fisheye@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-fisheye': public
  '@jimp/plugin-flip@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-rotate@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-blit@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-crop@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13)))':
    '@jimp/plugin-flip': public
  '@jimp/plugin-gaussian@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-gaussian': public
  '@jimp/plugin-invert@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-invert': public
  '@jimp/plugin-mask@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-mask': public
  '@jimp/plugin-normalize@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-normalize': public
  '@jimp/plugin-print@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-blit@0.16.13(@jimp/custom@0.16.13))':
    '@jimp/plugin-print': public
  '@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugin-resize': public
  '@jimp/plugin-rotate@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-blit@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-crop@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13))':
    '@jimp/plugin-rotate': public
  '@jimp/plugin-scale@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13))':
    '@jimp/plugin-scale': public
  '@jimp/plugin-shadow@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-blur@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13))':
    '@jimp/plugin-shadow': public
  '@jimp/plugin-threshold@0.16.13(@jimp/custom@0.16.13)(@jimp/plugin-color@0.16.13(@jimp/custom@0.16.13))(@jimp/plugin-resize@0.16.13(@jimp/custom@0.16.13))':
    '@jimp/plugin-threshold': public
  '@jimp/plugins@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/plugins': public
  '@jimp/png@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/png': public
  '@jimp/tiff@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/tiff': public
  '@jimp/types@0.16.13(@jimp/custom@0.16.13)':
    '@jimp/types': public
  '@jimp/utils@0.16.13':
    '@jimp/utils': public
  '@jridgewell/gen-mapping@0.3.5':
    '@jridgewell/gen-mapping': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': public
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': public
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': public
  '@js-sdsl/ordered-map@4.4.2':
    '@js-sdsl/ordered-map': public
  '@kwsites/file-exists@1.1.1':
    '@kwsites/file-exists': public
  '@kwsites/promise-deferred@1.1.1':
    '@kwsites/promise-deferred': public
  '@mapbox/node-pre-gyp@2.0.0':
    '@mapbox/node-pre-gyp': public
  '@modelcontextprotocol/sdk@1.9.0':
    '@modelcontextprotocol/sdk': public
  '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-arm64': public
  '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-x64': public
  '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-arm64': public
  '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-arm': public
  '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-x64': public
  '@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-win32-x64': public
  '@netlify/functions@3.0.0':
    '@netlify/functions': public
  '@netlify/node-cookies@0.1.0':
    '@netlify/node-cookies': public
  '@netlify/serverless-functions-api@1.30.1':
    '@netlify/serverless-functions-api': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': public
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': public
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': public
  '@nuxt/cli@3.23.0(magicast@0.3.5)':
    '@nuxt/cli': public
  '@nuxt/devalue@2.0.2':
    '@nuxt/devalue': public
  '@nuxt/devtools-kit@1.7.0(magicast@0.3.5)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))':
    '@nuxt/devtools-kit': public
  '@nuxt/devtools-ui-kit@1.7.0(@nuxt/devtools@1.7.0(rollup@4.35.0)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4)))(@unocss/webpack@0.65.4(rollup@4.35.0)(webpack@5.94.0(@swc/core@1.7.22)(esbuild@0.25.1)))(@vue/compiler-core@3.5.13)(axios@1.7.6)(change-case@5.4.4)(fuse.js@7.1.0)(magicast@0.3.5)(nuxt@3.16.0(@parcel/watcher@2.4.1)(@types/node@22.13.10)(db0@0.3.1)(eslint@9.22.0(jiti@2.4.2))(ioredis@5.6.0)(magicast@0.3.5)(optionator@0.9.4)(rollup@4.35.0)(terser@5.31.6)(tsx@4.19.3)(typescript@5.5.4)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(xml2js@0.6.2)(yaml@2.7.0))(postcss@8.5.3)(qrcode@1.5.4)(rollup@4.35.0)(typescript@5.5.4)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4))(webpack@5.94.0(@swc/core@1.7.22)(esbuild@0.25.1))':
    '@nuxt/devtools-ui-kit': public
  '@nuxt/devtools-wizard@1.7.0':
    '@nuxt/devtools-wizard': public
  '@nuxt/devtools@1.7.0(rollup@4.35.0)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4))':
    '@nuxt/devtools': public
  '@nuxt/eslint-config@0.5.7(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4)':
    '@nuxt/eslint-config': public
  '@nuxt/eslint-plugin@0.5.7(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4)':
    '@nuxt/eslint-plugin': public
  '@nuxt/kit@3.16.0(magicast@0.3.5)':
    '@nuxt/kit': public
  '@nuxt/schema@3.16.0':
    '@nuxt/schema': public
  '@nuxt/telemetry@2.6.5(magicast@0.3.5)':
    '@nuxt/telemetry': public
  '@nuxt/test-utils@3.17.2(@types/node@22.13.10)(jiti@2.4.2)(magicast@0.3.5)(terser@5.31.6)(tsx@4.19.3)(typescript@5.5.4)(vitest@2.1.9(@types/node@22.13.10)(terser@5.31.6))(yaml@2.7.0)':
    '@nuxt/test-utils': public
  '@nuxt/vite-builder@3.16.0(@types/node@22.13.10)(eslint@9.22.0(jiti@2.4.2))(magicast@0.3.5)(optionator@0.9.4)(rollup@4.35.0)(terser@5.31.6)(tsx@4.19.3)(typescript@5.5.4)(vue@3.5.13(typescript@5.5.4))(yaml@2.7.0)':
    '@nuxt/vite-builder': public
  '@oxc-parser/binding-darwin-arm64@0.56.5':
    '@oxc-parser/binding-darwin-arm64': public
  '@oxc-parser/binding-darwin-x64@0.56.5':
    '@oxc-parser/binding-darwin-x64': public
  '@oxc-parser/binding-linux-arm-gnueabihf@0.56.5':
    '@oxc-parser/binding-linux-arm-gnueabihf': public
  '@oxc-parser/binding-linux-arm64-gnu@0.56.5':
    '@oxc-parser/binding-linux-arm64-gnu': public
  '@oxc-parser/binding-linux-arm64-musl@0.56.5':
    '@oxc-parser/binding-linux-arm64-musl': public
  '@oxc-parser/binding-linux-x64-gnu@0.56.5':
    '@oxc-parser/binding-linux-x64-gnu': public
  '@oxc-parser/binding-linux-x64-musl@0.56.5':
    '@oxc-parser/binding-linux-x64-musl': public
  '@oxc-parser/binding-wasm32-wasi@0.56.5':
    '@oxc-parser/binding-wasm32-wasi': public
  '@oxc-parser/binding-win32-arm64-msvc@0.56.5':
    '@oxc-parser/binding-win32-arm64-msvc': public
  '@oxc-parser/binding-win32-x64-msvc@0.56.5':
    '@oxc-parser/binding-win32-x64-msvc': public
  '@oxc-parser/wasm@0.56.5':
    '@oxc-parser/wasm': public
  '@oxc-project/types@0.56.5':
    '@oxc-project/types': public
  '@parcel/watcher-android-arm64@2.4.1':
    '@parcel/watcher-android-arm64': public
  '@parcel/watcher-darwin-arm64@2.4.1':
    '@parcel/watcher-darwin-arm64': public
  '@parcel/watcher-darwin-x64@2.4.1':
    '@parcel/watcher-darwin-x64': public
  '@parcel/watcher-freebsd-x64@2.4.1':
    '@parcel/watcher-freebsd-x64': public
  '@parcel/watcher-linux-arm-glibc@2.4.1':
    '@parcel/watcher-linux-arm-glibc': public
  '@parcel/watcher-linux-arm64-glibc@2.4.1':
    '@parcel/watcher-linux-arm64-glibc': public
  '@parcel/watcher-linux-arm64-musl@2.4.1':
    '@parcel/watcher-linux-arm64-musl': public
  '@parcel/watcher-linux-x64-glibc@2.4.1':
    '@parcel/watcher-linux-x64-glibc': public
  '@parcel/watcher-linux-x64-musl@2.4.1':
    '@parcel/watcher-linux-x64-musl': public
  '@parcel/watcher-wasm@2.4.1':
    '@parcel/watcher-wasm': public
  '@parcel/watcher-win32-arm64@2.4.1':
    '@parcel/watcher-win32-arm64': public
  '@parcel/watcher-win32-ia32@2.4.1':
    '@parcel/watcher-win32-ia32': public
  '@parcel/watcher-win32-x64@2.4.1':
    '@parcel/watcher-win32-x64': public
  '@parcel/watcher@2.4.1':
    '@parcel/watcher': public
  '@pipeletteio/nop@1.0.5':
    '@pipeletteio/nop': public
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': public
  '@pkgr/core@0.1.1':
    '@pkgr/core': public
  '@polka/url@1.0.0-next.25':
    '@polka/url': public
  '@poppinss/colors@4.1.4':
    '@poppinss/colors': public
  '@poppinss/dumper@0.6.3':
    '@poppinss/dumper': public
  '@poppinss/exception@1.2.1':
    '@poppinss/exception': public
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': public
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': public
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': public
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': public
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': public
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': public
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': public
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': public
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': public
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': public
  '@quansync/fs@0.1.1':
    '@quansync/fs': public
  '@redocly/ajv@8.11.2':
    '@redocly/ajv': public
  '@redocly/config@0.22.1':
    '@redocly/config': public
  '@redocly/openapi-core@1.33.1(supports-color@9.4.0)':
    '@redocly/openapi-core': public
  '@rollup/plugin-alias@5.1.0(rollup@3.29.4)':
    '@rollup/plugin-alias': public
  '@rollup/plugin-commonjs@25.0.8(rollup@3.29.4)':
    '@rollup/plugin-commonjs': public
  '@rollup/plugin-inject@5.0.5(rollup@4.35.0)':
    '@rollup/plugin-inject': public
  '@rollup/plugin-json@6.1.0(rollup@3.29.4)':
    '@rollup/plugin-json': public
  '@rollup/plugin-node-resolve@15.2.3(rollup@3.29.4)':
    '@rollup/plugin-node-resolve': public
  '@rollup/plugin-replace@5.0.7(rollup@3.29.4)':
    '@rollup/plugin-replace': public
  '@rollup/plugin-terser@0.4.4(rollup@4.35.0)':
    '@rollup/plugin-terser': public
  '@rollup/pluginutils@5.1.0(rollup@3.29.4)':
    '@rollup/pluginutils': public
  '@rollup/rollup-android-arm-eabi@4.21.2':
    '@rollup/rollup-android-arm-eabi': public
  '@rollup/rollup-android-arm64@4.21.2':
    '@rollup/rollup-android-arm64': public
  '@rollup/rollup-darwin-arm64@4.21.2':
    '@rollup/rollup-darwin-arm64': public
  '@rollup/rollup-darwin-x64@4.21.2':
    '@rollup/rollup-darwin-x64': public
  '@rollup/rollup-freebsd-arm64@4.35.0':
    '@rollup/rollup-freebsd-arm64': public
  '@rollup/rollup-freebsd-x64@4.35.0':
    '@rollup/rollup-freebsd-x64': public
  '@rollup/rollup-linux-arm-gnueabihf@4.21.2':
    '@rollup/rollup-linux-arm-gnueabihf': public
  '@rollup/rollup-linux-arm-musleabihf@4.21.2':
    '@rollup/rollup-linux-arm-musleabihf': public
  '@rollup/rollup-linux-arm64-gnu@4.21.2':
    '@rollup/rollup-linux-arm64-gnu': public
  '@rollup/rollup-linux-arm64-musl@4.21.2':
    '@rollup/rollup-linux-arm64-musl': public
  '@rollup/rollup-linux-loongarch64-gnu@4.35.0':
    '@rollup/rollup-linux-loongarch64-gnu': public
  '@rollup/rollup-linux-powerpc64le-gnu@4.21.2':
    '@rollup/rollup-linux-powerpc64le-gnu': public
  '@rollup/rollup-linux-riscv64-gnu@4.21.2':
    '@rollup/rollup-linux-riscv64-gnu': public
  '@rollup/rollup-linux-s390x-gnu@4.21.2':
    '@rollup/rollup-linux-s390x-gnu': public
  '@rollup/rollup-linux-x64-gnu@4.21.2':
    '@rollup/rollup-linux-x64-gnu': public
  '@rollup/rollup-linux-x64-musl@4.21.2':
    '@rollup/rollup-linux-x64-musl': public
  '@rollup/rollup-win32-arm64-msvc@4.21.2':
    '@rollup/rollup-win32-arm64-msvc': public
  '@rollup/rollup-win32-ia32-msvc@4.21.2':
    '@rollup/rollup-win32-ia32-msvc': public
  '@rollup/rollup-win32-x64-msvc@4.21.2':
    '@rollup/rollup-win32-x64-msvc': public
  '@rustup/nng-darwin-arm64@0.1.2':
    '@rustup/nng-darwin-arm64': public
  '@rustup/nng-darwin-universal@0.1.2':
    '@rustup/nng-darwin-universal': public
  '@rustup/nng-darwin-x64@0.1.2':
    '@rustup/nng-darwin-x64': public
  '@rustup/nng-linux-x64-gnu@0.1.2':
    '@rustup/nng-linux-x64-gnu': public
  '@rustup/nng-win32-x64-msvc@0.1.2':
    '@rustup/nng-win32-x64-msvc': public
  '@rustup/nng@0.1.2':
    '@rustup/nng': public
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': public
  '@shikijs/core@2.5.0':
    '@shikijs/core': public
  '@shikijs/engine-javascript@2.5.0':
    '@shikijs/engine-javascript': public
  '@shikijs/engine-oniguruma@2.5.0':
    '@shikijs/engine-oniguruma': public
  '@shikijs/langs@1.29.2':
    '@shikijs/langs': public
  '@shikijs/themes@1.29.2':
    '@shikijs/themes': public
  '@shikijs/transformers@2.5.0':
    '@shikijs/transformers': public
  '@shikijs/twoslash@3.2.1(typescript@5.5.4)':
    '@shikijs/twoslash': public
  '@shikijs/types@2.5.0':
    '@shikijs/types': public
  '@shikijs/vitepress-twoslash@1.29.2(@nuxt/kit@3.16.0(magicast@0.3.5))(typescript@5.5.4)':
    '@shikijs/vitepress-twoslash': public
  '@shikijs/vscode-textmate@10.0.2':
    '@shikijs/vscode-textmate': public
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': public
  '@sindresorhus/merge-streams@2.3.0':
    '@sindresorhus/merge-streams': public
  '@speed-highlight/core@1.2.7':
    '@speed-highlight/core': public
  '@stylistic/eslint-plugin@2.13.0(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4)':
    '@stylistic/eslint-plugin': public
  '@swc/core-darwin-arm64@1.7.22':
    '@swc/core-darwin-arm64': public
  '@swc/core-darwin-x64@1.7.22':
    '@swc/core-darwin-x64': public
  '@swc/core-linux-arm-gnueabihf@1.7.22':
    '@swc/core-linux-arm-gnueabihf': public
  '@swc/core-linux-arm64-gnu@1.7.22':
    '@swc/core-linux-arm64-gnu': public
  '@swc/core-linux-arm64-musl@1.7.22':
    '@swc/core-linux-arm64-musl': public
  '@swc/core-linux-x64-gnu@1.7.22':
    '@swc/core-linux-x64-gnu': public
  '@swc/core-linux-x64-musl@1.7.22':
    '@swc/core-linux-x64-musl': public
  '@swc/core-win32-arm64-msvc@1.7.22':
    '@swc/core-win32-arm64-msvc': public
  '@swc/core-win32-ia32-msvc@1.7.22':
    '@swc/core-win32-ia32-msvc': public
  '@swc/core-win32-x64-msvc@1.7.22':
    '@swc/core-win32-x64-msvc': public
  '@swc/core@1.7.22':
    '@swc/core': public
  '@swc/counter@0.1.3':
    '@swc/counter': public
  '@swc/types@0.1.12':
    '@swc/types': public
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': public
  '@terascope/fetch-github-release@1.0.0':
    '@terascope/fetch-github-release': public
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': public
  '@trysound/sax@0.2.0':
    '@trysound/sax': public
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': public
  '@types/debug@4.1.12':
    '@types/debug': public
  '@types/doctrine@0.0.9':
    '@types/doctrine': public
  '@types/eslint@9.6.1':
    '@types/eslint': public
  '@types/estree@1.0.6':
    '@types/estree': public
  '@types/hast@3.0.4':
    '@types/hast': public
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': public
  '@types/http-proxy@1.17.16':
    '@types/http-proxy': public
  '@types/json-schema@7.0.15':
    '@types/json-schema': public
  '@types/jsonfile@6.1.4':
    '@types/jsonfile': public
  '@types/keyv@3.1.4':
    '@types/keyv': public
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': public
  '@types/markdown-it@14.1.2':
    '@types/markdown-it': public
  '@types/mdast@4.0.4':
    '@types/mdast': public
  '@types/mdurl@2.0.0':
    '@types/mdurl': public
  '@types/ms@0.7.34':
    '@types/ms': public
  '@types/node-fetch@2.6.11':
    '@types/node-fetch': public
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': public
  '@types/parse-path@7.0.3':
    '@types/parse-path': public
  '@types/resolve@1.20.2':
    '@types/resolve': public
  '@types/responselike@1.0.3':
    '@types/responselike': public
  '@types/retry@0.12.2':
    '@types/retry': public
  '@types/unist@3.0.3':
    '@types/unist': public
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': public
  '@types/xml2js@0.4.14':
    '@types/xml2js': public
  '@types/yauzl@2.10.3':
    '@types/yauzl': public
  '@typescript-eslint/eslint-plugin@8.26.1(@typescript-eslint/parser@8.26.1(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4))(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.26.1(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@8.26.1':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@8.26.1(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.26.1':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.26.1(typescript@5.5.4)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.26.1(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.26.1':
    '@typescript-eslint/visitor-keys': public
  '@typescript/vfs@1.6.0(typescript@5.5.4)':
    '@typescript/vfs': public
  '@ungap/structured-clone@1.2.0':
    '@ungap/structured-clone': public
  '@unhead/vue@2.0.0-rc.13(vue@3.5.13(typescript@5.5.4))':
    '@unhead/vue': public
  '@unocss/astro@0.65.4(rollup@4.35.0)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4))':
    '@unocss/astro': public
  '@unocss/cli@0.65.4(rollup@4.35.0)':
    '@unocss/cli': public
  '@unocss/config@0.65.4':
    '@unocss/config': public
  '@unocss/core@0.65.4':
    '@unocss/core': public
  '@unocss/extractor-arbitrary-variants@0.65.4':
    '@unocss/extractor-arbitrary-variants': public
  '@unocss/inspector@0.65.4(vue@3.5.13(typescript@5.5.4))':
    '@unocss/inspector': public
  '@unocss/nuxt@0.65.4(magicast@0.3.5)(postcss@8.5.3)(rollup@4.35.0)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4))(webpack@5.94.0(@swc/core@1.7.22)(esbuild@0.25.1))':
    '@unocss/nuxt': public
  '@unocss/postcss@0.65.4(postcss@8.5.3)':
    '@unocss/postcss': public
  '@unocss/preset-attributify@0.65.4':
    '@unocss/preset-attributify': public
  '@unocss/preset-icons@0.65.4':
    '@unocss/preset-icons': public
  '@unocss/preset-mini@0.65.4':
    '@unocss/preset-mini': public
  '@unocss/preset-tagify@0.65.4':
    '@unocss/preset-tagify': public
  '@unocss/preset-typography@0.65.4':
    '@unocss/preset-typography': public
  '@unocss/preset-uno@0.65.4':
    '@unocss/preset-uno': public
  '@unocss/preset-web-fonts@0.65.4':
    '@unocss/preset-web-fonts': public
  '@unocss/preset-wind@0.65.4':
    '@unocss/preset-wind': public
  '@unocss/reset@0.65.4':
    '@unocss/reset': public
  '@unocss/rule-utils@0.65.4':
    '@unocss/rule-utils': public
  '@unocss/transformer-attributify-jsx@0.65.4':
    '@unocss/transformer-attributify-jsx': public
  '@unocss/transformer-compile-class@0.65.4':
    '@unocss/transformer-compile-class': public
  '@unocss/transformer-directives@0.65.4':
    '@unocss/transformer-directives': public
  '@unocss/transformer-variant-group@0.65.4':
    '@unocss/transformer-variant-group': public
  '@unocss/vite@0.65.4(rollup@4.35.0)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4))':
    '@unocss/vite': public
  '@unocss/webpack@0.65.4(rollup@4.35.0)(webpack@5.94.0(@swc/core@1.7.22)(esbuild@0.25.1))':
    '@unocss/webpack': public
  '@unrs/rspack-resolver-binding-darwin-arm64@1.1.2':
    '@unrs/rspack-resolver-binding-darwin-arm64': public
  '@unrs/rspack-resolver-binding-darwin-x64@1.1.2':
    '@unrs/rspack-resolver-binding-darwin-x64': public
  '@unrs/rspack-resolver-binding-freebsd-x64@1.1.2':
    '@unrs/rspack-resolver-binding-freebsd-x64': public
  '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.1.2':
    '@unrs/rspack-resolver-binding-linux-arm-gnueabihf': public
  '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.1.2':
    '@unrs/rspack-resolver-binding-linux-arm64-gnu': public
  '@unrs/rspack-resolver-binding-linux-arm64-musl@1.1.2':
    '@unrs/rspack-resolver-binding-linux-arm64-musl': public
  '@unrs/rspack-resolver-binding-linux-x64-gnu@1.1.2':
    '@unrs/rspack-resolver-binding-linux-x64-gnu': public
  '@unrs/rspack-resolver-binding-linux-x64-musl@1.1.2':
    '@unrs/rspack-resolver-binding-linux-x64-musl': public
  '@unrs/rspack-resolver-binding-wasm32-wasi@1.1.2':
    '@unrs/rspack-resolver-binding-wasm32-wasi': public
  '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.1.2':
    '@unrs/rspack-resolver-binding-win32-arm64-msvc': public
  '@unrs/rspack-resolver-binding-win32-x64-msvc@1.1.2':
    '@unrs/rspack-resolver-binding-win32-x64-msvc': public
  '@vercel/nft@0.29.2(rollup@4.35.0)':
    '@vercel/nft': public
  '@vitejs/plugin-vue-jsx@4.1.2(vite@6.2.2(@types/node@22.13.10)(jiti@2.4.2)(terser@5.31.6)(tsx@4.19.3)(yaml@2.7.0))(vue@3.5.13(typescript@5.5.4))':
    '@vitejs/plugin-vue-jsx': public
  '@vitejs/plugin-vue@5.2.2(vite@5.4.14(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4))':
    '@vitejs/plugin-vue': public
  '@vitest/eslint-plugin@1.1.37(@typescript-eslint/utils@8.26.1(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4))(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4)(vitest@2.1.9(@types/node@22.13.10)(terser@5.31.6))':
    '@vitest/eslint-plugin': public
  '@vitest/expect@2.1.9':
    '@vitest/expect': public
  '@vitest/mocker@2.1.9(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))':
    '@vitest/mocker': public
  '@vitest/pretty-format@2.1.9':
    '@vitest/pretty-format': public
  '@vitest/runner@2.1.9':
    '@vitest/runner': public
  '@vitest/snapshot@2.1.9':
    '@vitest/snapshot': public
  '@vitest/spy@2.1.9':
    '@vitest/spy': public
  '@vitest/utils@2.1.9':
    '@vitest/utils': public
  '@volar/language-core@2.4.12':
    '@volar/language-core': public
  '@volar/source-map@2.4.12':
    '@volar/source-map': public
  '@vue-macros/common@1.16.1(vue@3.5.13(typescript@5.5.4))':
    '@vue-macros/common': public
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': public
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.26.10)':
    '@vue/babel-plugin-jsx': public
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.26.10)':
    '@vue/babel-plugin-resolve-type': public
  '@vue/compiler-core@3.5.13':
    '@vue/compiler-core': public
  '@vue/compiler-dom@3.5.13':
    '@vue/compiler-dom': public
  '@vue/compiler-sfc@3.5.13':
    '@vue/compiler-sfc': public
  '@vue/compiler-ssr@3.5.13':
    '@vue/compiler-ssr': public
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': public
  '@vue/devtools-api@7.7.2':
    '@vue/devtools-api': public
  '@vue/devtools-core@7.6.8(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4))':
    '@vue/devtools-core': public
  '@vue/devtools-kit@7.6.8':
    '@vue/devtools-kit': public
  '@vue/devtools-shared@7.7.2':
    '@vue/devtools-shared': public
  '@vue/language-core@2.1.10(typescript@5.5.4)':
    '@vue/language-core': public
  '@vue/reactivity@3.5.13':
    '@vue/reactivity': public
  '@vue/runtime-core@3.5.13':
    '@vue/runtime-core': public
  '@vue/runtime-dom@3.5.13':
    '@vue/runtime-dom': public
  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.5.4))':
    '@vue/server-renderer': public
  '@vue/shared@3.5.13':
    '@vue/shared': public
  '@vueuse/core@12.8.2(typescript@5.5.4)':
    '@vueuse/core': public
  '@vueuse/integrations@12.8.2(axios@1.7.6)(change-case@5.4.4)(focus-trap@7.6.4)(fuse.js@7.1.0)(qrcode@1.5.4)(typescript@5.5.4)':
    '@vueuse/integrations': public
  '@vueuse/metadata@12.8.2':
    '@vueuse/metadata': public
  '@vueuse/nuxt@12.8.2(magicast@0.3.5)(nuxt@3.16.0(@parcel/watcher@2.4.1)(@types/node@22.13.10)(db0@0.3.1)(eslint@9.22.0(jiti@2.4.2))(ioredis@5.6.0)(magicast@0.3.5)(optionator@0.9.4)(rollup@4.35.0)(terser@5.31.6)(tsx@4.19.3)(typescript@5.5.4)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(xml2js@0.6.2)(yaml@2.7.0))(typescript@5.5.4)':
    '@vueuse/nuxt': public
  '@vueuse/shared@12.8.2(typescript@5.5.4)':
    '@vueuse/shared': public
  '@webassemblyjs/ast@1.12.1':
    '@webassemblyjs/ast': public
  '@webassemblyjs/floating-point-hex-parser@1.11.6':
    '@webassemblyjs/floating-point-hex-parser': public
  '@webassemblyjs/helper-api-error@1.11.6':
    '@webassemblyjs/helper-api-error': public
  '@webassemblyjs/helper-buffer@1.12.1':
    '@webassemblyjs/helper-buffer': public
  '@webassemblyjs/helper-numbers@1.11.6':
    '@webassemblyjs/helper-numbers': public
  '@webassemblyjs/helper-wasm-bytecode@1.11.6':
    '@webassemblyjs/helper-wasm-bytecode': public
  '@webassemblyjs/helper-wasm-section@1.12.1':
    '@webassemblyjs/helper-wasm-section': public
  '@webassemblyjs/ieee754@1.11.6':
    '@webassemblyjs/ieee754': public
  '@webassemblyjs/leb128@1.11.6':
    '@webassemblyjs/leb128': public
  '@webassemblyjs/utf8@1.11.6':
    '@webassemblyjs/utf8': public
  '@webassemblyjs/wasm-edit@1.12.1':
    '@webassemblyjs/wasm-edit': public
  '@webassemblyjs/wasm-gen@1.12.1':
    '@webassemblyjs/wasm-gen': public
  '@webassemblyjs/wasm-opt@1.12.1':
    '@webassemblyjs/wasm-opt': public
  '@webassemblyjs/wasm-parser@1.12.1':
    '@webassemblyjs/wasm-parser': public
  '@webassemblyjs/wast-printer@1.12.1':
    '@webassemblyjs/wast-printer': public
  '@xterm/addon-fit@0.10.0(@xterm/xterm@5.5.0)':
    '@xterm/addon-fit': public
  '@xterm/xterm@5.5.0':
    '@xterm/xterm': public
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': public
  '@xtuc/long@4.2.2':
    '@xtuc/long': public
  abbrev@3.0.0:
    abbrev: public
  abort-controller@3.0.0:
    abort-controller: public
  abstract-leveldown@7.2.0:
    abstract-leveldown: public
  accepts@2.0.0:
    accepts: public
  acorn-import-attributes@1.9.5(acorn@8.14.1):
    acorn-import-attributes: public
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: public
  acorn@8.14.1:
    acorn: public
  ag-charts-types@10.3.4:
    ag-charts-types: public
  ag-grid-community@32.3.4:
    ag-grid-community: public
  ag-grid-vue3@32.3.4(typescript@5.5.4):
    ag-grid-vue3: public
  agent-base@7.1.3:
    agent-base: public
  agentkeepalive@4.5.0:
    agentkeepalive: public
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: public
  ajv@6.12.6:
    ajv: public
  algoliasearch@5.21.0:
    algoliasearch: public
  alien-signals@0.2.2:
    alien-signals: public
  ansi-colors@4.1.3:
    ansi-colors: public
  ansi-regex@5.0.1:
    ansi-regex: public
  ansi-styles@4.3.0:
    ansi-styles: public
  ansis@3.17.0:
    ansis: public
  antlr4-c3@3.3.7(antlr4ng-cli@1.0.7):
    antlr4-c3: public
  antlr4ng-cli@1.0.7:
    antlr4ng-cli: public
  antlr4ng@2.0.11(antlr4ng-cli@1.0.7):
    antlr4ng: public
  any-base@1.1.0:
    any-base: public
  anymatch@3.1.3:
    anymatch: public
  archiver-utils@5.0.2:
    archiver-utils: public
  archiver@7.0.1:
    archiver: public
  are-docs-informative@0.0.2:
    are-docs-informative: public
  argparse@2.0.1:
    argparse: public
  asn1@0.2.6:
    asn1: public
  assert-plus@1.0.0:
    assert-plus: public
  assertion-error@2.0.1:
    assertion-error: public
  ast-kit@1.4.2:
    ast-kit: public
  ast-walker-scope@0.6.2:
    ast-walker-scope: public
  async-map-like@1.0.2:
    async-map-like: public
  async-sema@3.1.1:
    async-sema: public
  async@3.2.6:
    async: public
  asynckit@0.4.0:
    asynckit: public
  autoprefixer@10.4.20(postcss@8.5.3):
    autoprefixer: public
  aws-sign2@0.7.0:
    aws-sign2: public
  aws4@1.13.2:
    aws4: public
  axios@1.7.6(debug@2.6.9):
    axios: public
  b4a@1.6.6:
    b4a: public
  balanced-match@1.0.2:
    balanced-match: public
  bare-events@2.4.2:
    bare-events: public
  base64-js@1.5.1:
    base64-js: public
  bcrypt-pbkdf@1.0.2:
    bcrypt-pbkdf: public
  binary-extensions@2.3.0:
    binary-extensions: public
  bindings@1.5.0:
    bindings: public
  birpc@0.2.19:
    birpc: public
  bl@1.2.3:
    bl: public
  bmp-js@0.1.0:
    bmp-js: public
  body-parser@2.2.0:
    body-parser: public
  boolbase@1.0.0:
    boolbase: public
  brace-expansion@1.1.11:
    brace-expansion: public
  braces@3.0.3:
    braces: public
  brolog@1.14.2:
    brolog: public
  browserslist@4.23.3:
    browserslist: public
  buffer-crc32@1.0.0:
    buffer-crc32: public
  buffer-equal@0.0.1:
    buffer-equal: public
  buffer-from@1.1.2:
    buffer-from: public
  buffer@5.7.1:
    buffer: public
  builtin-modules@3.3.0:
    builtin-modules: public
  bullmq@5.43.1:
    bullmq: public
  bundle-name@4.1.0:
    bundle-name: public
  bundle-require@5.1.0(esbuild@0.25.1):
    bundle-require: public
  bytes@3.1.2:
    bytes: public
  c12@3.0.2(magicast@0.3.5):
    c12: public
  cac@6.7.14:
    cac: public
  cacheable-lookup@5.0.4:
    cacheable-lookup: public
  cacheable-request@7.0.4:
    cacheable-request: public
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: public
  call-bound@1.0.4:
    call-bound: public
  callsites@3.1.0:
    callsites: public
  camelcase@5.3.1:
    camelcase: public
  caniuse-api@3.0.0:
    caniuse-api: public
  caniuse-lite@1.0.30001653:
    caniuse-lite: public
  caseless@0.12.0:
    caseless: public
  catering@2.1.1:
    catering: public
  ccount@2.0.1:
    ccount: public
  centra@2.7.0:
    centra: public
  chai@5.2.0:
    chai: public
  chalk@4.1.2:
    chalk: public
  change-case@5.4.4:
    change-case: public
  character-entities-html4@2.1.0:
    character-entities-html4: public
  character-entities-legacy@3.0.0:
    character-entities-legacy: public
  character-entities@2.0.2:
    character-entities: public
  check-error@2.1.1:
    check-error: public
  cheerio@0.22.0:
    cheerio: public
  chokidar@4.0.3:
    chokidar: public
  chownr@2.0.0:
    chownr: public
  chrome-trace-event@1.0.4:
    chrome-trace-event: public
  ci-info@4.0.0:
    ci-info: public
  citty@0.1.6:
    citty: public
  clean-regexp@1.0.0:
    clean-regexp: public
  clipboardy@4.0.0:
    clipboardy: public
  cliui@8.0.1:
    cliui: public
  clone-class@1.0.3:
    clone-class: public
  clone-response@1.0.3:
    clone-response: public
  cluster-key-slot@1.1.2:
    cluster-key-slot: public
  cmd-ts@0.10.2:
    cmd-ts: public
  cockatiel@2.0.2:
    cockatiel: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  colord@2.9.3:
    colord: public
  colorette@2.0.19:
    colorette: public
  combined-stream@1.0.8:
    combined-stream: public
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: public
  commander@10.0.1:
    commander: public
  comment-parser@1.4.1:
    comment-parser: public
  commondir@1.0.1:
    commondir: public
  compatx@0.1.8:
    compatx: public
  compress-commons@6.0.2:
    compress-commons: public
  concat-map@0.0.1:
    concat-map: public
  confbox@0.1.8:
    confbox: public
  consola@3.2.3:
    consola: public
  content-disposition@1.0.0:
    content-disposition: public
  content-type@1.0.5:
    content-type: public
  convert-source-map@2.0.0:
    convert-source-map: public
  cookie-es@2.0.0:
    cookie-es: public
  cookie-signature@1.2.2:
    cookie-signature: public
  cookie@0.7.2:
    cookie: public
  copy-anything@3.0.5:
    copy-anything: public
  core-js-compat@3.38.1:
    core-js-compat: public
  core-util-is@1.0.3:
    core-util-is: public
  cors@2.8.5:
    cors: public
  crc-32@1.2.2:
    crc-32: public
  crc32-stream@6.0.0:
    crc32-stream: public
  cron-parser@4.9.0:
    cron-parser: public
  croner@9.0.0:
    croner: public
  cronstrue@2.56.0:
    cronstrue: public
  cross-spawn@7.0.6:
    cross-spawn: public
  crossws@0.3.4:
    crossws: public
  css-declaration-sorter@7.2.0(postcss@8.4.41):
    css-declaration-sorter: public
  css-select@1.2.0:
    css-select: public
  css-tree@3.1.0:
    css-tree: public
  css-what@2.1.3:
    css-what: public
  cssesc@3.0.0:
    cssesc: public
  cssnano-preset-default@7.0.5(postcss@8.4.41):
    cssnano-preset-default: public
  cssnano-utils@5.0.0(postcss@8.4.41):
    cssnano-utils: public
  cssnano@7.0.6(postcss@8.5.3):
    cssnano: public
  csso@5.0.5:
    csso: public
  csstype@3.1.3:
    csstype: public
  cuid@2.1.8:
    cuid: public
  dashdash@1.14.1:
    dashdash: public
  dayjs@1.11.13:
    dayjs: public
  db0@0.3.1:
    db0: public
  de-indent@1.0.2:
    de-indent: public
  debug@4.4.0(supports-color@9.4.0):
    debug: public
  decamelize@1.2.0:
    decamelize: public
  decode-named-character-reference@1.0.2:
    decode-named-character-reference: public
  decompress-response@6.0.0:
    decompress-response: public
  deep-eql@5.0.2:
    deep-eql: public
  deep-is@0.1.4:
    deep-is: public
  deepmerge@4.3.1:
    deepmerge: public
  default-browser-id@5.0.0:
    default-browser-id: public
  default-browser@5.2.1:
    default-browser: public
  defer-to-connect@2.0.1:
    defer-to-connect: public
  deferred-leveldown@7.0.0:
    deferred-leveldown: public
  define-lazy-prop@3.0.0:
    define-lazy-prop: public
  defu@6.1.4:
    defu: public
  delayed-stream@1.0.0:
    delayed-stream: public
  denque@2.1.0:
    denque: public
  depd@2.0.0:
    depd: public
  dequal@2.0.3:
    dequal: public
  destr@2.0.3:
    destr: public
  destroy@1.2.0:
    destroy: public
  detect-libc@1.0.3:
    detect-libc: public
  devalue@5.1.1:
    devalue: public
  devlop@1.1.0:
    devlop: public
  didyoumean@1.2.2:
    didyoumean: public
  diff@7.0.0:
    diff: public
  dijkstrajs@1.0.3:
    dijkstrajs: public
  dir-glob@3.0.1:
    dir-glob: public
  docs:
    docs: public
  doctrine@3.0.0:
    doctrine: public
  dom-serializer@0.1.1:
    dom-serializer: public
  dom-walk@0.1.2:
    dom-walk: public
  domelementtype@1.3.1:
    domelementtype: public
  domhandler@2.4.2:
    domhandler: public
  domutils@1.5.1:
    domutils: public
  dot-prop@9.0.0:
    dot-prop: public
  dotenv@16.4.5:
    dotenv: public
  dt-sql-parser@4.0.2(antlr4ng-cli@1.0.7):
    dt-sql-parser: public
  ducks@1.0.2(redux-observable@2.0.0(redux@4.2.1))(redux@4.2.1):
    ducks: public
  dunder-proto@1.0.1:
    dunder-proto: public
  duplexer@0.1.2:
    duplexer: public
  eastasianwidth@0.2.0:
    eastasianwidth: public
  ecc-jsbn@0.1.2:
    ecc-jsbn: public
  ee-first@1.1.1:
    ee-first: public
  electron-to-chromium@1.5.13:
    electron-to-chromium: public
  emoji-regex-xs@1.0.0:
    emoji-regex-xs: public
  emoji-regex@8.0.0:
    emoji-regex: public
  encodeurl@2.0.0:
    encodeurl: public
  encoding-down@7.1.0:
    encoding-down: public
  end-of-stream@1.4.4:
    end-of-stream: public
  enhanced-resolve@5.17.1:
    enhanced-resolve: public
  entities@1.1.2:
    entities: public
  err-code@2.0.3:
    err-code: public
  error-ex@1.3.2:
    error-ex: public
  error-stack-parser-es@0.1.5:
    error-stack-parser-es: public
  errx@0.1.0:
    errx: public
  es-define-property@1.0.1:
    es-define-property: public
  es-errors@1.3.0:
    es-errors: public
  es-module-lexer@1.5.4:
    es-module-lexer: public
  es-object-atoms@1.1.1:
    es-object-atoms: public
  esbuild@0.25.1:
    esbuild: public
  escalade@3.2.0:
    escalade: public
  escape-html@1.0.3:
    escape-html: public
  escape-string-regexp@4.0.0:
    escape-string-regexp: public
  eslint-compat-utils@0.6.4(eslint@9.22.0(jiti@2.4.2)):
    eslint-compat-utils: public
  eslint-config-flat-gitignore@1.0.1(eslint@9.22.0(jiti@2.4.2)):
    eslint-config-flat-gitignore: public
  eslint-flat-config-utils@1.1.0:
    eslint-flat-config-utils: public
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-json-compat-utils@0.2.1(eslint@9.22.0(jiti@2.4.2))(jsonc-eslint-parser@2.4.0):
    eslint-json-compat-utils: public
  eslint-merge-processors@1.0.0(eslint@9.22.0(jiti@2.4.2)):
    eslint-merge-processors: public
  eslint-plugin-antfu@2.7.0(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-antfu: public
  eslint-plugin-command@2.1.0(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-command: public
  eslint-plugin-es-x@7.8.0(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-es-x: public
  eslint-plugin-import-x@4.8.0(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4):
    eslint-plugin-import-x: public
  eslint-plugin-jsdoc@50.6.6(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-jsdoc: public
  eslint-plugin-jsonc@2.19.1(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-jsonc: public
  eslint-plugin-n@17.16.2(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-n: public
  eslint-plugin-no-only-tests@3.3.0:
    eslint-plugin-no-only-tests: public
  eslint-plugin-perfectionist@4.10.1(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4):
    eslint-plugin-perfectionist: public
  eslint-plugin-regexp@2.7.0(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-regexp: public
  eslint-plugin-toml@0.12.0(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-toml: public
  eslint-plugin-unicorn@56.0.1(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-unicorn: public
  eslint-plugin-unused-imports@4.1.4(@typescript-eslint/eslint-plugin@8.26.1(@typescript-eslint/parser@8.26.1(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4))(eslint@9.22.0(jiti@2.4.2))(typescript@5.5.4))(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-unused-imports: public
  eslint-plugin-vue@9.33.0(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-vue: public
  eslint-plugin-yml@1.17.0(eslint@9.22.0(jiti@2.4.2)):
    eslint-plugin-yml: public
  eslint-processor-vue-blocks@1.0.0(@vue/compiler-sfc@3.5.13)(eslint@9.22.0(jiti@2.4.2)):
    eslint-processor-vue-blocks: public
  eslint-scope@8.3.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: public
  esm@3.2.25:
    esm: public
  espree@10.3.0:
    espree: public
  esquery@1.6.0:
    esquery: public
  esrecurse@4.3.0:
    esrecurse: public
  estraverse@5.3.0:
    estraverse: public
  estree-walker@3.0.3:
    estree-walker: public
  esutils@2.0.3:
    esutils: public
  etag@1.8.1:
    etag: public
  event-target-shim@5.0.1:
    event-target-shim: public
  events@3.3.0:
    events: public
  eventsource-parser@3.0.1:
    eventsource-parser: public
  eventsource@3.0.6:
    eventsource: public
  exif-parser@0.1.12:
    exif-parser: public
  expect-type@1.2.0:
    expect-type: public
  express-rate-limit@7.5.0(express@5.1.0):
    express-rate-limit: public
  express@5.1.0:
    express: public
  exsolve@1.0.4:
    exsolve: public
  extend@3.0.2:
    extend: public
  externality@1.0.2:
    externality: public
  extract-zip@2.0.1:
    extract-zip: public
  extsprintf@1.3.0:
    extsprintf: public
  fake-indexeddb@6.0.0:
    fake-indexeddb: public
  fast-deep-equal@3.1.3:
    fast-deep-equal: public
  fast-fifo@1.3.2:
    fast-fifo: public
  fast-glob@3.3.3:
    fast-glob: public
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: public
  fast-levenshtein@2.0.6:
    fast-levenshtein: public
  fast-npm-meta@0.2.2:
    fast-npm-meta: public
  fast-xml-parser@3.21.1:
    fast-xml-parser: public
  fastq@1.17.1:
    fastq: public
  fd-slicer@1.1.0:
    fd-slicer: public
  fdir@6.4.3(picomatch@4.0.2):
    fdir: public
  figures@6.1.0:
    figures: public
  file-box@1.4.15:
    file-box: public
  file-entry-cache@8.0.0:
    file-entry-cache: public
  file-type@16.5.4:
    file-type: public
  file-uri-to-path@1.0.0:
    file-uri-to-path: public
  fill-range@7.1.1:
    fill-range: public
  finalhandler@2.1.0:
    finalhandler: public
  find-up-simple@1.0.1:
    find-up-simple: public
  find-up@5.0.0:
    find-up: public
  flash-store@1.3.5:
    flash-store: public
  flat-cache@4.0.1:
    flat-cache: public
  flatted@3.3.3:
    flatted: public
  floating-vue@5.2.2(@nuxt/kit@3.16.0(magicast@0.3.5))(vue@3.5.13(typescript@5.5.4)):
    floating-vue: public
  focus-trap@7.6.4:
    focus-trap: public
  follow-redirects@1.15.6(debug@2.6.9):
    follow-redirects: public
  foreground-child@3.3.0:
    foreground-child: public
  forever-agent@0.6.1:
    forever-agent: public
  form-data-encoder@1.7.2:
    form-data-encoder: public
  form-data@4.0.0:
    form-data: public
  formdata-node@4.4.1:
    formdata-node: public
  forwarded@0.2.0:
    forwarded: public
  fp-ts@2.16.9:
    fp-ts: public
  fraction.js@4.3.7:
    fraction.js: public
  fresh@2.0.0:
    fresh: public
  fs-minipass@2.1.0:
    fs-minipass: public
  fs.realpath@1.0.0:
    fs.realpath: public
  fsevents@2.3.3:
    fsevents: public
  function-bind@1.1.2:
    function-bind: public
  fuse.js@7.1.0:
    fuse.js: public
  fzf@0.5.2:
    fzf: public
  gensync@1.0.0-beta.2:
    gensync: public
  gerror@1.0.16:
    gerror: public
  get-caller-file@2.0.5:
    get-caller-file: public
  get-func-name@2.0.2:
    get-func-name: public
  get-intrinsic@1.3.0:
    get-intrinsic: public
  get-package-type@0.1.0:
    get-package-type: public
  get-port-please@3.1.2:
    get-port-please: public
  get-port@6.1.2:
    get-port: public
  get-proto@1.0.1:
    get-proto: public
  get-stream@8.0.1:
    get-stream: public
  get-tsconfig@4.10.0:
    get-tsconfig: public
  getopts@2.3.0:
    getopts: public
  getpass@0.1.7:
    getpass: public
  gifwrap@0.9.4:
    gifwrap: public
  giget@2.0.0:
    giget: public
  git-config-path@2.0.0:
    git-config-path: public
  git-up@8.0.1:
    git-up: public
  git-url-parse@16.0.1:
    git-url-parse: public
  glob-parent@6.0.2:
    glob-parent: public
  glob-to-regexp@0.4.1:
    glob-to-regexp: public
  glob@11.0.0:
    glob: public
  global-directory@4.0.1:
    global-directory: public
  global@4.4.0:
    global: public
  globals@15.15.0:
    globals: public
  globby@14.1.0:
    globby: public
  google-protobuf@3.21.4:
    google-protobuf: public
  gopd@1.2.0:
    gopd: public
  got@11.8.6:
    got: public
  graceful-fs@4.2.11:
    graceful-fs: public
  graphemer@1.4.0:
    graphemer: public
  gzip-size@7.0.0:
    gzip-size: public
  h3@1.15.1:
    h3: public
  har-schema@2.0.0:
    har-schema: public
  har-validator@5.1.5:
    har-validator: public
  has-flag@4.0.0:
    has-flag: public
  has-symbols@1.1.0:
    has-symbols: public
  hasown@2.0.2:
    hasown: public
  hast-util-to-html@9.0.5:
    hast-util-to-html: public
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: public
  he@1.2.0:
    he: public
  hookable@5.5.3:
    hookable: public
  hosted-git-info@2.8.9:
    hosted-git-info: public
  html-void-elements@3.0.0:
    html-void-elements: public
  htmlparser2@3.10.1:
    htmlparser2: public
  http-cache-semantics@4.1.1:
    http-cache-semantics: public
  http-errors@2.0.0:
    http-errors: public
  http-shutdown@1.2.2:
    http-shutdown: public
  http-signature@1.2.0:
    http-signature: public
  http2-wrapper@1.0.3:
    http2-wrapper: public
  https-proxy-agent@7.0.6(supports-color@9.4.0):
    https-proxy-agent: public
  httpxy@0.1.7:
    httpxy: public
  human-signals@5.0.0:
    human-signals: public
  humanize-ms@1.2.1:
    humanize-ms: public
  iconv-lite@0.6.3:
    iconv-lite: public
  ieee754@1.2.1:
    ieee754: public
  ignore@7.0.3:
    ignore: public
  image-meta@0.2.1:
    image-meta: public
  image-q@4.0.0:
    image-q: public
  import-fresh@3.3.0:
    import-fresh: public
  importx@0.5.2:
    importx: public
  impound@0.2.2(rollup@4.35.0):
    impound: public
  imurmurhash@0.1.4:
    imurmurhash: public
  indent-string@4.0.0:
    indent-string: public
  index-to-position@0.1.2:
    index-to-position: public
  inflight@1.0.6:
    inflight: public
  inherits@2.0.4:
    inherits: public
  ini@4.1.1:
    ini: public
  interpret@2.2.0:
    interpret: public
  ioredis@5.6.0:
    ioredis: public
  ipaddr.js@1.9.1:
    ipaddr.js: public
  iron-webcrypto@1.2.1:
    iron-webcrypto: public
  is-arrayish@0.2.1:
    is-arrayish: public
  is-binary-path@2.1.0:
    is-binary-path: public
  is-buffer@2.0.5:
    is-buffer: public
  is-builtin-module@3.2.1:
    is-builtin-module: public
  is-core-module@2.15.1:
    is-core-module: public
  is-docker@3.0.0:
    is-docker: public
  is-extglob@2.1.1:
    is-extglob: public
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: public
  is-function@1.0.2:
    is-function: public
  is-glob@4.0.3:
    is-glob: public
  is-inside-container@1.0.0:
    is-inside-container: public
  is-installed-globally@1.0.0:
    is-installed-globally: public
  is-module@1.0.0:
    is-module: public
  is-network-error@1.1.0:
    is-network-error: public
  is-number@7.0.0:
    is-number: public
  is-path-inside@4.0.0:
    is-path-inside: public
  is-plain-obj@4.1.0:
    is-plain-obj: public
  is-promise@4.0.0:
    is-promise: public
  is-reference@1.2.1:
    is-reference: public
  is-ssh@1.4.0:
    is-ssh: public
  is-stream@3.0.0:
    is-stream: public
  is-typedarray@1.0.0:
    is-typedarray: public
  is-unicode-supported@2.1.0:
    is-unicode-supported: public
  is-what@4.1.16:
    is-what: public
  is-wsl@3.1.0:
    is-wsl: public
  is64bit@2.0.0:
    is64bit: public
  isarray@1.0.0:
    isarray: public
  isexe@2.0.0:
    isexe: public
  isstream@0.1.2:
    isstream: public
  ix@4.6.1:
    ix: public
  jackspeak@4.0.1:
    jackspeak: public
  jest-worker@27.5.1:
    jest-worker: public
  jimp@0.16.13:
    jimp: public
  jiti@2.4.2:
    jiti: public
  jpeg-js@0.4.4:
    jpeg-js: public
  js-levenshtein@1.1.6:
    js-levenshtein: public
  js-tokens@9.0.1:
    js-tokens: public
  js-yaml@4.1.0:
    js-yaml: public
  jsbn@0.1.1:
    jsbn: public
  jsdoc-type-pratt-parser@4.1.0:
    jsdoc-type-pratt-parser: public
  jsesc@3.0.2:
    jsesc: public
  json-buffer@3.0.1:
    json-buffer: public
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: public
  json-rpc-peer@0.17.0:
    json-rpc-peer: public
  json-rpc-protocol@0.13.2:
    json-rpc-protocol: public
  json-schema-traverse@0.4.1:
    json-schema-traverse: public
  json-schema@0.4.0:
    json-schema: public
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: public
  json-stringify-safe@5.0.1:
    json-stringify-safe: public
  json5@2.2.3:
    json5: public
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: public
  jsonc-parser@3.3.1:
    jsonc-parser: public
  jsonfile@6.1.0:
    jsonfile: public
  jsprim@1.4.2:
    jsprim: public
  jsqr@1.4.0:
    jsqr: public
  keyv@4.5.4:
    keyv: public
  kleur@3.0.3:
    kleur: public
  klona@2.0.6:
    klona: public
  knex@3.1.0:
    knex: public
  knitwork@1.2.0:
    knitwork: public
  koffi@2.10.1:
    koffi: public
  kolorist@1.8.0:
    kolorist: public
  language-monitor@1.0.3:
    language-monitor: public
  languagedetect@1.3.0:
    languagedetect: public
  launch-editor@2.10.0:
    launch-editor: public
  lazystream@1.0.1:
    lazystream: public
  level-codec@10.0.0:
    level-codec: public
  level-concat-iterator@3.1.0:
    level-concat-iterator: public
  level-errors@3.0.1:
    level-errors: public
  level-iterator-stream@5.0.0:
    level-iterator-stream: public
  level-js@6.1.0:
    level-js: public
  level-packager@6.0.1:
    level-packager: public
  level-supports@2.1.0:
    level-supports: public
  level@7.0.1:
    level: public
  leveldown@6.1.1:
    leveldown: public
  levelup@5.1.1:
    levelup: public
  levn@0.4.1:
    levn: public
  lilconfig@3.1.2:
    lilconfig: public
  lines-and-columns@1.2.4:
    lines-and-columns: public
  listhen@1.9.0:
    listhen: public
  load-bmfont@1.4.2:
    load-bmfont: public
  load-tsconfig@0.2.5:
    load-tsconfig: public
  loader-runner@4.3.0:
    loader-runner: public
  local-pkg@1.1.1:
    local-pkg: public
  locate-path@6.0.0:
    locate-path: public
  lodash.assignin@4.2.0:
    lodash.assignin: public
  lodash.bind@4.2.1:
    lodash.bind: public
  lodash.camelcase@4.3.0:
    lodash.camelcase: public
  lodash.defaults@4.2.0:
    lodash.defaults: public
  lodash.filter@4.6.0:
    lodash.filter: public
  lodash.flatten@4.4.0:
    lodash.flatten: public
  lodash.foreach@4.5.0:
    lodash.foreach: public
  lodash.isarguments@3.1.0:
    lodash.isarguments: public
  lodash.map@4.6.0:
    lodash.map: public
  lodash.memoize@4.1.2:
    lodash.memoize: public
  lodash.merge@4.6.2:
    lodash.merge: public
  lodash.pick@4.4.0:
    lodash.pick: public
  lodash.reduce@4.6.0:
    lodash.reduce: public
  lodash.reject@4.6.0:
    lodash.reject: public
  lodash.some@4.6.0:
    lodash.some: public
  lodash.uniq@4.5.0:
    lodash.uniq: public
  lodash@4.17.21:
    lodash: public
  long@5.2.3:
    long: public
  longest-streak@3.1.0:
    longest-streak: public
  loupe@3.1.3:
    loupe: public
  lowercase-keys@2.0.0:
    lowercase-keys: public
  lru-cache@10.4.3:
    lru-cache: public
  ltgt@2.2.1:
    ltgt: public
  luxon@3.5.0:
    luxon: public
  magic-regexp@0.8.0:
    magic-regexp: public
  magic-string-ast@0.7.1:
    magic-string-ast: public
  magic-string@0.30.17:
    magic-string: public
  magicast@0.3.5:
    magicast: public
  make-error@1.3.6:
    make-error: public
  mark.js@8.11.1:
    mark.js: public
  markdown-table@3.0.3:
    markdown-table: public
  math-intrinsics@1.1.0:
    math-intrinsics: public
  mdast-util-find-and-replace@3.0.1:
    mdast-util-find-and-replace: public
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: public
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: public
  mdast-util-gfm-footnote@2.0.0:
    mdast-util-gfm-footnote: public
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: public
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: public
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: public
  mdast-util-gfm@3.0.0:
    mdast-util-gfm: public
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: public
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: public
  mdast-util-to-markdown@2.1.0:
    mdast-util-to-markdown: public
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: public
  mdn-data@2.12.2:
    mdn-data: public
  media-typer@1.1.0:
    media-typer: public
  memory-card@1.1.2:
    memory-card: public
  merge-descriptors@2.0.0:
    merge-descriptors: public
  merge-stream@2.0.0:
    merge-stream: public
  merge2@1.4.1:
    merge2: public
  micromark-core-commonmark@2.0.1:
    micromark-core-commonmark: public
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: public
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: public
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: public
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: public
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: public
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: public
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: public
  micromark-factory-destination@2.0.0:
    micromark-factory-destination: public
  micromark-factory-label@2.0.0:
    micromark-factory-label: public
  micromark-factory-space@2.0.0:
    micromark-factory-space: public
  micromark-factory-title@2.0.0:
    micromark-factory-title: public
  micromark-factory-whitespace@2.0.0:
    micromark-factory-whitespace: public
  micromark-util-character@2.1.0:
    micromark-util-character: public
  micromark-util-chunked@2.0.0:
    micromark-util-chunked: public
  micromark-util-classify-character@2.0.0:
    micromark-util-classify-character: public
  micromark-util-combine-extensions@2.0.0:
    micromark-util-combine-extensions: public
  micromark-util-decode-numeric-character-reference@2.0.1:
    micromark-util-decode-numeric-character-reference: public
  micromark-util-decode-string@2.0.0:
    micromark-util-decode-string: public
  micromark-util-encode@2.0.0:
    micromark-util-encode: public
  micromark-util-html-tag-name@2.0.0:
    micromark-util-html-tag-name: public
  micromark-util-normalize-identifier@2.0.0:
    micromark-util-normalize-identifier: public
  micromark-util-resolve-all@2.0.0:
    micromark-util-resolve-all: public
  micromark-util-sanitize-uri@2.0.0:
    micromark-util-sanitize-uri: public
  micromark-util-subtokenize@2.0.1:
    micromark-util-subtokenize: public
  micromark-util-symbol@2.0.0:
    micromark-util-symbol: public
  micromark-util-types@2.0.0:
    micromark-util-types: public
  micromark@4.0.0:
    micromark: public
  micromatch@4.0.8:
    micromatch: public
  mime-db@1.54.0:
    mime-db: public
  mime-types@3.0.1:
    mime-types: public
  mime@3.0.0:
    mime: public
  mimic-fn@4.0.0:
    mimic-fn: public
  mimic-function@5.0.1:
    mimic-function: public
  mimic-response@3.1.0:
    mimic-response: public
  min-document@2.19.0:
    min-document: public
  min-indent@1.0.1:
    min-indent: public
  minimatch@3.1.2:
    minimatch: public
  minimist@1.2.8:
    minimist: public
  minipass@7.1.2:
    minipass: public
  minisearch@7.1.2:
    minisearch: public
  minizlib@2.1.2:
    minizlib: public
  mitt@3.0.1:
    mitt: public
  mkdirp@0.5.6:
    mkdirp: public
  mkdist@1.5.4(typescript@5.5.4):
    mkdist: public
  mlly@1.7.4:
    mlly: public
  mocked-exports@0.1.1:
    mocked-exports: public
  monaco-editor@0.51.0:
    monaco-editor: public
  monaco-sql-languages@0.12.2(antlr4ng-cli@1.0.7)(monaco-editor@0.51.0):
    monaco-sql-languages: public
  mri@1.2.0:
    mri: public
  mrmime@2.0.0:
    mrmime: public
  ms@2.1.2:
    ms: public
  msgpackr-extract@3.0.3:
    msgpackr-extract: public
  msgpackr@1.11.2:
    msgpackr: public
  muggle-string@0.4.1:
    muggle-string: public
  multi-progress@4.0.0(progress@2.0.3):
    multi-progress: public
  mustache@4.2.0:
    mustache: public
  nanoid@5.1.4:
    nanoid: public
  nanotar@0.2.0:
    nanotar: public
  napi-macros@2.0.0:
    napi-macros: public
  natural-compare@1.4.0:
    natural-compare: public
  natural-orderby@5.0.0:
    natural-orderby: public
  negotiator@1.0.0:
    negotiator: public
  neo-async@2.6.2:
    neo-async: public
  nitropack@2.11.6(typescript@5.5.4)(xml2js@0.6.2):
    nitropack: public
  node-abort-controller@3.1.1:
    node-abort-controller: public
  node-addon-api@7.1.1:
    node-addon-api: public
  node-domexception@1.0.0:
    node-domexception: public
  node-fetch-native@1.6.6:
    node-fetch-native: public
  node-fetch@2.7.0:
    node-fetch: public
  node-forge@1.3.1:
    node-forge: public
  node-gyp-build-optional-packages@5.2.2:
    node-gyp-build-optional-packages: public
  node-gyp-build@4.8.2:
    node-gyp-build: public
  node-mock-http@1.0.0:
    node-mock-http: public
  node-releases@2.0.18:
    node-releases: public
  nop@1.0.0:
    nop: public
  nopt@8.1.0:
    nopt: public
  normalize-package-data@2.5.0:
    normalize-package-data: public
  normalize-path@3.0.0:
    normalize-path: public
  normalize-range@0.1.2:
    normalize-range: public
  normalize-url@6.1.0:
    normalize-url: public
  npm-run-path@5.3.0:
    npm-run-path: public
  nth-check@2.1.1:
    nth-check: public
  nuxi@3.13.1:
    nuxi: public
  nuxt-monaco-editor@1.3.1(magicast@0.3.5)(monaco-editor@0.51.0)(vite@6.2.2(@types/node@22.13.10)(jiti@2.4.2)(terser@5.31.6)(tsx@4.19.3)(yaml@2.7.0)):
    nuxt-monaco-editor: public
  nuxt@3.16.0(@parcel/watcher@2.4.1)(@types/node@22.13.10)(db0@0.3.1)(eslint@9.22.0(jiti@2.4.2))(ioredis@5.6.0)(magicast@0.3.5)(optionator@0.9.4)(rollup@4.35.0)(terser@5.31.6)(tsx@4.19.3)(typescript@5.5.4)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(xml2js@0.6.2)(yaml@2.7.0):
    nuxt: public
  nypm@0.4.1:
    nypm: public
  oauth-sign@0.9.0:
    oauth-sign: public
  object-assign@4.1.1:
    object-assign: public
  object-inspect@1.13.4:
    object-inspect: public
  ofetch@1.4.1:
    ofetch: public
  ohash@1.1.6:
    ohash: public
  omggif@1.0.10:
    omggif: public
  on-change@5.0.1:
    on-change: public
  on-finished@2.4.1:
    on-finished: public
  once@1.4.0:
    once: public
  onetime@6.0.0:
    onetime: public
  oniguruma-to-es@2.3.0:
    oniguruma-to-es: public
  open-graph@0.2.6:
    open-graph: public
  open@10.1.0:
    open: public
  openai@4.87.3(ws@8.18.1)(zod@3.24.2):
    openai: public
  openapi-typescript@7.6.1(typescript@5.5.4):
    openapi-typescript: public
  optionator@0.9.4:
    optionator: public
  oxc-parser@0.56.5:
    oxc-parser: public
  p-cancelable@2.1.1:
    p-cancelable: public
  p-limit@3.1.0:
    p-limit: public
  p-locate@5.0.0:
    p-locate: public
  p-map@7.0.3:
    p-map: public
  p-retry@6.2.1:
    p-retry: public
  p-throttle@6.2.0:
    p-throttle: public
  p-try@2.2.0:
    p-try: public
  package-json-from-dist@1.0.0:
    package-json-from-dist: public
  package-manager-detector@0.2.11:
    package-manager-detector: public
  packages/agent:
    '@wechatferry/agent': public
  packages/core:
    '@wechatferry/core': public
  packages/logger:
    '@wechatferry/logger': public
  packages/mcp:
    '@wechatferry/mcp': public
  packages/nuxt:
    '@wechatferry/nuxt': public
  packages/nuxt/client:
    my-module-client: public
  packages/nuxt/playground:
    nuxt-wcferry-playground: public
  packages/plugins:
    '@wechatferry/plugins': public
  packages/puppet:
    '@wechatferry/puppet': public
  packages/robot:
    '@wechatferry/robot': public
  packages/wechatferry:
    wechatferry: public
  pako@1.0.11:
    pako: public
  parent-module@1.0.1:
    parent-module: public
  parse-bmfont-ascii@1.0.6:
    parse-bmfont-ascii: public
  parse-bmfont-binary@1.0.6:
    parse-bmfont-binary: public
  parse-bmfont-xml@1.1.6:
    parse-bmfont-xml: public
  parse-git-config@3.0.0:
    parse-git-config: public
  parse-gitignore@2.0.0:
    parse-gitignore: public
  parse-headers@2.0.5:
    parse-headers: public
  parse-imports@2.1.1:
    parse-imports: public
  parse-json@8.1.0:
    parse-json: public
  parse-ms@4.0.0:
    parse-ms: public
  parse-path@7.0.0:
    parse-path: public
  parse-url@9.2.0:
    parse-url: public
  parseurl@1.3.3:
    parseurl: public
  path-browserify@1.0.1:
    path-browserify: public
  path-exists@4.0.0:
    path-exists: public
  path-is-absolute@1.0.1:
    path-is-absolute: public
  path-key@3.1.1:
    path-key: public
  path-parse@1.0.7:
    path-parse: public
  path-scurry@2.0.0:
    path-scurry: public
  path-to-regexp@8.2.0:
    path-to-regexp: public
  path-type@6.0.0:
    path-type: public
  pathe@1.1.2:
    pathe: public
  pathval@2.0.0:
    pathval: public
  peek-readable@4.1.0:
    peek-readable: public
  pend@1.2.0:
    pend: public
  perfect-debounce@1.0.0:
    perfect-debounce: public
  performance-now@2.1.0:
    performance-now: public
  pg-connection-string@2.6.2:
    pg-connection-string: public
  phin@2.9.3:
    phin: public
  picocolors@1.1.1:
    picocolors: public
  picomatch@2.3.1:
    picomatch: public
  pixelmatch@4.0.2:
    pixelmatch: public
  pkce-challenge@5.0.0:
    pkce-challenge: public
  pkg-types@1.3.1:
    pkg-types: public
  pluralize@8.0.0:
    pluralize: public
  pngjs@5.0.0:
    pngjs: public
  pnpm-workspace-yaml@0.1.2:
    pnpm-workspace-yaml: public
  postcss-calc@10.0.2(postcss@8.4.41):
    postcss-calc: public
  postcss-colormin@7.0.2(postcss@8.4.41):
    postcss-colormin: public
  postcss-convert-values@7.0.3(postcss@8.4.41):
    postcss-convert-values: public
  postcss-discard-comments@7.0.2(postcss@8.4.41):
    postcss-discard-comments: public
  postcss-discard-duplicates@7.0.1(postcss@8.4.41):
    postcss-discard-duplicates: public
  postcss-discard-empty@7.0.0(postcss@8.4.41):
    postcss-discard-empty: public
  postcss-discard-overridden@7.0.0(postcss@8.4.41):
    postcss-discard-overridden: public
  postcss-merge-longhand@7.0.3(postcss@8.4.41):
    postcss-merge-longhand: public
  postcss-merge-rules@7.0.3(postcss@8.4.41):
    postcss-merge-rules: public
  postcss-minify-font-values@7.0.0(postcss@8.4.41):
    postcss-minify-font-values: public
  postcss-minify-gradients@7.0.0(postcss@8.4.41):
    postcss-minify-gradients: public
  postcss-minify-params@7.0.2(postcss@8.4.41):
    postcss-minify-params: public
  postcss-minify-selectors@7.0.3(postcss@8.4.41):
    postcss-minify-selectors: public
  postcss-nested@6.2.0(postcss@8.4.41):
    postcss-nested: public
  postcss-normalize-charset@7.0.0(postcss@8.4.41):
    postcss-normalize-charset: public
  postcss-normalize-display-values@7.0.0(postcss@8.4.41):
    postcss-normalize-display-values: public
  postcss-normalize-positions@7.0.0(postcss@8.4.41):
    postcss-normalize-positions: public
  postcss-normalize-repeat-style@7.0.0(postcss@8.4.41):
    postcss-normalize-repeat-style: public
  postcss-normalize-string@7.0.0(postcss@8.4.41):
    postcss-normalize-string: public
  postcss-normalize-timing-functions@7.0.0(postcss@8.4.41):
    postcss-normalize-timing-functions: public
  postcss-normalize-unicode@7.0.2(postcss@8.4.41):
    postcss-normalize-unicode: public
  postcss-normalize-url@7.0.0(postcss@8.4.41):
    postcss-normalize-url: public
  postcss-normalize-whitespace@7.0.0(postcss@8.4.41):
    postcss-normalize-whitespace: public
  postcss-ordered-values@7.0.1(postcss@8.4.41):
    postcss-ordered-values: public
  postcss-reduce-initial@7.0.2(postcss@8.4.41):
    postcss-reduce-initial: public
  postcss-reduce-transforms@7.0.0(postcss@8.4.41):
    postcss-reduce-transforms: public
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: public
  postcss-svgo@7.0.1(postcss@8.4.41):
    postcss-svgo: public
  postcss-unique-selectors@7.0.2(postcss@8.4.41):
    postcss-unique-selectors: public
  postcss-value-parser@4.2.0:
    postcss-value-parser: public
  postcss@8.5.3:
    postcss: public
  preact@10.23.2:
    preact: public
  prelude-ls@1.2.1:
    prelude-ls: public
  pretty-bytes@6.1.1:
    pretty-bytes: public
  pretty-ms@9.2.0:
    pretty-ms: public
  process-nextick-args@2.0.1:
    process-nextick-args: public
  process@0.11.10:
    process: public
  progress@2.0.3:
    progress: public
  promise-retry@2.0.1:
    promise-retry: public
  prompts@2.4.2:
    prompts: public
  property-information@7.0.0:
    property-information: public
  protobufjs@7.4.0:
    protobufjs: public
  protoc-gen-ts@0.8.7:
    protoc-gen-ts: public
  protocols@2.0.1:
    protocols: public
  proxy-addr@2.0.7:
    proxy-addr: public
  proxy-from-env@1.1.0:
    proxy-from-env: public
  psl@1.9.0:
    psl: public
  pump@3.0.0:
    pump: public
  punycode@2.3.1:
    punycode: public
  qrcode-terminal@0.12.0:
    qrcode-terminal: public
  qrcode@1.5.4:
    qrcode: public
  qs@6.14.0:
    qs: public
  quansync@0.2.8:
    quansync: public
  queue-microtask@1.2.3:
    queue-microtask: public
  queue-tick@1.0.1:
    queue-tick: public
  quick-lru@5.1.1:
    quick-lru: public
  radix3@1.1.2:
    radix3: public
  randombytes@2.1.0:
    randombytes: public
  range-parser@1.2.1:
    range-parser: public
  raw-body@3.0.0:
    raw-body: public
  rc9@2.1.2:
    rc9: public
  read-pkg-up@7.0.1:
    read-pkg-up: public
  read-pkg@5.2.0:
    read-pkg: public
  readable-stream@4.5.2:
    readable-stream: public
  readable-web-to-node-stream@3.0.2:
    readable-web-to-node-stream: public
  readdir-glob@1.1.3:
    readdir-glob: public
  readdirp@3.6.0:
    readdirp: public
  rechoir@0.8.0:
    rechoir: public
  redis-errors@1.2.0:
    redis-errors: public
  redis-parser@3.0.0:
    redis-parser: public
  redux-observable@2.0.0(redux@4.2.1):
    redux-observable: public
  redux@4.2.1:
    redux: public
  refa@0.12.1:
    refa: public
  regenerator-runtime@0.13.11:
    regenerator-runtime: public
  regex-recursion@5.1.1:
    regex-recursion: public
  regex-utilities@2.3.0:
    regex-utilities: public
  regex@5.1.1:
    regex: public
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: public
  regexp-tree@0.1.27:
    regexp-tree: public
  regjsparser@0.10.0:
    regjsparser: public
  request@2.88.2:
    request: public
  require-directory@2.1.1:
    require-directory: public
  require-from-string@2.0.2:
    require-from-string: public
  require-main-filename@2.0.0:
    require-main-filename: public
  resolve-alpn@1.2.1:
    resolve-alpn: public
  resolve-from@5.0.0:
    resolve-from: public
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: public
  resolve@1.22.8:
    resolve: public
  responselike@2.0.1:
    responselike: public
  restore-cursor@5.1.0:
    restore-cursor: public
  retry@0.13.1:
    retry: public
  reusify@1.0.4:
    reusify: public
  rfdc@1.4.1:
    rfdc: public
  rollup-plugin-dts@6.1.1(rollup@3.29.4)(typescript@5.5.4):
    rollup-plugin-dts: public
  rollup-plugin-visualizer@5.14.0(rollup@4.35.0):
    rollup-plugin-visualizer: public
  rollup@3.29.4:
    rollup: public
  router@2.2.0:
    router: public
  rspack-resolver@1.1.2:
    rspack-resolver: public
  run-applescript@7.0.0:
    run-applescript: public
  run-parallel-limit@1.1.0:
    run-parallel-limit: public
  run-parallel@1.2.0:
    run-parallel: public
  rx-queue@1.0.5:
    rx-queue: public
  rxjs@7.8.1:
    rxjs: public
  safe-buffer@5.2.1:
    safe-buffer: public
  safer-buffer@2.1.2:
    safer-buffer: public
  sax@1.4.1:
    sax: public
  schema-utils@3.3.0:
    schema-utils: public
  scslre@0.3.0:
    scslre: public
  scule@1.3.0:
    scule: public
  search-insights@2.17.0:
    search-insights: public
  semver@7.7.1:
    semver: public
  send@1.2.0:
    send: public
  serialize-javascript@6.0.2:
    serialize-javascript: public
  serve-placeholder@2.0.2:
    serve-placeholder: public
  serve-static@2.2.0:
    serve-static: public
  set-blocking@2.0.0:
    set-blocking: public
  setprototypeof@1.2.0:
    setprototypeof: public
  shebang-command@2.0.0:
    shebang-command: public
  shebang-regex@3.0.0:
    shebang-regex: public
  shell-quote@1.8.1:
    shell-quote: public
  shiki@1.29.2:
    shiki: public
  side-channel-list@1.0.0:
    side-channel-list: public
  side-channel-map@1.0.1:
    side-channel-map: public
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: public
  side-channel@1.1.0:
    side-channel: public
  siginfo@2.0.0:
    siginfo: public
  signal-exit@4.1.0:
    signal-exit: public
  simple-git@3.27.0:
    simple-git: public
  sirv@2.0.4:
    sirv: public
  sisteransi@1.0.5:
    sisteransi: public
  slash@4.0.0:
    slash: public
  slashes@3.0.12:
    slashes: public
  smob@1.5.0:
    smob: public
  source-map-js@1.2.1:
    source-map-js: public
  source-map-support@0.5.21:
    source-map-support: public
  source-map@0.7.4:
    source-map: public
  space-separated-tokens@2.0.2:
    space-separated-tokens: public
  spdx-correct@3.2.0:
    spdx-correct: public
  spdx-exceptions@2.5.0:
    spdx-exceptions: public
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: public
  spdx-license-ids@3.0.20:
    spdx-license-ids: public
  speakingurl@14.0.1:
    speakingurl: public
  splitpanes@3.1.5:
    splitpanes: public
  sshpk@1.18.0:
    sshpk: public
  stable-hash@0.0.5:
    stable-hash: public
  stackback@0.0.2:
    stackback: public
  standard-as-callback@2.1.0:
    standard-as-callback: public
  state-switch@1.7.1(brolog@1.14.2)(gerror@1.0.16)(rxjs@7.8.1):
    state-switch: public
  statuses@2.0.1:
    statuses: public
  std-env@3.8.1:
    std-env: public
  streamx@2.19.1:
    streamx: public
  string-width@4.2.3:
    string-width: public
    string-width-cjs: public
  string_decoder@1.3.0:
    string_decoder: public
  stringify-entities@4.0.4:
    stringify-entities: public
  strip-ansi@6.0.1:
    strip-ansi: public
    strip-ansi-cjs: public
  strip-final-newline@3.0.0:
    strip-final-newline: public
  strip-indent@3.0.0:
    strip-indent: public
  strip-json-comments@3.1.1:
    strip-json-comments: public
  strip-literal@3.0.0:
    strip-literal: public
  strnum@1.0.5:
    strnum: public
  stronger-typed-streams@0.2.0:
    stronger-typed-streams: public
  strtok3@6.3.0:
    strtok3: public
  structured-clone-es@1.0.0:
    structured-clone-es: public
  stylehacks@7.0.3(postcss@8.4.41):
    stylehacks: public
  superjson@2.2.1:
    superjson: public
  supports-color@7.2.0:
    supports-color: public
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: public
  svgo@3.3.2:
    svgo: public
  synckit@0.9.1:
    synckit: public
  system-architecture@0.1.0:
    system-architecture: public
  tabbable@6.2.0:
    tabbable: public
  tapable@2.2.1:
    tapable: public
  tar-stream@3.1.7:
    tar-stream: public
  tar@6.2.1:
    tar: public
  tarn@3.0.2:
    tarn: public
  terser-webpack-plugin@5.3.10(@swc/core@1.7.22)(esbuild@0.25.1)(webpack@5.94.0(@swc/core@1.7.22)(esbuild@0.25.1)):
    terser-webpack-plugin: public
  terser@5.31.6:
    terser: public
  text-decoder@1.1.1:
    text-decoder: public
  tildify@2.0.0:
    tildify: public
  timm@1.7.1:
    timm: public
  tiny-conventional-commits-parser@0.0.1:
    tiny-conventional-commits-parser: public
  tiny-invariant@1.3.3:
    tiny-invariant: public
  tinybench@2.9.0:
    tinybench: public
  tinycolor2@1.6.0:
    tinycolor2: public
  tinyexec@0.3.2:
    tinyexec: public
  tinyglobby@0.2.12:
    tinyglobby: public
  tinypool@1.0.1:
    tinypool: public
  tinyrainbow@1.2.0:
    tinyrainbow: public
  tinyspy@3.0.2:
    tinyspy: public
  to-fast-properties@2.0.0:
    to-fast-properties: public
  to-regex-range@5.0.1:
    to-regex-range: public
  toidentifier@1.0.1:
    toidentifier: public
  token-types@4.2.1:
    token-types: public
  toml-eslint-parser@0.10.0:
    toml-eslint-parser: public
  totalist@3.0.1:
    totalist: public
  tough-cookie@2.5.0:
    tough-cookie: public
  tr46@0.0.3:
    tr46: public
  trim-lines@3.0.1:
    trim-lines: public
  ts-api-utils@2.0.1(typescript@5.5.4):
    ts-api-utils: public
  tsconfck@3.1.5(typescript@5.5.4):
    tsconfck: public
  tslib@2.8.1:
    tslib: public
  tsx@4.19.3:
    tsx: public
  tunnel-agent@0.6.0:
    tunnel-agent: public
  tweetnacl@0.14.5:
    tweetnacl: public
  twoslash-protocol@0.2.12:
    twoslash-protocol: public
  twoslash-vue@0.2.12(typescript@5.5.4):
    twoslash-vue: public
  twoslash@0.2.12(typescript@5.5.4):
    twoslash: public
  type-check@0.4.0:
    type-check: public
  type-fest@4.37.0:
    type-fest: public
  type-is@2.0.1:
    type-is: public
  type-level-regexp@0.1.17:
    type-level-regexp: public
  typed-emitter@1.5.0-from-event:
    typed-emitter: public
  typesafe-actions@5.1.0:
    typesafe-actions: public
  typescript@5.5.4:
    typescript: public
  ufo@1.5.4:
    ufo: public
  ultrahtml@1.5.3:
    ultrahtml: public
  unconfig@7.3.1:
    unconfig: public
  uncrypto@0.1.3:
    uncrypto: public
  unctx@2.4.1:
    unctx: public
  undici-types@6.20.0:
    undici-types: public
  unenv@2.0.0-rc.14:
    unenv: public
  unhead@2.0.0-rc.13:
    unhead: public
  unicorn-magic@0.3.0:
    unicorn-magic: public
  unimport@3.14.6(rollup@4.35.0):
    unimport: public
  unist-util-is@6.0.0:
    unist-util-is: public
  unist-util-position@5.0.0:
    unist-util-position: public
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: public
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: public
  unist-util-visit@5.0.0:
    unist-util-visit: public
  universalify@2.0.1:
    universalify: public
  unocss@0.65.4(@unocss/webpack@0.65.4(rollup@4.35.0)(webpack@5.94.0(@swc/core@1.7.22)(esbuild@0.25.1)))(postcss@8.5.3)(rollup@4.35.0)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4)):
    unocss: public
  unpipe@1.0.0:
    unpipe: public
  unplugin-utils@0.2.4:
    unplugin-utils: public
  unplugin-vue-components@0.27.5(@babel/parser@7.26.10)(@nuxt/kit@3.16.0(magicast@0.3.5))(rollup@4.35.0)(vue@3.5.13(typescript@5.5.4)):
    unplugin-vue-components: public
  unplugin-vue-router@0.12.0(vue-router@4.5.0(vue@3.5.13(typescript@5.5.4)))(vue@3.5.13(typescript@5.5.4)):
    unplugin-vue-router: public
  unplugin@2.2.0:
    unplugin: public
  unstorage@1.15.0(db0@0.3.1)(ioredis@5.6.0):
    unstorage: public
  untun@0.1.3:
    untun: public
  untyped@2.0.0:
    untyped: public
  unwasm@0.3.9:
    unwasm: public
  update-browserslist-db@1.1.0(browserslist@4.23.3):
    update-browserslist-db: public
  uqr@0.1.2:
    uqr: public
  uri-js-replace@1.0.1:
    uri-js-replace: public
  uri-js@4.4.1:
    uri-js: public
  urlpattern-polyfill@8.0.2:
    urlpattern-polyfill: public
  utif@2.0.1:
    utif: public
  util-deprecate@1.0.2:
    util-deprecate: public
  utility-types@3.11.0:
    utility-types: public
  uuid@9.0.1:
    uuid: public
  v-lazy-show@0.3.0(@vue/compiler-core@3.5.13):
    v-lazy-show: public
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: public
  vary@1.1.2:
    vary: public
  verror@1.10.0:
    verror: public
  vfile-message@4.0.2:
    vfile-message: public
  vfile@6.0.3:
    vfile: public
  vite-dev-rpc@1.0.7(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6)):
    vite-dev-rpc: public
  vite-hot-client@0.2.4(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6)):
    vite-hot-client: public
  vite-node@2.1.9(@types/node@22.13.10)(terser@5.31.6):
    vite-node: public
  vite-plugin-checker@0.9.0(eslint@9.22.0(jiti@2.4.2))(optionator@0.9.4)(typescript@5.5.4)(vite@6.2.2(@types/node@22.13.10)(jiti@2.4.2)(terser@5.31.6)(tsx@4.19.3)(yaml@2.7.0)):
    vite-plugin-checker: public
  vite-plugin-inspect@0.8.9(@nuxt/kit@3.16.0(magicast@0.3.5))(rollup@4.35.0)(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6)):
    vite-plugin-inspect: public
  vite-plugin-static-copy@2.3.0(vite@6.2.2(@types/node@22.13.10)(jiti@2.4.2)(terser@5.31.6)(tsx@4.19.3)(yaml@2.7.0)):
    vite-plugin-static-copy: public
  vite-plugin-vue-inspector@5.3.1(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6)):
    vite-plugin-vue-inspector: public
  vite-plugin-vue-tracer@0.1.1(vite@5.4.2(@types/node@22.13.10)(terser@5.31.6))(vue@3.5.13(typescript@5.5.4)):
    vite-plugin-vue-tracer: public
  vite@5.4.2(@types/node@22.13.10)(terser@5.31.6):
    vite: public
  vitepress-plugin-group-icons@1.3.7:
    vitepress-plugin-group-icons: public
  vitepress@1.6.3(@algolia/client-search@5.21.0)(@types/node@22.13.10)(axios@1.7.6)(change-case@5.4.4)(fuse.js@7.1.0)(postcss@8.5.3)(qrcode@1.5.4)(search-insights@2.17.0)(terser@5.31.6)(typescript@5.5.4):
    vitepress: public
  vitest-environment-nuxt@1.0.1(@types/node@22.13.10)(jiti@2.4.2)(magicast@0.3.5)(terser@5.31.6)(tsx@4.19.3)(typescript@5.5.4)(vitest@2.1.9(@types/node@22.13.10)(terser@5.31.6))(yaml@2.7.0):
    vitest-environment-nuxt: public
  vscode-uri@3.1.0:
    vscode-uri: public
  vue-bundle-renderer@2.1.1:
    vue-bundle-renderer: public
  vue-devtools-stub@0.1.0:
    vue-devtools-stub: public
  vue-eslint-parser@9.4.3(eslint@9.22.0(jiti@2.4.2)):
    vue-eslint-parser: public
  vue-flow-layout@0.1.1(vue@3.5.13(typescript@5.5.4)):
    vue-flow-layout: public
  vue-resize@2.0.0-alpha.1(vue@3.5.13(typescript@5.5.4)):
    vue-resize: public
  vue-router@4.5.0(vue@3.5.13(typescript@5.5.4)):
    vue-router: public
  vue@3.5.13(typescript@5.5.4):
    vue: public
  watchdog@0.9.2:
    watchdog: public
  watchpack@2.4.2:
    watchpack: public
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: public
  webidl-conversions@3.0.1:
    webidl-conversions: public
  webpack-sources@3.2.3:
    webpack-sources: public
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: public
  webpack@5.94.0(@swc/core@1.7.22)(esbuild@0.25.1):
    webpack: public
  wechat4u@0.7.14:
    wechat4u: public
  wechaty-grpc@1.5.2:
    wechaty-grpc: public
  wechaty-plugin-contrib@1.12.2:
    wechaty-plugin-contrib: public
  wechaty-puppet-service@1.19.9(brolog@1.14.2)(redux@4.2.1)(wechaty-puppet@1.20.2(rxjs@7.8.1))(wechaty@1.20.2(@swc/core@1.7.22)(brolog@1.14.2)(redux@4.2.1)(rxjs@7.8.1)):
    wechaty-puppet-service: public
  wechaty-puppet-wechat4u@1.14.14(@swc/core@1.7.22)(wechaty-puppet@1.20.2(rxjs@7.8.1)):
    wechaty-puppet-wechat4u: public
  wechaty-puppet@1.20.2(rxjs@7.8.1):
    wechaty-puppet: public
  wechaty-redux@1.20.2(brolog@1.14.2)(wechaty-puppet@1.20.2(rxjs@7.8.1))(wechaty@1.20.2(@swc/core@1.7.22)(brolog@1.14.2)(redux@4.2.1)(rxjs@7.8.1)):
    wechaty-redux: public
  wechaty-token@1.1.2:
    wechaty-token: public
  wechaty@1.20.2(@swc/core@1.7.22)(brolog@1.14.2)(redux@4.2.1)(rxjs@7.8.1):
    wechaty: public
  whatwg-url@5.0.0:
    whatwg-url: public
  which-module@2.0.1:
    which-module: public
  which@3.0.1:
    which: public
  why-is-node-running@2.3.0:
    why-is-node-running: public
  word-wrap@1.2.5:
    word-wrap: public
  wrap-ansi@7.0.0:
    wrap-ansi: public
    wrap-ansi-cjs: public
  wrappy@1.0.2:
    wrappy: public
  ws@8.18.1:
    ws: public
  xhr@2.6.0:
    xhr: public
  xml-name-validator@4.0.0:
    xml-name-validator: public
  xml-parse-from-string@1.0.1:
    xml-parse-from-string: public
  xml2js@0.6.2:
    xml2js: public
  xmlbuilder@11.0.1:
    xmlbuilder: public
  xstate@4.38.3:
    xstate: public
  xtend@4.0.2:
    xtend: public
  y18n@5.0.8:
    y18n: public
  yallist@4.0.0:
    yallist: public
  yaml-ast-parser@0.0.43:
    yaml-ast-parser: public
  yaml-eslint-parser@1.2.3:
    yaml-eslint-parser: public
  yaml@2.7.0:
    yaml: public
  yargs-parser@21.1.1:
    yargs-parser: public
  yargs@17.7.2:
    yargs: public
  yauzl@2.10.0:
    yauzl: public
  yocto-queue@0.1.0:
    yocto-queue: public
  yoctocolors@2.1.1:
    yoctocolors: public
  youch-core@0.3.2:
    youch-core: public
  youch@4.1.0-beta.6:
    youch: public
  zip-stream@6.0.1:
    zip-stream: public
  zod-to-json-schema@3.24.5(zod@3.24.2):
    zod-to-json-schema: public
  zod@3.24.2:
    zod: public
  zwitch@2.0.4:
    zwitch: public
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.8.0
pendingBuilds: []
prunedAt: Mon, 04 Aug 2025 13:39:55 GMT
publicHoistPattern:
  - '*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.3.1'
  - '@emnapi/runtime@1.3.1'
  - '@emnapi/wasi-threads@1.0.1'
  - '@esbuild/aix-ppc64@0.19.12'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.23.1'
  - '@esbuild/aix-ppc64@0.25.1'
  - '@esbuild/android-arm64@0.19.12'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.23.1'
  - '@esbuild/android-arm64@0.25.1'
  - '@esbuild/android-arm@0.19.12'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.23.1'
  - '@esbuild/android-arm@0.25.1'
  - '@esbuild/android-x64@0.19.12'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.23.1'
  - '@esbuild/android-x64@0.25.1'
  - '@esbuild/darwin-arm64@0.19.12'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.23.1'
  - '@esbuild/darwin-arm64@0.25.1'
  - '@esbuild/darwin-x64@0.19.12'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.23.1'
  - '@esbuild/darwin-x64@0.25.1'
  - '@esbuild/freebsd-arm64@0.19.12'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.23.1'
  - '@esbuild/freebsd-arm64@0.25.1'
  - '@esbuild/freebsd-x64@0.19.12'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.23.1'
  - '@esbuild/freebsd-x64@0.25.1'
  - '@esbuild/linux-arm64@0.19.12'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.23.1'
  - '@esbuild/linux-arm64@0.25.1'
  - '@esbuild/linux-arm@0.19.12'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.23.1'
  - '@esbuild/linux-arm@0.25.1'
  - '@esbuild/linux-ia32@0.19.12'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.23.1'
  - '@esbuild/linux-ia32@0.25.1'
  - '@esbuild/linux-loong64@0.19.12'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.23.1'
  - '@esbuild/linux-loong64@0.25.1'
  - '@esbuild/linux-mips64el@0.19.12'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.23.1'
  - '@esbuild/linux-mips64el@0.25.1'
  - '@esbuild/linux-ppc64@0.19.12'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.23.1'
  - '@esbuild/linux-ppc64@0.25.1'
  - '@esbuild/linux-riscv64@0.19.12'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.23.1'
  - '@esbuild/linux-riscv64@0.25.1'
  - '@esbuild/linux-s390x@0.19.12'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.23.1'
  - '@esbuild/linux-s390x@0.25.1'
  - '@esbuild/linux-x64@0.19.12'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.23.1'
  - '@esbuild/linux-x64@0.25.1'
  - '@esbuild/netbsd-arm64@0.25.1'
  - '@esbuild/netbsd-x64@0.19.12'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.23.1'
  - '@esbuild/netbsd-x64@0.25.1'
  - '@esbuild/openbsd-arm64@0.23.1'
  - '@esbuild/openbsd-arm64@0.25.1'
  - '@esbuild/openbsd-x64@0.19.12'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.23.1'
  - '@esbuild/openbsd-x64@0.25.1'
  - '@esbuild/sunos-x64@0.19.12'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.23.1'
  - '@esbuild/sunos-x64@0.25.1'
  - '@esbuild/win32-arm64@0.19.12'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.23.1'
  - '@esbuild/win32-arm64@0.25.1'
  - '@esbuild/win32-ia32@0.19.12'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.23.1'
  - '@esbuild/win32-ia32@0.25.1'
  - '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3'
  - '@napi-rs/wasm-runtime@0.2.7'
  - '@oxc-parser/binding-darwin-arm64@0.56.5'
  - '@oxc-parser/binding-darwin-x64@0.56.5'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.56.5'
  - '@oxc-parser/binding-linux-arm64-gnu@0.56.5'
  - '@oxc-parser/binding-linux-arm64-musl@0.56.5'
  - '@oxc-parser/binding-linux-x64-gnu@0.56.5'
  - '@oxc-parser/binding-linux-x64-musl@0.56.5'
  - '@oxc-parser/binding-wasm32-wasi@0.56.5'
  - '@oxc-parser/binding-win32-arm64-msvc@0.56.5'
  - '@parcel/watcher-android-arm64@2.4.1'
  - '@parcel/watcher-darwin-arm64@2.4.1'
  - '@parcel/watcher-darwin-x64@2.4.1'
  - '@parcel/watcher-freebsd-x64@2.4.1'
  - '@parcel/watcher-linux-arm-glibc@2.4.1'
  - '@parcel/watcher-linux-arm64-glibc@2.4.1'
  - '@parcel/watcher-linux-arm64-musl@2.4.1'
  - '@parcel/watcher-linux-x64-glibc@2.4.1'
  - '@parcel/watcher-linux-x64-musl@2.4.1'
  - '@parcel/watcher-win32-arm64@2.4.1'
  - '@parcel/watcher-win32-ia32@2.4.1'
  - '@rollup/rollup-android-arm-eabi@4.21.2'
  - '@rollup/rollup-android-arm-eabi@4.35.0'
  - '@rollup/rollup-android-arm64@4.21.2'
  - '@rollup/rollup-android-arm64@4.35.0'
  - '@rollup/rollup-darwin-arm64@4.21.2'
  - '@rollup/rollup-darwin-arm64@4.35.0'
  - '@rollup/rollup-darwin-x64@4.21.2'
  - '@rollup/rollup-darwin-x64@4.35.0'
  - '@rollup/rollup-freebsd-arm64@4.35.0'
  - '@rollup/rollup-freebsd-x64@4.35.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.21.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.35.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.21.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.35.0'
  - '@rollup/rollup-linux-arm64-gnu@4.21.2'
  - '@rollup/rollup-linux-arm64-gnu@4.35.0'
  - '@rollup/rollup-linux-arm64-musl@4.21.2'
  - '@rollup/rollup-linux-arm64-musl@4.35.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.35.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.21.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.35.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.21.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.35.0'
  - '@rollup/rollup-linux-s390x-gnu@4.21.2'
  - '@rollup/rollup-linux-s390x-gnu@4.35.0'
  - '@rollup/rollup-linux-x64-gnu@4.21.2'
  - '@rollup/rollup-linux-x64-gnu@4.35.0'
  - '@rollup/rollup-linux-x64-musl@4.21.2'
  - '@rollup/rollup-linux-x64-musl@4.35.0'
  - '@rollup/rollup-win32-arm64-msvc@4.21.2'
  - '@rollup/rollup-win32-arm64-msvc@4.35.0'
  - '@rollup/rollup-win32-ia32-msvc@4.21.2'
  - '@rollup/rollup-win32-ia32-msvc@4.35.0'
  - '@rustup/nng-darwin-arm64@0.1.2'
  - '@rustup/nng-darwin-universal@0.1.2'
  - '@rustup/nng-darwin-x64@0.1.2'
  - '@rustup/nng-linux-x64-gnu@0.1.2'
  - '@swc/core-darwin-arm64@1.7.22'
  - '@swc/core-darwin-x64@1.7.22'
  - '@swc/core-linux-arm-gnueabihf@1.7.22'
  - '@swc/core-linux-arm64-gnu@1.7.22'
  - '@swc/core-linux-arm64-musl@1.7.22'
  - '@swc/core-linux-x64-gnu@1.7.22'
  - '@swc/core-linux-x64-musl@1.7.22'
  - '@swc/core-win32-arm64-msvc@1.7.22'
  - '@swc/core-win32-ia32-msvc@1.7.22'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/rspack-resolver-binding-darwin-arm64@1.1.2'
  - '@unrs/rspack-resolver-binding-darwin-x64@1.1.2'
  - '@unrs/rspack-resolver-binding-freebsd-x64@1.1.2'
  - '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.1.2'
  - '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.1.2'
  - '@unrs/rspack-resolver-binding-linux-arm64-musl@1.1.2'
  - '@unrs/rspack-resolver-binding-linux-x64-gnu@1.1.2'
  - '@unrs/rspack-resolver-binding-linux-x64-musl@1.1.2'
  - '@unrs/rspack-resolver-binding-wasm32-wasi@1.1.2'
  - '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.1.2'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v3
virtualStoreDir: D:\AIhub\wechat\node_modules\.pnpm
virtualStoreDirMaxLength: 120
