import { WechatyBuilder } from 'wechaty'
import { expect, it, vi } from 'vitest'
import { WechatferryPuppet } from '../packages/puppet/src'
import { WechatferryAgent } from '../packages/agent/src'
import { createSafeModePuppet } from '../packages/plugins/src'

// 创建一个完全模拟的 Wechatferry 类
class MockWechatferry {
  constructor() {
    // 模拟构造函数，不执行任何实际操作
  }

  start() {
    return true
  }

  stop() {
    return true
  }

  isLogin() {
    return true
  }

  getSelfWxid() {
    return 'filehelper'
  }

  sendText(wxid: string, msg: string) {
    console.log(`Mock sendText: ${wxid} -> ${msg}`)
    return true
  }

  sendImage(wxid: string, path: string) {
    console.log(`Mock sendImage: ${wxid} -> ${path}`)
    return true
  }

  getContactList() {
    return []
  }

  getChatRoomList() {
    return []
  }

  // 添加其他需要的方法...
}

// 使用 Proxy 来拦截所有方法调用
const mockCore = new Proxy(new MockWechatferry(), {
  get(target, prop, receiver) {
    if (typeof prop === 'string') {
      // 拦截所有 send 开头的方法
      if (prop !== 'send' && prop.startsWith('send')) {
        return (...args: any[]) => {
          console.log(`Mock ${prop}:`, JSON.stringify(args))
          return true
        }
      }

      // 如果目标对象有这个方法，就使用它
      if (prop in target) {
        return Reflect.get(target, prop, receiver)
      }

      // 否则返回一个默认的模拟函数
      return (...args: any[]) => {
        console.log(`Mock ${prop}:`, args)
        return true
      }
    }
    return Reflect.get(target, prop, receiver)
  },
})

const agent = new WechatferryAgent({
  wcf: mockCore as any,
})

const puppet = createSafeModePuppet(new WechatferryPuppet({
  agent,
}))

const bot = WechatyBuilder.build({
  puppet,
})

const fakeMessage = {
  is_self: false,
  is_group: true,
  id: '5588315607154242157',
  type: 10000,
  ts: 1725260569,
  roomid: '53423823892@chatroom',
  content: '',
  sender: '53423823892@chatroom',
  sign: '623cbc75463d1f4daffa48297eea3157',
  thumb: '',
  extra: '',
  xml: '',
}

it('mock wechatferry basic functionality', () => {
  expect(mockCore.isLogin()).toBe(true)
  expect(mockCore.getSelfWxid()).toBe('filehelper')
  expect(mockCore.sendText('test', 'hello')).toBe(true)
})

it('safeMode with mock', async () => {
  let count = 3 // 减少测试次数
  while (count--) {
    await puppet.messageSend('filehelper', {
      type: 'Text',
      payload: {
        text: 'hello',
        mentions: [],
      },
    })
  }
  expect(true).toBe(true) // 如果没有抛出异常就算成功
}, { timeout: 10000 })

it('agent basic functionality', () => {
  expect(agent).toBeDefined()
  expect(typeof agent.start).toBe('function')
  expect(typeof agent.stop).toBe('function')
})
