{"name": "fp-ts", "version": "2.16.9", "description": "Functional programming in TypeScript", "main": "./lib/index.js", "module": "./es6/index.js", "typings": "lib/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/gcanti/fp-ts.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/gcanti/fp-ts/issues"}, "homepage": "https://github.com/gcanti/fp-ts", "tags": ["typescript", "algebraic-data-types", "functional-programming"], "keywords": ["typescript", "algebraic-data-types", "functional-programming"]}