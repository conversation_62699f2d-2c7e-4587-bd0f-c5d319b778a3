{"name": "@wechatferry/puppet", "type": "module", "version": "0.0.26", "description": "wc<PERSON><PERSON> puppet for we<PERSON><PERSON>", "author": "mrrhq <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/wechatferry/wechatferry#readme", "repository": {"type": "git", "url": "https://github.com/wechatferry/wechatferry"}, "keywords": ["wechat", "wc<PERSON><PERSON>", "robot"], "sideEffects": ["./src/events/index.ts"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "scripts": {"build": "unbuild", "dev": "unbuild --stub"}, "dependencies": {"@wechatferry/agent": "workspace:*", "@wechatferry/core": "workspace:*", "file-box": "^1.4.15", "knex": "^3.1.0", "p-retry": "^6.2.1", "unstorage": "^1.15.0", "wechaty-puppet": "^1.20.2", "xml2js": "^0.6.2"}, "devDependencies": {"@types/xml2js": "^0.4.14"}}