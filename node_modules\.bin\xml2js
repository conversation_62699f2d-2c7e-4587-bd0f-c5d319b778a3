#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/AIhub/wechat/node_modules/.pnpm/fast-xml-parser@3.21.1/node_modules/fast-xml-parser/node_modules:/mnt/d/AIhub/wechat/node_modules/.pnpm/fast-xml-parser@3.21.1/node_modules:/mnt/d/AIhub/wechat/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/AIhub/wechat/node_modules/.pnpm/fast-xml-parser@3.21.1/node_modules/fast-xml-parser/node_modules:/mnt/d/AIhub/wechat/node_modules/.pnpm/fast-xml-parser@3.21.1/node_modules:/mnt/d/AIhub/wechat/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../fast-xml-parser/cli.js" "$@"
else
  exec node  "$basedir/../fast-xml-parser/cli.js" "$@"
fi
