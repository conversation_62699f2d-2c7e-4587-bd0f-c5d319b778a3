'use strict';

const node_url = require('node:url');
const EventEmitter = require('node:events');
const koffi = require('koffi');
const pathe = require('pathe');
const nng = require('@rustup/nng');
const logger$1 = require('@wechatferry/logger');
const wcf = require('./proto/wcf.cjs');
const _package = require('./package.json.cjs');

var _documentCurrentScript = typeof document !== 'undefined' ? document.currentScript : null;
function _interopDefaultCompat (e) { return e && typeof e === 'object' && 'default' in e ? e.default : e; }

const EventEmitter__default = /*#__PURE__*/_interopDefaultCompat(EventEmitter);
const koffi__default = /*#__PURE__*/_interopDefaultCompat(koffi);

var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
const _dirname = typeof __dirname !== "undefined" ? __dirname : pathe.dirname(node_url.fileURLToPath((typeof document === 'undefined' ? require('u' + 'rl').pathToFileURL(__filename).href : (_documentCurrentScript && _documentCurrentScript.src || new URL('sdk.cjs', document.baseURI).href))));
function resolvedWechatferrySDKOptions(options) {
  return {
    debug: false,
    sdkRoot: pathe.resolve(_dirname, `../sdk/v${_package.wcferry.version}`),
    port: 10086,
    host: "127.0.0.1",
    ...options
  };
}
const logger = logger$1.useLogger("core:sdk");
class WechatferrySDK extends EventEmitter__default {
  constructor(options = {}) {
    super();
    __publicField(this, "lib");
    __publicField(this, "options");
    __publicField(this, "messageRecvDisposable");
    logger.debug(`constructor options: ${JSON.stringify(options)}`);
    this.options = resolvedWechatferrySDKOptions(options);
    this.lib = koffi__default.load(pathe.join(this.options.sdkRoot, "sdk.dll"));
  }
  get WxInitSDK() {
    logger.debug("WxInitSDK");
    return this.lib.func("WxInitSDK", "int", ["bool", "int"]);
  }
  get WxDestroySDK() {
    logger.debug("WxDestroySDK");
    return this.lib.func("WxDestroySDK", "void", []);
  }
  get tcpBaseUrl() {
    return `tcp://${this.options.host}`;
  }
  /** 用于发送指令的地址 */
  get cmdUrl() {
    return `${this.tcpBaseUrl}:${this.options.port}`;
  }
  /** 用于接收消息的地址 */
  get msgUrl() {
    return `${this.tcpBaseUrl}:${this.options.port + 1}`;
  }
  /**
   * 初始化 sdk
   * @param debug 是否开启调试
   * @param port 启动的端口
   */
  init(debug = this.options.debug, port = this.options.port) {
    logger.debug(`init debug: ${debug}, port: ${port}`);
    return this.WxInitSDK(debug, port) === 0;
  }
  /**
   * 销毁 sdk
   */
  destroy() {
    logger.debug("destroy");
    this.stopRecvMessage();
    return this.WxDestroySDK();
  }
  get isReceiving() {
    return !!this.messageRecvDisposable;
  }
  /**
   * 启用消息接收
   */
  startRecvMessage() {
    logger.debug("startRecvMessage");
    this.messageRecvDisposable = nng.Socket.recvMessage(this.msgUrl, void 0, (err, buf) => {
      if (err) {
        return logger.error(err);
      }
      const rsp = wcf.wcf.Response.deserialize(buf);
      this.emit("message", rsp.wxmsg);
    });
  }
  /**
   * 停止接收消息
   */
  stopRecvMessage() {
    logger.debug("stopRecvMessage");
    this.messageRecvDisposable?.dispose();
    this.messageRecvDisposable = void 0;
  }
}

exports.WechatferrySDK = WechatferrySDK;
exports.resolvedWechatferrySDKOptions = resolvedWechatferrySDKOptions;
