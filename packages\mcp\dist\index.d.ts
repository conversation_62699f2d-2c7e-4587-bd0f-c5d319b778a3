import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { WechatyInterface } from 'wechaty/impls';
import { WechatferryAgent } from '@wechatferry/agent';
import { WechatferryPuppet } from '@wechatferry/puppet';

interface WechatFerryServerOptions {
    wechaty: WechatyInterface;
}
declare class WechatFerryServer extends McpServer {
    wechaty: WechatyInterface;
    wcf: WechatferryAgent;
    puppet: WechatferryPuppet;
    constructor({ wechaty, }: WechatFerryServerOptions);
    init(): Promise<void>;
}

export { WechatFerryServer };
