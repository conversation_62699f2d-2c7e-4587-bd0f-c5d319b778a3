/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import { SecureServerOptions } from 'http2';
import { SecureContextOptions } from 'tls';
import { ServerInterceptor } from '.';
export interface KeyCertPair {
    private_key: Buffer;
    cert_chain: Buffer;
}
export interface SecureContextWatcher {
    (context: SecureContextOptions | null): void;
}
export declare abstract class ServerCredentials {
    private watchers;
    private latestContextOptions;
    _addWatcher(watcher: SecureContextWatcher): void;
    _removeWatcher(watcher: SecureContextWatcher): void;
    protected updateSecureContextOptions(options: SecureServerOptions | null): void;
    abstract _isSecure(): boolean;
    _getSettings(): SecureServerOptions | null;
    _getInterceptors(): ServerInterceptor[];
    abstract _equals(other: ServerCredentials): boolean;
    static createInsecure(): ServerCredentials;
    static createSsl(rootCerts: Buffer | null, keyCertPairs: KeyCertPair[], checkClientCertificate?: boolean): ServerCredentials;
}
export declare function createServerCredentialsWithInterceptors(credentials: ServerCredentials, interceptors: ServerInterceptor[]): ServerCredentials;
