{"name": "@wechatferry/robot", "type": "module", "version": "0.0.26", "description": "nuxt layers for wechatferry", "author": "mrrhq <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/mrrhq", "homepage": "https://github.com/wechatferry/wechatferry#readme", "repository": {"type": "git", "url": "https://github.com/wechatferry/wechatferry"}, "bugs": "https://github.com/wechatferry/wechatferry/issues", "keywords": ["wechat", "wc<PERSON><PERSON>", "robot", "nuxt", "nuxt-layer"], "main": "./nuxt.config.ts", "files": ["app", "app.config.ts", "app.vue", "assets", "components", "composables", "layouts", "nuxt.config.ts", "nuxt.schema.ts", "plugins", "server", "tokens.config.ts"], "scripts": {"dev": "nuxi dev", "dev:build": "nuxt build", "generate": "nuxt generate", "preview": "nuxt preview", "lint": "eslint ."}, "dependencies": {"@wechatferry/nuxt": "workspace:*", "bullmq": "^5.43.1", "openai": "^4.87.3"}, "devDependencies": {"nuxt": "catalog:"}}