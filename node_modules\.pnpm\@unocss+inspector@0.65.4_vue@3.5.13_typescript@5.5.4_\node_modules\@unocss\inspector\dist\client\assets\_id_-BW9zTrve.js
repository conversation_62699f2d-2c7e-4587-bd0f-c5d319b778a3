import{s as q,t as B,d as W,x as G,y as H,r as R,u as K,z as J,A as L,B as Q,j as n,c as P,g as A,w as k,q as j,C as M,_ as X,D as Y,o as S,e as x,k as w,E as C,i as F,F as z,n as O,h as Z,v as ee,M as te,G as re,H as le,m as se,l as ne,I as ae}from"./index-CQcRy5Wf.js";const D=/^(?!.*\[[^:]+:.+\]$)((?:.+:)?!?)(.*)$/;function ce(e={}){const p=e.prefix??"un-",l=e.prefixedOnly??!1,b=e.trueToNonValued??!1;let u;return{name:"attributify",match(a,{generator:g}){var y,r;const c=q(a);if(!c)return;let t=c[1];if(t.startsWith(p))t=t.slice(p.length);else if(l)return;const d=c[2],[,f="",m=d]=d.match(D)||[];if(m==="~"||b&&m==="true"||!m)return`${f}${t}`;if(u==null){const s=(r=(y=g==null?void 0:g.config)==null?void 0:y.separators)==null?void 0:r.join("|");s?u=new RegExp(`^(.*\\](?:${s}))(\\[[^\\]]+?\\])$`):u=!1}if(u){const[,s,o]=d.match(u)||[];if(o)return`${s}${f}${t}-${o}`}if(f&&m.match(/^[\d.]+$/)){const s=f.split(/([^:]*:)/g).filter(Boolean),o=s.pop()+m,i=s.join("");return[{matcher:`${f}${t}-${m}`},{matcher:`${i}${t}-${o}`}]}return`${f}${t}-${m}`}}}const oe=/(<\w[\w:.$-]*\s)((?:'[^>']*'|"[^>"]*"|`[^>`]*`|\{[^>}]*\}|[^>]*?)*)/g,ie=/(\?|(?!\d|-{2}|-\d)[\w\u00A0-\uFFFF-:%]+)(?:=("[^"]*|'[^']*))?/g,I=/[\s'"`;>]+/;function ue(e){return{name:"attributify",extract:({content:p,cursor:l})=>{const b=p.matchAll(oe);let u,a=0;for(const h of b){const[,_,v]=h,$=h.index+_.length;if(l>$&&l<=$+v.length){a=$,u=v;break}}if(!u)return null;const g=u.matchAll(ie);let c=0,t,d;for(const h of g){const[_,v,$]=h,E=a+h.index;if(l>E&&l<=E+_.length){c=E,t=v,d=$==null?void 0:$.slice(1);break}}if(!t||t==="class"||t==="className"||t===":class")return null;const f=!!(e!=null&&e.prefix)&&t.startsWith(e.prefix);if(e!=null&&e.prefixedOnly&&!f)return null;const m=f?t.slice(e.prefix.length):t;if(d===void 0)return{extracted:m,resolveReplacement(h){const _=f?e.prefix.length:0;return{start:c+_,end:c+t.length,replacement:h}}};const y=c+t.length+2;let r=I.exec(d),s=0,o;for(;r;){const[h]=r;if(l>y+s&&l<=y+s+r.index){o=d.slice(s,s+r.index);break}s+=r.index+h.length,r=I.exec(d.slice(s))}o===void 0&&(o=d.slice(s));const[,i="",V]=o.match(D)||[];return{extracted:`${i}${m}-${V}`,transformSuggestions(h){return h.filter(_=>_.startsWith(`${i}${m}-`)).map(_=>i+_.slice(i.length+m.length+1))},resolveReplacement(h){return{start:s+y,end:s+y+o.length,replacement:i+h.slice(i.length+m.length+1)}}}}}}const de=["v-bind:",":"],T=/[\s'"`;]+/g,N=/<[^>\s]*\s((?:'[^']*'|"[^"]*"|`[^`]*`|\{[^}]*\}|=>|[^>]*?)*)/g,fe=/(\?|(?!\d|-{2}|-\d)[\w\u00A0-\uFFFF:!%.~<-]+)=?(?:"([^"]*)"|'([^']*)'|\{([^}]*)\})?/g,U=["placeholder","fill","opacity","stroke-opacity"];function me(e){const p=(e==null?void 0:e.ignoreAttributes)??U,l=(e==null?void 0:e.nonValuedAttribute)??!0,b=(e==null?void 0:e.trueToNonValued)??!1;return{name:"@unocss/preset-attributify/extractor",extract({code:u}){return Array.from(u.matchAll(N)).flatMap(a=>Array.from((a[1]||"").matchAll(fe))).flatMap(([,a,...g])=>{const c=g.filter(Boolean).join("");if(p.includes(a))return[];for(const t of de)if(a.startsWith(t)){a=a.slice(t.length);break}if(!c){if(B(a)&&l!==!1){const t=[`[${a}=""]`];return b&&t.push(`[${a}="true"]`),t}return[]}return["class","className"].includes(a)?c.split(T).filter(B):N.test(c)?(N.lastIndex=0,this.extract({code:c})):e!=null&&e.prefixedOnly&&e.prefix&&!a.startsWith(e.prefix)?[]:c.split(T).filter(t=>!!t&&t!==":").map(t=>`[${a}~="${t}"]`)})}}}const he=(e={})=>{e.strict=e.strict??!1,e.prefix=e.prefix??"un-",e.prefixedOnly=e.prefixedOnly??!1,e.nonValuedAttribute=e.nonValuedAttribute??!0,e.ignoreAttributes=e.ignoreAttributes??U;const p=[ce(e)],l=[me(e)],b=[ue(e)];return{name:"@unocss/preset-attributify",enforce:"post",variants:p,extractors:l,options:e,autocomplete:{extractors:b},extractorDefault:e.strict?!1:void 0}},xe={key:0,"h-full":"","of-hidden":"",flex:"","flex-col":""},pe={p:"4",grid:"~ cols-4 gap-4"},ye={key:0,"row-span-3":""},ve={key:0,"h-full":"","of-hidden":""},_e=W({__name:"ModuleInfo",props:{id:{}},setup(e){const p=e,{data:l}=G(H(p,"id")),b=p.id.split(/\./g).pop(),u=R(null),a=K(u,"module-scrolls");function g(){fetch(`/__open-in-editor?file=${encodeURIComponent(p.id)}`)}const{extractors:c}=he({strict:!0}),t=J(async()=>{var r;const y=new Set;if(c){const s={code:((r=l.value)==null?void 0:r.code)||""};for(const o of c){const i=await o.extract(s);i==null||i.forEach(V=>y.add(V))}}return Array.from(y).filter(s=>!s.startsWith("[")).filter(s=>{var o,i;return!((i=(o=l.value)==null?void 0:o.matched)!=null&&i.some(({rawSelector:V})=>V===s))})}),d=R(!1),f=R("source"),m=L(Q(()=>{var y;return(y=l.value)==null?void 0:y.css}),d);return(y,r)=>{const s=re,o=le,i=X,V=se,h=ne,_=Y;return n(l)?(S(),P("div",xe,[A(i,{ref_key:"status",ref:u,p0:""},{default:k(()=>{var v;return[x("div",pe,[x("div",null,[r[3]||(r[3]=x("div",{op50:""}," Module ",-1)),x("a",{"cursor-pointer":"",op80:"","hover:op100":"",onClick:g},[A(s,{id:n(l).id,"mr-1":""},null,8,["id"]),r[2]||(r[2]=x("div",{"i-carbon-launch":""},null,-1))])]),x("div",null,[r[4]||(r[4]=x("div",{op50:""}," Matched Rules ",-1)),w(" "+C(n(l).matched.length),1)]),x("div",null,[r[5]||(r[5]=x("div",{op50:""}," CSS Size ",-1)),w(" "+C(((((v=n(l))==null?void 0:v.gzipSize)||0)/1024).toFixed(2))+" KiB ",1),r[6]||(r[6]=x("span",{op50:""},"gzipped",-1))]),n(t).length?(S(),P("div",ye,[r[7]||(r[7]=x("div",{op50:""}," Potentially Unmatched ",-1)),x("code",null,C(n(t).join(", ")),1)])):M("",!0)]),A(o,{modelValue:n(f),"onUpdate:modelValue":r[0]||(r[0]=$=>F(f)?f.value=$:null)},null,8,["modelValue"])]}),_:1},512),n(f)==="source"?(S(),P("div",ve,[A(n(te),null,{default:k(()=>[A(n(z),{size:"50"},{default:k(()=>{var v;return[A(V,{"h-full":"","model-value":n(l).code,"read-only":!0,mode:n(b),matched:(v=n(l).matched)==null?void 0:v.map(({rawSelector:$})=>$),class:"scrolls module-scrolls",style:O(n(a))},null,8,["model-value","mode","matched","style"])]}),_:1}),A(n(z),{size:"50"},{default:k(()=>[x("div",null,[A(h,{border:"l b gray-400/20",title:"Output CSS"},{default:k(()=>[x("label",null,[Z(x("input",{"onUpdate:modelValue":r[1]||(r[1]=v=>F(d)?d.value=v:null),type:"checkbox"},null,512),[[ee,n(d)]]),r[8]||(r[8]=w(" Prettify "))])]),_:1}),A(V,{"h-full":"",border:"l main","model-value":n(m),"read-only":!0,mode:"css",class:"scrolls module-scrolls",style:O(n(a))},null,8,["model-value","style"])])]),_:1})]),_:1})])):(S(),j(_,{key:1,"flex-grow":"","overflow-y-auto":"",selectors:n(l).matched,colors:n(l).colors},null,8,["selectors","colors"]))])):M("",!0)}}}),be=W({__name:"[id]",setup(e){const p=ae();return(l,b)=>{const u=_e;return S(),j(u,{id:n(p).params.id},null,8,["id"])}}});export{be as default};
