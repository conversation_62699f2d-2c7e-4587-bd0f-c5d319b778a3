# 配置文件

# 服务端配置
server:
  # 端口设置
  port: 9201

spring:
  # 配置应用信息
  application:
    # 应用名
    name: wechat-ferry
  # swagger适配
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# 日志配置
logging:
  config: classpath:logback-spring.xml

# 本服务参数
wechat:
  ferry:
    # DLL文件位置
    dll-path: E:\WeChatFerry\clients\java\wechat-ferry-mvn\dll\sdk.dll
    # socket端口
    socket-port: 10086
    # 联系人类型-官方杂号，禁止与其他分类重复(格式：代码|名称)
    contacts-type-mixed:
      - filehelper|文件传输助手
    # 联系人类型-公众号，禁止与其他分类重复(格式：代码|名称)
    contacts-type-official:
      - weixinguanhaozhushou|微信公众平台
    # 需要开启消息处理的群
    open-msg-groups:
      - 53257911728@chatroom
    # 接收消息回调开关
    receive-msg-callback-switch: false
    # 接收消息回调地址
    receive-msg-callback-urls:
      - http://localhost:9001/msg
    # 发送消息回调标识 1-关闭 2-全部回调 3-发送成功才回调
    send-msg-callback-flag: '1'
    # 发送消息回调地址
    send-msg-callback-urls:
      - http://localhost:9001/msg
    # 调用第三方服务客户端成功状态码
    third-party-ok-codes:
      # key:状态码字段 val:状态码值
      code: '200'

