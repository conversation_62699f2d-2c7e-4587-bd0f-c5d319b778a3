import{d as x,r as S,u as b,a as k,b as V,f as B,o as y,c as w,e as s,g as a,w as p,h as C,i as _,v as R,j as l,k as P,n as f,_ as E,l as N,m as U,p as $,q as I}from"./index-CQcRy5Wf.js";const L={"h-full":"",grid:"~ rows-[max-content_1fr]","of-hidden":""},M={"h-full":"","of-hidden":"",grid:"","grid-cols-2":""},T=x({__name:"ReplPlayground",setup(v){const r=S(null),t=b(r,"repl-scrolls"),o=k("unocss:inspector:repl",`<div class="text-sm hover:text-red">
Hello World
</div>`),n=V("unocss-inspector-safelist",!1),{data:c}=B(o,n);return(z,e)=>{var i,m;const g=E,h=N,u=U;return y(),w("div",L,[s("div",{ref_key:"status",ref:r},[a(g,null,{default:p(()=>e[2]||(e[2]=[s("div",null," REPL Playground ",-1),s("div",{op60:""}," Edit your code below to test and play UnoCSS's matching and generating. ",-1)])),_:1}),a(h,{border:"b gray-400/20",title:""},{default:p(()=>[s("label",null,[C(s("input",{"onUpdate:modelValue":e[0]||(e[0]=d=>_(n)?n.value=d:null),type:"checkbox"},null,512),[[R,l(n)]]),e[3]||(e[3]=P(" Include safelist "))])]),_:1})],512),s("div",M,[a(u,{modelValue:l(o),"onUpdate:modelValue":e[1]||(e[1]=d=>_(o)?o.value=d:null),mode:"html",matched:((i=l(c))==null?void 0:i.matched)||[],class:"scrolls repl-scrolls",style:f(l(t))},null,8,["modelValue","matched","style"]),a(u,{border:"l main","model-value":((m=l(c))==null?void 0:m.css)||"/* empty */","read-only":!0,mode:"css",class:"scrolls repl-scrolls",style:f(l(t))},null,8,["model-value","style"])])])}}}),j={};function q(v,r){const t=T;return y(),I(t)}const H=$(j,[["render",q]]);export{H as default};
