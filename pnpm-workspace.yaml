packages:
  - docs
  - playground
  - packages/**
catalog:
  '@antfu/eslint-config': ^3.16.0
  '@iconify-json/carbon': ^1.2.8
  '@nuxt/devtools': ^1.7.0
  '@nuxt/kit': ^3.16.0
  '@nuxt/schema': ^3.16.0
  '@types/fs-extra': ^11.0.4
  '@types/node': ^22.13.10
  bumpp: ^9.11.1
  consola: ^3.4.0
  esno: ^4.8.0
  execa: ^8.0.1
  fs-extra: ^11.3.0
  lint-staged: ^15.5.0
  nuxt: ^3.16.0
  ofetch: ^1.4.1
  pathe: ^1.1.2
  simple-git: ^3.27.0
  simple-git-hooks: ^2.11.1
  std-env: ^3.8.1
  taze: ^0.16.9
  typescript: ^5.5.4
  unbuild: ^2.0.0
  vitepress: ^1.6.3
  vitepress-plugin-group-icons: ^1.3.7
  vitest: ^2.1.9
  vue: ^3.5.13
