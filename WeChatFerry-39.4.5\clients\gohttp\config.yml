# 运行日志
Log:
    Dir: logs # 存储目录，非必要不修改
    Level: info # 记录级别，debug|info|warn|error
    Target: stdout # 输出方式，both|file|null|stdout|stderr

# Wcf 服务
Wcf:
    Address: 127.0.0.1:7601 # 若使用外部地址，请删除 SdkLibrary 选项
    MsgPrint: false # 是否将消息输出到控制台，可用于调试
    SdkLibrary: sdk.dll # wcf 二进制文件路径，留空则不托管（Linux 必须留空）

# Web 服务
Web:
    Address: 127.0.0.1:7600 # 监听地址，外网访问修改为 0.0.0.0:7600
    PushUrl: "" # 消息推送地址，一行一个，留空则不启用
    Storage: storage # 附件存储路径，非必要不修改
    Swagger: true # 是否启用内置接口文档和调试工具
    Token: "" # 使用 Token 验证请求，留空则不验证
