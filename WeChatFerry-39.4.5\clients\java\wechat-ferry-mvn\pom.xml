<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>

    <artifactId>wechat-ferry-mvn</artifactId>
    <packaging>jar</packaging>
    <name>wechat-ferry-mvn</name>
    <version>39.2.4</version>
    <description>WeChatFerry客户端Java-Maven版</description>

    <properties>
        <application.main.class>com.wechat.ferry.WeChatFerryApplication</application.main.class>
        <app.name>wechat-ferry-mvn</app.name>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--thymeleaf-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <!-- 日志 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <!-- 参数校验 -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <!-- swagger3包-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>
        <!-- Alibaba Fastjson -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.52</version>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>
        <!-- httpclient依赖 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.22.2</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.6.0</version>
        </dependency>

        <!-- 配置文件加解密 -->
<!--        <dependency>-->
<!--            <groupId>com.github.ulisesbocchio</groupId>-->
<!--            <artifactId>jasypt-spring-boot-starter</artifactId>-->
<!--            <version>3.0.5</version>-->
<!--        </dependency>-->
        <!-- orm 相关 -->
<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus-annotation</artifactId>-->
<!--            <version>3.5.7</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus-extension</artifactId>-->
<!--            <version>3.5.7</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus-boot-starter</artifactId>-->
<!--            <version>3.5.7</version>-->
<!--        </dependency>-->
        <!-- mysql数据库驱动 -->
<!--        <dependency>-->
<!--            <groupId>com.mysql</groupId>-->
<!--            <artifactId>mysql-connector-j</artifactId>-->
<!--            <version>8.0.33</version>-->
<!--        </dependency>-->

        <!-- 引入本地lib -->
        <dependency>
            <groupId>io.sisu.nng</groupId>
            <artifactId>nng-java</artifactId>
            <version>1.4.0-SNAPSHOT</version>
            <!-- system表示依赖不是由maven仓库，而是本地的jar包 -->
            <scope>system</scope>
            <!-- jar包位置 -->
            <systemPath>${project.basedir}/src/main/resources/libs/nng-java-1.4.0-SNAPSHOT.jar</systemPath>
        </dependency>
    </dependencies>

    <build>
        <!-- 最终 Jar 包的名字 -->
        <finalName>${app.name}-${project.version}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 排除特定资源 -->
                <excludes>
                    <exclude>bin/*</exclude>
                    <exclude>proto/*</exclude>
                    <exclude>sql/*</exclude>
                </excludes>
            </resource>
        </resources>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.5.0.Final</version>
            </extension>
        </extensions>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <!-- 主类入口 -->
                    <mainClass>${application.main.class}</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 同时添加maven插件，用于编译protobuf生成java文件 -->
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>0.5.0</version>
                <configuration>
                    <!-- 注意，需要与 com.google.protobuf:protobuf-java 一致 -->
                    <protocArtifact>
                        com.google.protobuf:protoc:3.22.2:exe:${os.detected.classifier}
                    </protocArtifact>
                    <!-- 默认值，proto源文件路径 -->
                    <protoSourceRoot>${project.basedir}/src/main/resources/proto</protoSourceRoot>
                    <!-- 默认值，proto目标java文件路径，如果不指定则只在jar与编译中生成 -->
                    <outputDirectory>src/main/java</outputDirectory>
                    <pluginId>grpc-java</pluginId>
                    <!-- 设置是否在生成java文件之前清空outputDirectory的文件，默认值为true，设置为false时也会覆盖同名文件 -->
                    <clearOutputDirectory>false</clearOutputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
