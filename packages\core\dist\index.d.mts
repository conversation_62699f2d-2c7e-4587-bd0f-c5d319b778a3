import EventEmitter, { EventEmitter as EventEmitter$1 } from 'node:events';
import { Buffer } from 'node:buffer';
import { FileBoxInterface, FileBox } from 'file-box';
import * as pb_1 from 'google-protobuf';
import { Socket } from '@rustup/nng';

/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 5.27.1
 * source: proto/wcf.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */

declare namespace wcf {
    enum Functions {
        FUNC_RESERVED = 0,
        FUNC_IS_LOGIN = 1,
        FUNC_GET_SELF_WXID = 16,
        FUNC_GET_MSG_TYPES = 17,
        FUNC_GET_CONTACTS = 18,
        FUNC_GET_DB_NAMES = 19,
        FUNC_GET_DB_TABLES = 20,
        FUNC_GET_USER_INFO = 21,
        FUNC_GET_AUDIO_MSG = 22,
        FUNC_SEND_TXT = 32,
        FUNC_SEND_IMG = 33,
        FUNC_SEND_FILE = 34,
        FUNC_SEND_XML = 35,
        FUNC_SEND_EMOTION = 36,
        FUNC_SEND_RICH_TXT = 37,
        FUNC_SEND_PAT_MSG = 38,
        FUNC_FORWARD_MSG = 39,
        FUNC_ENABLE_RECV_TXT = 48,
        FUNC_DISABLE_RECV_TXT = 64,
        FUNC_EXEC_DB_QUERY = 80,
        FUNC_ACCEPT_FRIEND = 81,
        FUNC_RECV_TRANSFER = 82,
        FUNC_REFRESH_PYQ = 83,
        FUNC_DOWNLOAD_ATTACH = 84,
        FUNC_GET_CONTACT_INFO = 85,
        FUNC_REVOKE_MSG = 86,
        FUNC_REFRESH_QRCODE = 87,
        FUNC_DECRYPT_IMAGE = 96,
        FUNC_EXEC_OCR = 97,
        FUNC_ADD_ROOM_MEMBERS = 112,
        FUNC_DEL_ROOM_MEMBERS = 113,
        FUNC_INV_ROOM_MEMBERS = 114
    }
    class Request extends pb_1.Message {
        #private;
        constructor(data?: any[] | ({
            func?: Functions;
        } & (({
            empty?: Empty;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: string;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: TextMsg;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: PathMsg;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: DbQuery;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: Verification;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: MemberMgmt;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: XmlMsg;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: DecPath;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: Transfer;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: string;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: boolean;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: AttachMsg;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: AudioMsg;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: RichText;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: PatMsg;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: ForwardMsg;
        }))));
        get func(): Functions;
        set func(value: Functions);
        get empty(): Empty;
        set empty(value: Empty);
        get has_empty(): boolean;
        get str(): string;
        set str(value: string);
        get has_str(): boolean;
        get txt(): TextMsg;
        set txt(value: TextMsg);
        get has_txt(): boolean;
        get file(): PathMsg;
        set file(value: PathMsg);
        get has_file(): boolean;
        get query(): DbQuery;
        set query(value: DbQuery);
        get has_query(): boolean;
        get v(): Verification;
        set v(value: Verification);
        get has_v(): boolean;
        get m(): MemberMgmt;
        set m(value: MemberMgmt);
        get has_m(): boolean;
        get xml(): XmlMsg;
        set xml(value: XmlMsg);
        get has_xml(): boolean;
        get dec(): DecPath;
        set dec(value: DecPath);
        get has_dec(): boolean;
        get tf(): Transfer;
        set tf(value: Transfer);
        get has_tf(): boolean;
        get ui64(): string;
        set ui64(value: string);
        get has_ui64(): boolean;
        get flag(): boolean;
        set flag(value: boolean);
        get has_flag(): boolean;
        get att(): AttachMsg;
        set att(value: AttachMsg);
        get has_att(): boolean;
        get am(): AudioMsg;
        set am(value: AudioMsg);
        get has_am(): boolean;
        get rt(): RichText;
        set rt(value: RichText);
        get has_rt(): boolean;
        get pm(): PatMsg;
        set pm(value: PatMsg);
        get has_pm(): boolean;
        get fm(): ForwardMsg;
        set fm(value: ForwardMsg);
        get has_fm(): boolean;
        get msg(): "empty" | "str" | "txt" | "file" | "query" | "v" | "m" | "xml" | "dec" | "tf" | "ui64" | "flag" | "att" | "am" | "rt" | "pm" | "fm" | "none";
        static fromObject(data: {
            func?: Functions;
            empty?: ReturnType<typeof Empty.prototype.toObject>;
            str?: string;
            txt?: ReturnType<typeof TextMsg.prototype.toObject>;
            file?: ReturnType<typeof PathMsg.prototype.toObject>;
            query?: ReturnType<typeof DbQuery.prototype.toObject>;
            v?: ReturnType<typeof Verification.prototype.toObject>;
            m?: ReturnType<typeof MemberMgmt.prototype.toObject>;
            xml?: ReturnType<typeof XmlMsg.prototype.toObject>;
            dec?: ReturnType<typeof DecPath.prototype.toObject>;
            tf?: ReturnType<typeof Transfer.prototype.toObject>;
            ui64?: string;
            flag?: boolean;
            att?: ReturnType<typeof AttachMsg.prototype.toObject>;
            am?: ReturnType<typeof AudioMsg.prototype.toObject>;
            rt?: ReturnType<typeof RichText.prototype.toObject>;
            pm?: ReturnType<typeof PatMsg.prototype.toObject>;
            fm?: ReturnType<typeof ForwardMsg.prototype.toObject>;
        }): Request;
        toObject(): {
            func?: Functions;
            empty?: ReturnType<typeof Empty.prototype.toObject>;
            str?: string;
            txt?: ReturnType<typeof TextMsg.prototype.toObject>;
            file?: ReturnType<typeof PathMsg.prototype.toObject>;
            query?: ReturnType<typeof DbQuery.prototype.toObject>;
            v?: ReturnType<typeof Verification.prototype.toObject>;
            m?: ReturnType<typeof MemberMgmt.prototype.toObject>;
            xml?: ReturnType<typeof XmlMsg.prototype.toObject>;
            dec?: ReturnType<typeof DecPath.prototype.toObject>;
            tf?: ReturnType<typeof Transfer.prototype.toObject>;
            ui64?: string;
            flag?: boolean;
            att?: ReturnType<typeof AttachMsg.prototype.toObject>;
            am?: ReturnType<typeof AudioMsg.prototype.toObject>;
            rt?: ReturnType<typeof RichText.prototype.toObject>;
            pm?: ReturnType<typeof PatMsg.prototype.toObject>;
            fm?: ReturnType<typeof ForwardMsg.prototype.toObject>;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Request;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): Request;
    }
    class Response extends pb_1.Message {
        #private;
        constructor(data?: any[] | ({
            func?: Functions;
        } & (({
            status?: number;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: string;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: WxMsg;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: MsgTypes;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: RpcContacts;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: DbNames;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: DbTables;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: DbRows;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: UserInfo;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: OcrMsg;
        }))));
        get func(): Functions;
        set func(value: Functions);
        get status(): number;
        set status(value: number);
        get has_status(): boolean;
        get str(): string;
        set str(value: string);
        get has_str(): boolean;
        get wxmsg(): WxMsg;
        set wxmsg(value: WxMsg);
        get has_wxmsg(): boolean;
        get types(): MsgTypes;
        set types(value: MsgTypes);
        get has_types(): boolean;
        get contacts(): RpcContacts;
        set contacts(value: RpcContacts);
        get has_contacts(): boolean;
        get dbs(): DbNames;
        set dbs(value: DbNames);
        get has_dbs(): boolean;
        get tables(): DbTables;
        set tables(value: DbTables);
        get has_tables(): boolean;
        get rows(): DbRows;
        set rows(value: DbRows);
        get has_rows(): boolean;
        get ui(): UserInfo;
        set ui(value: UserInfo);
        get has_ui(): boolean;
        get ocr(): OcrMsg;
        set ocr(value: OcrMsg);
        get has_ocr(): boolean;
        get msg(): "str" | "none" | "status" | "wxmsg" | "types" | "contacts" | "dbs" | "tables" | "rows" | "ui" | "ocr";
        static fromObject(data: {
            func?: Functions;
            status?: number;
            str?: string;
            wxmsg?: ReturnType<typeof WxMsg.prototype.toObject>;
            types?: ReturnType<typeof MsgTypes.prototype.toObject>;
            contacts?: ReturnType<typeof RpcContacts.prototype.toObject>;
            dbs?: ReturnType<typeof DbNames.prototype.toObject>;
            tables?: ReturnType<typeof DbTables.prototype.toObject>;
            rows?: ReturnType<typeof DbRows.prototype.toObject>;
            ui?: ReturnType<typeof UserInfo.prototype.toObject>;
            ocr?: ReturnType<typeof OcrMsg.prototype.toObject>;
        }): Response;
        toObject(): {
            func?: Functions;
            status?: number;
            str?: string;
            wxmsg?: ReturnType<typeof WxMsg.prototype.toObject>;
            types?: ReturnType<typeof MsgTypes.prototype.toObject>;
            contacts?: ReturnType<typeof RpcContacts.prototype.toObject>;
            dbs?: ReturnType<typeof DbNames.prototype.toObject>;
            tables?: ReturnType<typeof DbTables.prototype.toObject>;
            rows?: ReturnType<typeof DbRows.prototype.toObject>;
            ui?: ReturnType<typeof UserInfo.prototype.toObject>;
            ocr?: ReturnType<typeof OcrMsg.prototype.toObject>;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Response;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): Response;
    }
    class Empty extends pb_1.Message {
        #private;
        constructor(data?: any[] | {});
        static fromObject(data: {}): Empty;
        toObject(): {};
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Empty;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): Empty;
    }
    class WxMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            is_self?: boolean;
            is_group?: boolean;
            id?: string;
            type?: number;
            ts?: number;
            roomid?: string;
            content?: string;
            sender?: string;
            sign?: string;
            thumb?: string;
            extra?: string;
            xml?: string;
        });
        get is_self(): boolean;
        set is_self(value: boolean);
        get is_group(): boolean;
        set is_group(value: boolean);
        get id(): string;
        set id(value: string);
        get type(): number;
        set type(value: number);
        get ts(): number;
        set ts(value: number);
        get roomid(): string;
        set roomid(value: string);
        get content(): string;
        set content(value: string);
        get sender(): string;
        set sender(value: string);
        get sign(): string;
        set sign(value: string);
        get thumb(): string;
        set thumb(value: string);
        get extra(): string;
        set extra(value: string);
        get xml(): string;
        set xml(value: string);
        static fromObject(data: {
            is_self?: boolean;
            is_group?: boolean;
            id?: string;
            type?: number;
            ts?: number;
            roomid?: string;
            content?: string;
            sender?: string;
            sign?: string;
            thumb?: string;
            extra?: string;
            xml?: string;
        }): WxMsg;
        toObject(): {
            is_self?: boolean;
            is_group?: boolean;
            id?: string;
            type?: number;
            ts?: number;
            roomid?: string;
            content?: string;
            sender?: string;
            sign?: string;
            thumb?: string;
            extra?: string;
            xml?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): WxMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): WxMsg;
    }
    class TextMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            msg?: string;
            receiver?: string;
            aters?: string;
        });
        get msg(): string;
        set msg(value: string);
        get receiver(): string;
        set receiver(value: string);
        get aters(): string;
        set aters(value: string);
        static fromObject(data: {
            msg?: string;
            receiver?: string;
            aters?: string;
        }): TextMsg;
        toObject(): {
            msg?: string;
            receiver?: string;
            aters?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): TextMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): TextMsg;
    }
    class PathMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            path?: string;
            receiver?: string;
        });
        get path(): string;
        set path(value: string);
        get receiver(): string;
        set receiver(value: string);
        static fromObject(data: {
            path?: string;
            receiver?: string;
        }): PathMsg;
        toObject(): {
            path?: string;
            receiver?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PathMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): PathMsg;
    }
    class XmlMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            receiver?: string;
            content?: string;
            path?: string;
            type?: number;
        });
        get receiver(): string;
        set receiver(value: string);
        get content(): string;
        set content(value: string);
        get path(): string;
        set path(value: string);
        get type(): number;
        set type(value: number);
        static fromObject(data: {
            receiver?: string;
            content?: string;
            path?: string;
            type?: number;
        }): XmlMsg;
        toObject(): {
            receiver?: string;
            content?: string;
            path?: string;
            type?: number;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): XmlMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): XmlMsg;
    }
    class MsgTypes extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            types?: Map<number, string>;
        });
        get types(): Map<number, string>;
        set types(value: Map<number, string>);
        static fromObject(data: {
            types?: {
                [key: number]: string;
            };
        }): MsgTypes;
        toObject(): {
            types?: {
                [key: number]: string;
            };
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MsgTypes;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): MsgTypes;
    }
    class RpcContact extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            wxid?: string;
            code?: string;
            remark?: string;
            name?: string;
            country?: string;
            province?: string;
            city?: string;
            gender?: number;
        });
        get wxid(): string;
        set wxid(value: string);
        get code(): string;
        set code(value: string);
        get remark(): string;
        set remark(value: string);
        get name(): string;
        set name(value: string);
        get country(): string;
        set country(value: string);
        get province(): string;
        set province(value: string);
        get city(): string;
        set city(value: string);
        get gender(): number;
        set gender(value: number);
        static fromObject(data: {
            wxid?: string;
            code?: string;
            remark?: string;
            name?: string;
            country?: string;
            province?: string;
            city?: string;
            gender?: number;
        }): RpcContact;
        toObject(): {
            wxid?: string;
            code?: string;
            remark?: string;
            name?: string;
            country?: string;
            province?: string;
            city?: string;
            gender?: number;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RpcContact;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): RpcContact;
    }
    class RpcContacts extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            contacts?: RpcContact[];
        });
        get contacts(): RpcContact[];
        set contacts(value: RpcContact[]);
        static fromObject(data: {
            contacts?: ReturnType<typeof RpcContact.prototype.toObject>[];
        }): RpcContacts;
        toObject(): {
            contacts?: ReturnType<typeof RpcContact.prototype.toObject>[];
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RpcContacts;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): RpcContacts;
    }
    class DbNames extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            names?: string[];
        });
        get names(): string[];
        set names(value: string[]);
        static fromObject(data: {
            names?: string[];
        }): DbNames;
        toObject(): {
            names?: string[];
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbNames;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): DbNames;
    }
    class DbTable extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            name?: string;
            sql?: string;
        });
        get name(): string;
        set name(value: string);
        get sql(): string;
        set sql(value: string);
        static fromObject(data: {
            name?: string;
            sql?: string;
        }): DbTable;
        toObject(): {
            name?: string;
            sql?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbTable;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): DbTable;
    }
    class DbTables extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            tables?: DbTable[];
        });
        get tables(): DbTable[];
        set tables(value: DbTable[]);
        static fromObject(data: {
            tables?: ReturnType<typeof DbTable.prototype.toObject>[];
        }): DbTables;
        toObject(): {
            tables?: ReturnType<typeof DbTable.prototype.toObject>[];
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbTables;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): DbTables;
    }
    class DbQuery extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            db?: string;
            sql?: string;
        });
        get db(): string;
        set db(value: string);
        get sql(): string;
        set sql(value: string);
        static fromObject(data: {
            db?: string;
            sql?: string;
        }): DbQuery;
        toObject(): {
            db?: string;
            sql?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbQuery;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): DbQuery;
    }
    class DbField extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            type?: number;
            column?: string;
            content?: Uint8Array;
        });
        get type(): number;
        set type(value: number);
        get column(): string;
        set column(value: string);
        get content(): Uint8Array;
        set content(value: Uint8Array);
        static fromObject(data: {
            type?: number;
            column?: string;
            content?: Uint8Array;
        }): DbField;
        toObject(): {
            type?: number;
            column?: string;
            content?: Uint8Array;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbField;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): DbField;
    }
    class DbRow extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            fields?: DbField[];
        });
        get fields(): DbField[];
        set fields(value: DbField[]);
        static fromObject(data: {
            fields?: ReturnType<typeof DbField.prototype.toObject>[];
        }): DbRow;
        toObject(): {
            fields?: ReturnType<typeof DbField.prototype.toObject>[];
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbRow;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): DbRow;
    }
    class DbRows extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            rows?: DbRow[];
        });
        get rows(): DbRow[];
        set rows(value: DbRow[]);
        static fromObject(data: {
            rows?: ReturnType<typeof DbRow.prototype.toObject>[];
        }): DbRows;
        toObject(): {
            rows?: ReturnType<typeof DbRow.prototype.toObject>[];
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbRows;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): DbRows;
    }
    class Verification extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            v3?: string;
            v4?: string;
            scene?: number;
        });
        get v3(): string;
        set v3(value: string);
        get v4(): string;
        set v4(value: string);
        get scene(): number;
        set scene(value: number);
        static fromObject(data: {
            v3?: string;
            v4?: string;
            scene?: number;
        }): Verification;
        toObject(): {
            v3?: string;
            v4?: string;
            scene?: number;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Verification;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): Verification;
    }
    class MemberMgmt extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            roomid?: string;
            wxids?: string;
        });
        get roomid(): string;
        set roomid(value: string);
        get wxids(): string;
        set wxids(value: string);
        static fromObject(data: {
            roomid?: string;
            wxids?: string;
        }): MemberMgmt;
        toObject(): {
            roomid?: string;
            wxids?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MemberMgmt;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): MemberMgmt;
    }
    class UserInfo extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            wxid?: string;
            name?: string;
            mobile?: string;
            home?: string;
        });
        get wxid(): string;
        set wxid(value: string);
        get name(): string;
        set name(value: string);
        get mobile(): string;
        set mobile(value: string);
        get home(): string;
        set home(value: string);
        static fromObject(data: {
            wxid?: string;
            name?: string;
            mobile?: string;
            home?: string;
        }): UserInfo;
        toObject(): {
            wxid?: string;
            name?: string;
            mobile?: string;
            home?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): UserInfo;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): UserInfo;
    }
    class DecPath extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            src?: string;
            dst?: string;
        });
        get src(): string;
        set src(value: string);
        get dst(): string;
        set dst(value: string);
        static fromObject(data: {
            src?: string;
            dst?: string;
        }): DecPath;
        toObject(): {
            src?: string;
            dst?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DecPath;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): DecPath;
    }
    class Transfer extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            wxid?: string;
            tfid?: string;
            taid?: string;
        });
        get wxid(): string;
        set wxid(value: string);
        get tfid(): string;
        set tfid(value: string);
        get taid(): string;
        set taid(value: string);
        static fromObject(data: {
            wxid?: string;
            tfid?: string;
            taid?: string;
        }): Transfer;
        toObject(): {
            wxid?: string;
            tfid?: string;
            taid?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Transfer;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): Transfer;
    }
    class AttachMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            id?: string;
            thumb?: string;
            extra?: string;
        });
        get id(): string;
        set id(value: string);
        get thumb(): string;
        set thumb(value: string);
        get extra(): string;
        set extra(value: string);
        static fromObject(data: {
            id?: string;
            thumb?: string;
            extra?: string;
        }): AttachMsg;
        toObject(): {
            id?: string;
            thumb?: string;
            extra?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): AttachMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): AttachMsg;
    }
    class AudioMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            id?: string;
            dir?: string;
        });
        get id(): string;
        set id(value: string);
        get dir(): string;
        set dir(value: string);
        static fromObject(data: {
            id?: string;
            dir?: string;
        }): AudioMsg;
        toObject(): {
            id?: string;
            dir?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): AudioMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): AudioMsg;
    }
    class RichText extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            name?: string;
            account?: string;
            title?: string;
            digest?: string;
            url?: string;
            thumburl?: string;
            receiver?: string;
        });
        get name(): string;
        set name(value: string);
        get account(): string;
        set account(value: string);
        get title(): string;
        set title(value: string);
        get digest(): string;
        set digest(value: string);
        get url(): string;
        set url(value: string);
        get thumburl(): string;
        set thumburl(value: string);
        get receiver(): string;
        set receiver(value: string);
        static fromObject(data: {
            name?: string;
            account?: string;
            title?: string;
            digest?: string;
            url?: string;
            thumburl?: string;
            receiver?: string;
        }): RichText;
        toObject(): {
            name?: string;
            account?: string;
            title?: string;
            digest?: string;
            url?: string;
            thumburl?: string;
            receiver?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RichText;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): RichText;
    }
    class PatMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            roomid?: string;
            wxid?: string;
        });
        get roomid(): string;
        set roomid(value: string);
        get wxid(): string;
        set wxid(value: string);
        static fromObject(data: {
            roomid?: string;
            wxid?: string;
        }): PatMsg;
        toObject(): {
            roomid?: string;
            wxid?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PatMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): PatMsg;
    }
    class OcrMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            status?: number;
            result?: string;
        });
        get status(): number;
        set status(value: number);
        get result(): string;
        set result(value: string);
        static fromObject(data: {
            status?: number;
            result?: string;
        }): OcrMsg;
        toObject(): {
            status?: number;
            result?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): OcrMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): OcrMsg;
    }
    class ForwardMsg extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            id?: string;
            receiver?: string;
        });
        get id(): string;
        set id(value: string);
        get receiver(): string;
        set receiver(value: string);
        static fromObject(data: {
            id?: string;
            receiver?: string;
        }): ForwardMsg;
        toObject(): {
            id?: string;
            receiver?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ForwardMsg;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): ForwardMsg;
    }
    class RoomData extends pb_1.Message {
        #private;
        constructor(data?: any[] | ({
            members?: RoomData.RoomMember[];
            field_3?: number;
            capacity?: number;
            field_7?: number;
            field_8?: number;
            admins?: string[];
        } & (({
            field_2?: number;
        }) | ({
            field_4?: number;
        }) | ({
            field_6?: string;
        }))));
        get members(): RoomData.RoomMember[];
        set members(value: RoomData.RoomMember[]);
        get field_2(): number;
        set field_2(value: number);
        get has_field_2(): boolean;
        get field_3(): number;
        set field_3(value: number);
        get field_4(): number;
        set field_4(value: number);
        get has_field_4(): boolean;
        get capacity(): number;
        set capacity(value: number);
        get field_6(): string;
        set field_6(value: string);
        get has_field_6(): boolean;
        get field_7(): number;
        set field_7(value: number);
        get field_8(): number;
        set field_8(value: number);
        get admins(): string[];
        set admins(value: string[]);
        get _field_2(): "none" | "field_2";
        get _field_4(): "none" | "field_4";
        get _field_6(): "none" | "field_6";
        static fromObject(data: {
            members?: ReturnType<typeof RoomData.RoomMember.prototype.toObject>[];
            field_2?: number;
            field_3?: number;
            field_4?: number;
            capacity?: number;
            field_6?: string;
            field_7?: number;
            field_8?: number;
            admins?: string[];
        }): RoomData;
        toObject(): {
            members?: ReturnType<typeof RoomData.RoomMember.prototype.toObject>[];
            field_2?: number;
            field_3?: number;
            field_4?: number;
            capacity?: number;
            field_6?: string;
            field_7?: number;
            field_8?: number;
            admins?: string[];
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RoomData;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): RoomData;
    }
    namespace RoomData {
        class RoomMember extends pb_1.Message {
            #private;
            constructor(data?: any[] | ({
                wxid?: string;
                state?: number;
            } & (({
                name?: string;
            }))));
            get wxid(): string;
            set wxid(value: string);
            get name(): string;
            set name(value: string);
            get has_name(): boolean;
            get state(): number;
            set state(value: number);
            get _name(): "none" | "name";
            static fromObject(data: {
                wxid?: string;
                name?: string;
                state?: number;
            }): RoomMember;
            toObject(): {
                wxid?: string;
                name?: string;
                state?: number;
            };
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RoomMember;
            serializeBinary(): Uint8Array;
            static deserializeBinary(bytes: Uint8Array): RoomMember;
        }
    }
}

declare function resolvedWechatferrySDKOptions(options: WechatferrySDKUserOptions): WechatferrySDKOptions;
interface WechatferrySDKEventMap {
    message: [wcf.WxMsg];
}
interface WechatferrySDKImpl extends EventEmitter<WechatferrySDKEventMap> {
    init: (debug?: boolean, port?: number) => boolean;
    destroy: () => void;
    startRecvMessage: () => void;
    stopRecvMessage: () => void;
    get isReceiving(): boolean;
    get cmdUrl(): string;
    get msgUrl(): string;
}
declare class WechatferrySDK extends EventEmitter<WechatferrySDKEventMap> implements WechatferrySDKImpl {
    private lib;
    private options;
    private messageRecvDisposable?;
    constructor(options?: WechatferrySDKUserOptions);
    private get WxInitSDK();
    private get WxDestroySDK();
    private get tcpBaseUrl();
    /** 用于发送指令的地址 */
    get cmdUrl(): string;
    /** 用于接收消息的地址 */
    get msgUrl(): string;
    /**
     * 初始化 sdk
     * @param debug 是否开启调试
     * @param port 启动的端口
     */
    init(debug?: boolean, port?: number): boolean;
    /**
     * 销毁 sdk
     */
    destroy(): void;
    get isReceiving(): boolean;
    /**
     * 启用消息接收
     */
    startRecvMessage(): void;
    /**
     * 停止接收消息
     */
    stopRecvMessage(): void;
}

interface WechatferrySDKUserOptions {
    /**
     * dll 文件所在目录
     * @default resolve(__dirname, '../sdk')
     */
    sdkRoot?: string;
    /**
     * sdk 端口
     * @default 10086
     */
    port?: number;
    /**
     * sdk host
     * @default '127.0.0.1'
     */
    host?: string;
    /**
     * 是否启用 dll 的 debug 模式
     * @default false
     */
    debug?: boolean;
}
interface WechatferrySDKOptions extends Required<WechatferrySDKUserOptions> {
}
interface WechatferryUserOptions {
    /**
     * sdk instance
     */
    sdk?: WechatferrySDKImpl;
    /**
     * socket instance
     */
    socket?: Socket;
}
interface WechatferryOptions extends Required<WechatferryUserOptions> {
}
type ToPlainType<T extends {
    toObject: () => unknown;
}> = Required<ReturnType<T['toObject']>>;
declare enum WechatAppMessageType {
    Text = 1,
    Img = 2,
    Audio = 3,
    Video = 4,
    Url = 5,
    Attach = 6,
    Open = 7,
    Emoji = 8,
    VoiceRemind = 9,
    ScanGood = 10,
    Good = 13,
    Emotion = 15,
    CardTicket = 16,
    RealtimeShareLocation = 17,
    ChatHistory = 19,
    MiniProgram = 33,
    MiniProgramApp = 36,// this is forwardable mini program
    Channels = 51,// 视频号
    GroupNote = 53,
    ReferMsg = 57,
    Transfers = 2000,
    RedEnvelopes = 2001,
    ReaderType = 100001
}
declare enum WechatMessageType {
    Moment = 0,
    Text = 1,
    Image = 3,
    Voice = 34,
    VerifyMsg = 37,
    PossibleFriendMsg = 40,
    ShareCard = 42,
    Video = 43,
    Emoticon = 47,
    Location = 48,
    App = 49,
    VoipMsg = 50,
    StatusNotify = 51,
    VoipNotify = 52,
    VoipInvite = 53,
    MicroVideo = 62,
    VerifyMsgEnterprise = 65,
    Transfer = 2000,// 转账
    RedEnvelope = 2001,// 红包
    MiniProgram = 2002,// 小程序
    GroupInvite = 2003,// 群邀请
    File = 2004,// 文件消息
    SysNotice = 9999,
    Sys = 10000,
    Recalled = 10002
}
type UserInfo$1 = ToPlainType<wcf.UserInfo>;
type Contact = ToPlainType<wcf.RpcContact>;
type DbTable$1 = ToPlainType<wcf.DbTable>;
interface WxMsg$1 extends ToPlainType<wcf.WxMsg> {
    type: WechatMessageType;
}

declare function resolvedWechatferryOptions(options: WechatferryUserOptions): WechatferryOptions;
interface WechatferryEventMap {
    message: [WxMsg$1];
    sended: [string];
}
declare class Wechatferry extends EventEmitter$1<WechatferryEventMap> {
    private sdk;
    private socket;
    constructor(options?: WechatferryUserOptions);
    /**
     * 启动 wcf
     */
    start(): void;
    /**
     * 停止 wcf
     */
    stop(): void;
    /**
     * 重置 SDK
     *
     * @description 退出登录后 sdk 的 rpc 就挂了，需要重新来
     */
    resetSdk(): void;
    /**
     * 开始接收消息
     */
    startRecvMessage(): number | undefined;
    /**
     * 停止接收消息
     */
    stopRecvMessage(): void;
    /** 发送 WCF Function Request */
    send(req: wcf.Request): wcf.Response;
    /** 是否登录 */
    isLogin(): boolean;
    /** 登录用户 wxid */
    getSelfWxid(): string;
    /** 消息类型 */
    getMsgTypes(): Map<number, string>;
    /** 联系人 */
    getContacts(): Required<{
        wxid?: string;
        code?: string;
        remark?: string;
        name?: string;
        country?: string;
        province?: string;
        city?: string;
        gender?: number;
    }>[];
    /** 数据库列表 */
    getDbNames(): string[];
    /**
     * 数据库表列表
     *
     * @param db 数据库名称
     */
    getDbTables(db: string): Required<{
        name?: string;
        sql?: string;
    }>[];
    /**
     * 用户信息
     *
     */
    getUserInfo(): UserInfo$1;
    /**
     * 获取语音消息并转成 MP3
     *
     * @param id 消息 id
     * @param dir MP3 保存目录（目录不存在会出错）
     * @returns 成功返回存储路径
     */
    getAudioMsg(id: string, dir: string): string;
    /**
     * 发送文本消息
     * @param msg 要发送的消息，换行使用 `\n` （单杠）；如果 @ 人的话，需要带上跟 `aters` 里数量相同的 @
     * @param receiver 消息接收人，wxid 或者 roomId
     * @param mentions 要 @ 的 wxid，多个用逗号分隔；`@所有人` 只需要 `notify@all`
     * @returns 0 为成功，其他失败
     */
    sendTxt(msg: string, receiver: string, mentions?: string[]): number;
    /**
     * 发送图片消息
     *
     * @param image 图片文件
     * @param receiver 消息接收人，wxid 或者 roomId
     * @returns 0 为成功，其他失败
     */
    sendImg(image: FileBoxInterface, receiver: string): Promise<number>;
    /**
     * 发送文件消息
     *
     * @param file 图片文件
     * @param receiver 消息接收人，wxid 或者 roomId
     * @returns 0 为成功，其他失败
     */
    sendFile(file: FileBoxInterface, receiver: string): Promise<number>;
    /**
     * 发送 XML 消息
     */
    sendXml(xml: Omit<ReturnType<wcf.XmlMsg['toObject']>, 'receiver'>, receiver: string): number;
    /**
     * 发送表情消息
     * @param emotion 表情路径
     * @param receiver 消息接收人，wxid 或者 roomId
     * @returns 0 为成功，其他失败
     */
    sendEmotion(emotion: FileBox, receiver: string): Promise<number>;
    /**
     * 发送富文本消息
     *  卡片样式：
     *```md
     * |--------------------------------------|
     * | title， 最长两行                       |
     * | (长标题， 标题短的话这行没有)             |
     * | digest, 最多三行，会占位    |--------|  |
     * | digest, 最多三行，会占位    |thumburl|  |
     * | digest, 最多三行，会占位    |--------|  |
     * | (account logo) name                  |
     * |--------------------------------------|
     *```
     * @param desc 富文本
     * @param desc.name 左下显示的名字
     * @param desc.account 填公众号 id 可以显示对应的头像（gh_ 开头的）
     * @param desc.title 标题，最多两行
     * @param desc.digest 摘要，三行
     * @param desc.url 点击后跳转的链接
     * @param desc.thumburl 缩略图的链接
     * @param receiver 接收人, wxid 或者 roomId
     * @returns 0 为成功，其他失败
     */
    sendRichText(desc: Omit<ReturnType<wcf.RichText['toObject']>, 'receiver'>, receiver: string): number;
    /**
     * 拍一拍群友
     * @param roomId 群 id
     * @param wxid 要拍的群友的 wxid
     * @returns 1 为成功，其他失败
     */
    sendPatMsg(roomId: string, wxid: string): number;
    /**
     * 转发消息
     * @description 可以转发文本、图片、表情、甚至各种 XML；语音也行，不过效果嘛，自己验证吧。
     * @param id (uint64 in string format): 消息 id
     * @param receiver string 消息接收人，wxid 或者 roomId
     * @returns 1 为成功，其他失败
     */
    forwardMsg(id: string, receiver: string): number;
    /**
     * 执行 SQL 查询
     * @param db 数据库名
     * @param sql SQL 语句
     */
    execDbQuery(db: string, sql: string): {
        [k: string]: string | number | Buffer | undefined;
    }[];
    /**
     * 通过好友申请
     * @param v3 加密用户名 (好友申请消息里 v3 开头的字符串)
     * @param v4 Ticket (好友申请消息里 v4 开头的字符串)
     * @param scene 申请方式 (好友申请消息里的 scene); 为了兼容旧接口，默认为扫码添加 (30)
     * @returns 1 为成功，其他失败
     */
    acceptFriend(v3: string, v4: string, scene?: number): number;
    /**
     * 接收转账
     * @param wxid 转账消息里的发送人 wxid
     * @param transferId 转账消息里的 transferId
     * @param transactionId 转账消息里的 transactionId
     * @returns 1 为成功，其他失败
     */
    receiveTransfer(wxid: string, transferId: string, transactionId: string): number;
    /**
     * 刷新朋友圈
     * @param id 开始 id，0 为最新页 (string based uint64)
     * @returns 1 为成功，其他失败
     */
    refreshPyq(id: string): number;
    /**
     * 下载附件（图片、视频、文件
     * @param id 消息中 id
     * @param thumb 消息中的 thumb
     * @param extra 消息中的 extra
     * @returns 0 为成功, 其他失败。
     */
    downloadAttach(id: string, thumb?: string, extra?: string): number;
    /**
     * 联系人信息
     *
     * @param wxid 联系人 wxid
     * @deprecated https://github.com/wechatferry/wechatferry/issues/16
     */
    getContactInfo(wxid: string): {
        wxid?: string;
        code?: string;
        remark?: string;
        name?: string;
        country?: string;
        province?: string;
        city?: string;
        gender?: number;
    };
    /**
     * 撤回消息
     * @param id 消息 id
     * @returns int: 1 为成功，其他失败
     */
    revokeMsg(id: string): number;
    /**
     * 获取登录二维码，已经登录则返回空字符串
     *
     * @deprecated Not supported
     */
    refreshQrcode(): string;
    /**
     * 解密图片
     *
     * @param src 加密的图片路径
     * @param dir 保存图片的目录
     * @returns 成功返回存储路径；空字符串为失败，原因见日志。
     */
    decryptImage(src: string, dir: string): string;
    /**
     * 获取 OCR 结果
     * @description 鸡肋，需要图片能自动下载；通过下载接口下载的图片无法识别。
     * @param extra 待识别的图片路径，消息里的 extra
     * @returns OCR 结果
     */
    execOCR(extra: string): string | undefined;
    /**
     * 添加群成员
     * @param roomId 群 id
     * @param wxidList 要添加的 wxid 列表
     * @returns 1 为成功，其他失败
     */
    addRoomMembers(roomId: string, wxidList: string[]): number;
    /**
     * 删除群成员
     * @param roomId 群 id
     * @param wxidList 要删除的 wxid 列表
     * @returns int32 1 为成功，其他失败
     */
    delRoomMembers(roomId: string, wxidList: string[]): number;
    /**
     * 邀请群成员
     * @param roomId 群 id
     * @param wxidList 要邀请的 wxid 列表
     * @returns int32 1 为成功，其他失败
     */
    inviteRoomMembers(roomId: string, wxidList: string[]): number;
}

/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 5.27.1
 * source: proto/room-data.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */

declare class RoomData$1 extends pb_1.Message {
    #private;
    constructor(data?: any[] | {
        members?: RoomData$1.RoomMember[];
        field_2?: number;
        field_3?: number;
        field_4?: number;
        room_capacity?: number;
        field_7?: string;
        field_8?: string;
    });
    get members(): RoomData$1.RoomMember[];
    set members(value: RoomData$1.RoomMember[]);
    get field_2(): number;
    set field_2(value: number);
    get field_3(): number;
    set field_3(value: number);
    get field_4(): number;
    set field_4(value: number);
    get room_capacity(): number;
    set room_capacity(value: number);
    get field_7(): string;
    set field_7(value: string);
    get field_8(): string;
    set field_8(value: string);
    static fromObject(data: {
        members?: ReturnType<typeof RoomData$1.RoomMember.prototype.toObject>[];
        field_2?: number;
        field_3?: number;
        field_4?: number;
        room_capacity?: number;
        field_7?: string;
        field_8?: string;
    }): RoomData$1;
    toObject(): {
        members?: ReturnType<typeof RoomData$1.RoomMember.prototype.toObject>[];
        field_2?: number;
        field_3?: number;
        field_4?: number;
        room_capacity?: number;
        field_7?: string;
        field_8?: string;
    };
    serialize(): Uint8Array;
    serialize(w: pb_1.BinaryWriter): void;
    static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RoomData$1;
    serializeBinary(): Uint8Array;
    static deserializeBinary(bytes: Uint8Array): RoomData$1;
}
declare namespace RoomData$1 {
    class RoomMember extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            wxid?: string;
            name?: string;
            state?: number;
        });
        get wxid(): string;
        set wxid(value: string);
        get name(): string;
        set name(value: string);
        get state(): number;
        set state(value: number);
        static fromObject(data: {
            wxid?: string;
            name?: string;
            state?: number;
        }): RoomMember;
        toObject(): {
            wxid?: string;
            name?: string;
            state?: number;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RoomMember;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): RoomMember;
    }
}

/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 5.27.1
 * source: proto/bytes-extra.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */

declare class BytesExtra extends pb_1.Message {
    #private;
    constructor(data?: any[] | {
        message1?: BytesExtra.SubMessage1;
        properties?: BytesExtra.Property[];
    });
    get message1(): BytesExtra.SubMessage1;
    set message1(value: BytesExtra.SubMessage1);
    get has_message1(): boolean;
    get properties(): BytesExtra.Property[];
    set properties(value: BytesExtra.Property[]);
    static fromObject(data: {
        message1?: ReturnType<typeof BytesExtra.SubMessage1.prototype.toObject>;
        properties?: ReturnType<typeof BytesExtra.Property.prototype.toObject>[];
    }): BytesExtra;
    toObject(): {
        message1?: ReturnType<typeof BytesExtra.SubMessage1.prototype.toObject>;
        properties?: ReturnType<typeof BytesExtra.Property.prototype.toObject>[];
    };
    serialize(): Uint8Array;
    serialize(w: pb_1.BinaryWriter): void;
    static deserialize(bytes: Uint8Array | pb_1.BinaryReader): BytesExtra;
    serializeBinary(): Uint8Array;
    static deserializeBinary(bytes: Uint8Array): BytesExtra;
}
declare namespace BytesExtra {
    enum PropertyKey {
        FIELD0 = 0,
        WXID = 1,
        SIGN = 2,
        THUMB = 3,
        EXTRA = 4,
        XML = 7
    }
    class Property extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            type?: BytesExtra.PropertyKey;
            value?: string;
        });
        get type(): BytesExtra.PropertyKey;
        set type(value: BytesExtra.PropertyKey);
        get value(): string;
        set value(value: string);
        static fromObject(data: {
            type?: BytesExtra.PropertyKey;
            value?: string;
        }): Property;
        toObject(): {
            type?: BytesExtra.PropertyKey;
            value?: string;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Property;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): Property;
    }
    class SubMessage1 extends pb_1.Message {
        #private;
        constructor(data?: any[] | {
            field1?: number;
            field2?: number;
        });
        get field1(): number;
        set field1(value: number);
        get field2(): number;
        set field2(value: number);
        static fromObject(data: {
            field1?: number;
            field2?: number;
        }): SubMessage1;
        toObject(): {
            field1?: number;
            field2?: number;
        };
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SubMessage1;
        serializeBinary(): Uint8Array;
        static deserializeBinary(bytes: Uint8Array): SubMessage1;
    }
}

declare function saveFileBox(file: FileBoxInterface, dir?: string): Promise<string>;
declare function parseDbField(type: number, content: Uint8Array): string | number | Buffer | undefined;
declare function uint8Array2str(arr: Uint8Array): string;

export { BytesExtra, type Contact, type DbTable$1 as DbTable, RoomData$1 as RoomData, type UserInfo$1 as UserInfo, WechatAppMessageType, WechatMessageType, Wechatferry, type WechatferryEventMap, type WechatferryOptions, WechatferrySDK, type WechatferrySDKEventMap, type WechatferrySDKImpl, type WechatferrySDKOptions, type WechatferrySDKUserOptions, type WechatferryUserOptions, type WxMsg$1 as WxMsg, parseDbField, resolvedWechatferryOptions, resolvedWechatferrySDKOptions, saveFileBox, uint8Array2str, wcf };
