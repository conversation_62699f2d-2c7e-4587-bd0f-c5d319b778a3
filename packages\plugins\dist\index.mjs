import pThrottle from 'p-throttle';
import { log, types } from 'wechaty';
import { matchers, talkers } from 'wechaty-plugin-contrib';
import { createStorage } from 'unstorage';
import dayjs from 'dayjs';

function resolvedSafeModePuppetOptions({ globalThrottleOptions, throttleOptions, ...options }) {
  return {
    globalThrottleOptions: {
      limit: 40,
      interval: 6e4,
      ...globalThrottleOptions
    },
    throttleOptions: {
      limit: 1,
      interval: 1e3,
      ...throttleOptions
    },
    minIntervalBetweenConversations: 3e3,
    maxIntervalBetweenConversations: 5e3,
    ...options
  };
}
function createSafeModePuppet(puppet, options = {}) {
  const { globalThrottleOptions, throttleOptions, minIntervalBetweenConversations, maxIntervalBetweenConversations } = resolvedSafeModePuppetOptions(options);
  let lastConversationId = null;
  const throttles = /* @__PURE__ */ new Map();
  const globalThrottle = pThrottle(globalThrottleOptions);
  function getThrottle(conversationId) {
    if (throttles.has(conversationId)) {
      return throttles.get(conversationId);
    }
    const throttle = pThrottle(throttleOptions);
    throttles.set(conversationId, throttle);
    return throttle;
  }
  async function applyDelayForDifferentConversations(conversationId) {
    if (lastConversationId !== conversationId) {
      const delay = Math.random() * (maxIntervalBetweenConversations - minIntervalBetweenConversations) + minIntervalBetweenConversations;
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
    lastConversationId = conversationId;
  }
  return new Proxy(puppet, {
    // @ts-expect-error untyped
    get(target, prop, receiver) {
      const raw = Reflect.get(target, prop, receiver);
      if (typeof prop === "string" && typeof target[prop] === "function" && prop.startsWith("messageSend")) {
        return async function(...args) {
          const conversationId = args[0];
          const throttle = getThrottle(conversationId);
          const sendMessageGloballyThrottled = globalThrottle(async () => {
            await applyDelayForDifferentConversations(conversationId);
            const sendMessageLocallyThrottled = throttle(() => target[prop](...args));
            return sendMessageLocallyThrottled();
          });
          return await sendMessageGloballyThrottled();
        };
      }
      return raw;
    }
  });
}

function defineWechatyPlugin(factory) {
  return factory;
}

const BLOCK_COMMAND_REGEX = /(MUTE|UNMUTE)\s?(\d+)?/g;
const wechatyPluginRoomMute = defineWechatyPlugin((config) => {
  const store = createStorage({
    driver: config.driver
  });
  const isManagedRoom = matchers.roomMatcher(config.room);
  const isAdmin = matchers.contactMatcher(config.admin);
  const talkMuteWarn = talkers.roomTalker(`\u5DF2\u88AB\u7981\u8A00 {{ muteDuration }} \u5206\u949F\uFF0C\u5C06\u4E8E {{ unmuteTime }} \u540E\u89E3\u5C01\uFF0C\u671F\u95F4\u53D1\u8A00\u5C06\u88AB\u79FB\u51FA\u7FA4\u804A\u3002`);
  const talkUnmuteWarn = talkers.roomTalker("\u5DF2\u88AB\u89E3\u5C01\uFF0C\u8BF7\u7406\u6027\u53D1\u8A00\uFF0C\u8C22\u8C22\uFF01");
  const talkKick = talkers.roomTalker("\u4F60\u5728\u88AB\u7981\u8A00\u671F\u95F4\u53D1\u8A00\u3002\u73B0\u5C06\u4F60\u79FB\u51FA\u7FA4\u804A\u2708\uFE0F\n\u6B22\u8FCE\u51B7\u9759\u540E\u518D\u6B21\u8FDB\u7FA4\u3002\n\u8BF7\u5927\u5BB6\u7406\u6027\u4EA4\u6D41\uFF0C\u8C22\u8C22\uFF01");
  return (bot) => {
    log.verbose("WechatyPlugin", "wechatyPluginRoomMute(%s)", bot);
    bot.on("message", async (message) => {
      if (message.self())
        return;
      const room = message.room();
      if (!room)
        return;
      if (!await isManagedRoom(room))
        return;
      const talker = message.talker();
      if (await isAdmin(talker)) {
        if (message.type() !== types.Message.Text)
          return;
        const mentionList = await message.mentionList();
        if (mentionList.length <= 0)
          return;
        const text = await message.mentionText();
        const match = BLOCK_COMMAND_REGEX.exec(text);
        if (!match)
          return;
        const [_, command, muteDuration = "5"] = match;
        const unmuteTime = Date.now() + Number(muteDuration) * 60 * 1e3;
        const view = {
          muteDuration,
          unmuteTime: dayjs(unmuteTime).format("HH:mm:ss")
        };
        if (command === "UNMUTE") {
          await store.removeItem(mentionList[0].id);
          await talkUnmuteWarn(room, mentionList, view);
        } else if (command === "MUTE") {
          for (const member of mentionList) {
            await store.setItem(member.id, {
              unmuteTime
            });
          }
          await talkMuteWarn(room, mentionList, view);
        }
      } else if (await store.hasItem(talker.id)) {
        const { unmuteTime } = await store.getItem(talker.id);
        if (unmuteTime > Date.now()) {
          await talkKick(room, talker);
          await bot.sleep(1e3);
          await room.remove(talker);
        } else {
          await store.removeItem(talker.id);
        }
      }
    });
  };
});

const wechatyPluginRoomKick = defineWechatyPlugin((config) => {
  const isManagedRoom = matchers.roomMatcher(config.room);
  const isAdmin = matchers.contactMatcher(config.admin);
  const isBlackListMessage = matchers.messageMatcher(config.blackListMessage ?? false);
  const talkAdminKickWarn = talkers.roomTalker("\u4F60\u8FDD\u53CD\u4E86\u7FA4\u89C4\uFF0C\u7BA1\u7406\u5458\u73B0\u5C06\u4F60\u79FB\u51FA\u7FA4\u804A \u2708\uFE0F");
  const talkKickWarn = talkers.roomTalker("\u4F60\u53D1\u8868\u4E86\u4E0D\u5F53\u8A00\u8BBA\uFF0C\u73B0\u5C06\u4F60\u79FB\u51FA\u7FA4\u804A\u2708\uFE0F \n\u6B22\u8FCE\u518D\u6B21\u8FDB\u6765\u3002");
  return (bot) => {
    log.verbose("WechatyPlugin", "wechatyPluginRoomKick(%s)", bot);
    bot.on("message", async (message) => {
      if (message.self())
        return;
      const room = message.room();
      if (!room)
        return;
      if (!await isManagedRoom(room))
        return;
      const talker = message.talker();
      if (await isAdmin(talker)) {
        if (message.type() !== types.Message.Text)
          return;
        const mentionList = await message.mentionList();
        if (mentionList.length <= 0)
          return;
        const text = await message.mentionText();
        if (text === "KICK" || text === "BAN") {
          await talkAdminKickWarn(room, mentionList);
          await bot.sleep(1e3);
          for (const member of mentionList) {
            if (!await isAdmin(member)) {
              await room.remove(member);
            }
          }
        }
      } else if (await isBlackListMessage(message)) {
        await talkKickWarn(room, [talker]);
        await bot.sleep(1e3);
        await room.remove(talker);
      }
    });
  };
});

export { createSafeModePuppet, defineWechatyPlugin, wechatyPluginRoomKick, wechatyPluginRoomMute };
