"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.rotate = exports.intersperse = exports.prependAll = exports.unzip = exports.zipWith = exports.sort = exports.lefts = exports.rights = exports.reverse = exports.modifyAt = exports.deleteAt = exports.updateAt = exports.insertAt = exports.copy = exports.findLastIndex = exports.findLastMap = exports.findFirstMap = exports.findIndex = exports.dropRight = exports.dropLeft = exports.takeRight = exports.takeLeft = exports.init = exports.tail = exports.last = exports.head = exports.lookup = exports.isOutOfBound = exports.size = exports.scanRight = exports.scanLeft = exports.chainWithIndex = exports.foldRight = exports.matchRight = exports.matchRightW = exports.foldLeft = exports.matchLeft = exports.matchLeftW = exports.match = exports.matchW = exports.fromEither = exports.fromOption = exports.replicate = exports.makeBy = exports.appendW = exports.append = exports.prependW = exports.prepend = exports.isNonEmpty = exports.isEmpty = void 0;
exports.getUnionMonoid = exports.getUnionSemigroup = exports.getOrd = exports.getEq = exports.getMonoid = exports.getSemigroup = exports.getShow = exports.URI = exports.unfold = exports.wilt = exports.wither = exports.traverseWithIndex = exports.sequence = exports.traverse = exports.reduceRightWithIndex = exports.reduceRight = exports.reduceWithIndex = exports.reduce = exports.foldMapWithIndex = exports.foldMap = exports.duplicate = exports.extend = exports.filterWithIndex = exports.alt = exports.altW = exports.partitionMapWithIndex = exports.partitionMap = exports.partitionWithIndex = exports.partition = exports.filter = exports.separate = exports.compact = exports.filterMap = exports.filterMapWithIndex = exports.mapWithIndex = exports.flatten = exports.flatMap = exports.ap = exports.map = exports.zero = exports.of = exports.concat = exports.concatW = exports.fromOptionK = exports.chunksOf = exports.splitAt = exports.chop = exports.sortBy = exports.uniq = exports.elem = void 0;
exports.cons = exports.empty = exports.range = exports.chain = exports.apS = exports.bind = exports.let = exports.bindTo = exports.Do = exports.intercalate = exports.exists = exports.some = exports.every = exports.unsafeDeleteAt = exports.unsafeUpdateAt = exports.unsafeInsertAt = exports.fromEitherK = exports.FromEither = exports.filterE = exports.ChainRecBreadthFirst = exports.chainRecBreadthFirst = exports.ChainRecDepthFirst = exports.chainRecDepthFirst = exports.Witherable = exports.TraversableWithIndex = exports.Traversable = exports.FoldableWithIndex = exports.Foldable = exports.FilterableWithIndex = exports.Filterable = exports.Compactable = exports.Extend = exports.Alternative = exports.guard = exports.Zero = exports.Alt = exports.Unfoldable = exports.Monad = exports.chainFirst = exports.Chain = exports.Applicative = exports.apSecond = exports.apFirst = exports.Apply = exports.FunctorWithIndex = exports.Pointed = exports.flap = exports.Functor = exports.getDifferenceMagma = exports.getIntersectionSemigroup = void 0;
exports.array = exports.prependToAll = exports.snoc = void 0;
exports.fromPredicate = fromPredicate;
exports.takeLeftWhile = takeLeftWhile;
exports.spanLeft = spanLeft;
exports.dropLeftWhile = dropLeftWhile;
exports.findFirst = findFirst;
exports.findLast = findLast;
exports.zip = zip;
exports.comprehension = comprehension;
exports.union = union;
exports.intersection = intersection;
exports.difference = difference;
var Apply_1 = require("./Apply");
var Chain_1 = require("./Chain");
var FromEither_1 = require("./FromEither");
var function_1 = require("./function");
var Functor_1 = require("./Functor");
var _ = __importStar(require("./internal"));
var NEA = __importStar(require("./NonEmptyArray"));
var RA = __importStar(require("./ReadonlyArray"));
var Separated_1 = require("./Separated");
var Witherable_1 = require("./Witherable");
var Zero_1 = require("./Zero");
// -------------------------------------------------------------------------------------
// refinements
// -------------------------------------------------------------------------------------
/**
 * Test whether an array is empty
 *
 * @example
 * import { isEmpty } from 'fp-ts/Array'
 *
 * assert.strictEqual(isEmpty([]), true)
 * assert.strictEqual(isEmpty(['a']), false)
 *
 * @category refinements
 * @since 2.0.0
 */
var isEmpty = function (as) { return as.length === 0; };
exports.isEmpty = isEmpty;
/**
 * Test whether an array is non empty narrowing down the type to `NonEmptyArray<A>`
 *
 * @example
 * import { isNonEmpty } from 'fp-ts/Array'
 *
 * assert.strictEqual(isNonEmpty([]), false)
 * assert.strictEqual(isNonEmpty(['a']), true)
 *
 * @category refinements
 * @since 2.0.0
 */
exports.isNonEmpty = NEA.isNonEmpty;
// -------------------------------------------------------------------------------------
// constructors
// -------------------------------------------------------------------------------------
/**
 * Prepend an element to the front of a `Array`, creating a new `NonEmptyArray`.
 *
 * @example
 * import { prepend } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(pipe([2, 3, 4], prepend(1)), [1, 2, 3, 4])
 *
 * @since 2.10.0
 */
exports.prepend = NEA.prepend;
/**
 * Less strict version of [`prepend`](#prepend).
 *
 * @example
 * import { prependW } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(pipe([2, 3, 4], prependW("a")), ["a", 2, 3, 4]);
 *
 * @since 2.11.0
 */
exports.prependW = NEA.prependW;
/**
 * Append an element to the end of a `Array`, creating a new `NonEmptyArray`.
 *
 * @example
 * import { append } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(pipe([1, 2, 3], append(4)), [1, 2, 3, 4])
 *
 * @since 2.10.0
 */
exports.append = NEA.append;
/**
 * Less strict version of [`append`](#append).
 *
 * @example
 * import { appendW } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(pipe([1, 2, 3], appendW("d")), [1, 2, 3, "d"]);
 *
 * @since 2.11.0
 */
exports.appendW = NEA.appendW;
/**
 * Return a `Array` of length `n` with element `i` initialized with `f(i)`.
 *
 * **Note**. `n` is normalized to a non negative integer.
 *
 * @example
 * import { makeBy } from 'fp-ts/Array'
 *
 * const double = (i: number): number => i * 2
 * assert.deepStrictEqual(makeBy(5, double), [0, 2, 4, 6, 8])
 * assert.deepStrictEqual(makeBy(-3, double), [])
 * assert.deepStrictEqual(makeBy(4.32164, double), [0, 2, 4, 6])
 *
 * @category constructors
 * @since 2.0.0
 */
var makeBy = function (n, f) { return (n <= 0 ? [] : NEA.makeBy(f)(n)); };
exports.makeBy = makeBy;
/**
 * Create a `Array` containing a value repeated the specified number of times.
 *
 * **Note**. `n` is normalized to a non negative integer.
 *
 * @example
 * import { replicate } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(replicate(3, 'a'), ['a', 'a', 'a'])
 * assert.deepStrictEqual(replicate(-3, 'a'), [])
 * assert.deepStrictEqual(replicate(2.985647, 'a'), ['a', 'a'])
 *
 * @category constructors
 * @since 2.0.0
 */
var replicate = function (n, a) { return (0, exports.makeBy)(n, function () { return a; }); };
exports.replicate = replicate;
function fromPredicate(predicate) {
    return function (a) { return (predicate(a) ? [a] : []); };
}
// -------------------------------------------------------------------------------------
// conversions
// -------------------------------------------------------------------------------------
/**
 * Create an array from an `Option`. The resulting array will contain the content of the
 * `Option` if it is `Some` and it will be empty if the `Option` is `None`.
 *
 * @example
 * import { fromOption } from 'fp-ts/Array'
 * import { option } from "fp-ts";
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(pipe(option.some("a"), fromOption),["a"])
 * assert.deepStrictEqual(pipe(option.none, fromOption),[])
 *
 * @category conversions
 * @since 2.11.0
 */
var fromOption = function (ma) { return (_.isNone(ma) ? [] : [ma.value]); };
exports.fromOption = fromOption;
/**
 * Create an array from an `Either`. The resulting array will contain the content of the
 * `Either` if it is `Right` and it will be empty if the `Either` is `Left`.
 *
 * @example
 * import { fromEither } from 'fp-ts/Array'
 * import { either } from "fp-ts";
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(pipe(either.right("r"), fromEither), ["r"]);
 * assert.deepStrictEqual(pipe(either.left("l"), fromEither), []);
 *
 * @category conversions
 * @since 2.11.0
 */
var fromEither = function (e) { return (_.isLeft(e) ? [] : [e.right]); };
exports.fromEither = fromEither;
/**
 * Less strict version of [`match`](#match).
 *
 * The `W` suffix (short for **W**idening) means that the handler return types will be merged.
 *
 * @example
 * import { matchW } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * const matcherW = matchW(
 *   () => "No elements",
 *   (as) => as.length
 * );
 * assert.deepStrictEqual(pipe([1, 2, 3, 4], matcherW), 4);
 * assert.deepStrictEqual(pipe([], matcherW), "No elements");
 *
 * @category pattern matching
 * @since 2.11.0
 */
var matchW = function (onEmpty, onNonEmpty) {
    return function (as) {
        return (0, exports.isNonEmpty)(as) ? onNonEmpty(as) : onEmpty();
    };
};
exports.matchW = matchW;
/**
 * Takes an array, if the array is empty it returns the result of `onEmpty`, otherwise
 * it passes the array to `onNonEmpty` and returns the result.
 *
 * @example
 * import { match } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * const matcher = match(
 *   () => "No elements",
 *   (as) => `Found ${as.length} element(s)`
 * );
 * assert.deepStrictEqual(pipe([1, 2, 3, 4], matcher), "Found 4 element(s)");
 * assert.deepStrictEqual(pipe([], matcher), "No elements");
 *
 * @category pattern matching
 * @since 2.11.0
 */
exports.match = exports.matchW;
/**
 * Less strict version of [`matchLeft`](#matchleft). It will work when `onEmpty` and
 * `onNonEmpty` have different return types.
 *
 * @example
 * import { matchLeftW } from 'fp-ts/Array'
 *
 * const f = matchLeftW(
 *   () => 0,
 *   (head: string, tail: string[]) => `Found "${head}" followed by ${tail.length} elements`
 * );
 * assert.strictEqual(f(["a", "b", "c"]), 'Found "a" followed by 2 elements');
 * assert.strictEqual(f([]), 0);
 *
 * @category pattern matching
 * @since 2.11.0
 */
var matchLeftW = function (onEmpty, onNonEmpty) {
    return function (as) {
        return (0, exports.isNonEmpty)(as) ? onNonEmpty(NEA.head(as), NEA.tail(as)) : onEmpty();
    };
};
exports.matchLeftW = matchLeftW;
/**
 * Takes an array, if the array is empty it returns the result of `onEmpty`, otherwise
 * it passes the array to `onNonEmpty` broken into its first element and remaining elements.
 *
 * @example
 * import { matchLeft } from 'fp-ts/Array'
 *
 * const len: <A>(as: Array<A>) => number = matchLeft(() => 0, (_, tail) => 1 + len(tail))
 * assert.strictEqual(len([1, 2, 3]), 3)
 *
 * @category pattern matching
 * @since 2.10.0
 */
exports.matchLeft = exports.matchLeftW;
/**
 * Alias of [`matchLeft`](#matchleft).
 *
 * @category pattern matching
 * @since 2.0.0
 */
exports.foldLeft = exports.matchLeft;
/**
 * Less strict version of [`matchRight`](#matchright). It will work when `onEmpty` and
 * `onNonEmpty` have different return types.
 *
 * @example
 * import { matchRightW } from 'fp-ts/Array'
 *
 * const f = matchRightW(
 *   () => 0,
 *   (head: string[], tail: string) => `Found ${head.length} elements folllowed by "${tail}"`
 * );
 * assert.strictEqual(f(["a", "b", "c"]), 'Found 2 elements folllowed by "c"');
 * assert.strictEqual(f([]), 0);
 *
 * @category pattern matching
 * @since 2.11.0
 */
var matchRightW = function (onEmpty, onNonEmpty) {
    return function (as) {
        return (0, exports.isNonEmpty)(as) ? onNonEmpty(NEA.init(as), NEA.last(as)) : onEmpty();
    };
};
exports.matchRightW = matchRightW;
/**
 * Takes an array, if the array is empty it returns the result of `onEmpty`, otherwise
 * it passes the array to `onNonEmpty` broken  into its initial elements and the last element.
 *
 * @example
 * import { matchRight } from 'fp-ts/Array'
 *
 * const len: <A>(as: Array<A>) => number = matchRight(
 *   () => 0,
 *   (head, _) => 1 + len(head)
 * );
 * assert.strictEqual(len([1, 2, 3]), 3);
 *
 * @category pattern matching
 * @since 2.10.0
 */
exports.matchRight = exports.matchRightW;
/**
 * Alias of [`matchRight`](#matchright).
 *
 * @category pattern matching
 * @since 2.0.0
 */
exports.foldRight = exports.matchRight;
// -------------------------------------------------------------------------------------
// combinators
// -------------------------------------------------------------------------------------
/**
 * Same as [`chain`](#chain), but passing also the index to the iterating function.
 *
 * @example
 * import { chainWithIndex, replicate } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * const f = (index: number, x: string) => replicate(2, `${x}${index}`);
 * assert.deepStrictEqual(pipe(["a", "b", "c"], chainWithIndex(f)), ["a0", "a0", "b1", "b1", "c2", "c2"]);
 *
 * @category sequencing
 * @since 2.7.0
 */
var chainWithIndex = function (f) {
    return function (as) {
        var out = [];
        for (var i = 0; i < as.length; i++) {
            var bs = f(i, as[i]);
            for (var j = 0; j < bs.length; j++) {
                out.push(bs[j]);
            }
        }
        return out;
    };
};
exports.chainWithIndex = chainWithIndex;
/**
 * Same as `reduce` but it carries over the intermediate steps
 *
 * @example
 * import { scanLeft } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(scanLeft(10, (b, a: number) => b - a)([1, 2, 3]), [10, 9, 7, 4])
 *
 * @since 2.0.0
 */
var scanLeft = function (b, f) {
    return function (as) {
        var len = as.length;
        var out = new Array(len + 1);
        out[0] = b;
        for (var i = 0; i < len; i++) {
            out[i + 1] = f(out[i], as[i]);
        }
        return out;
    };
};
exports.scanLeft = scanLeft;
/**
 * Fold an array from the right, keeping all intermediate results instead of only the final result
 *
 * @example
 * import { scanRight } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(scanRight(10, (a: number, b) => b - a)([1, 2, 3]), [4, 5, 7, 10])
 *
 * @since 2.0.0
 */
var scanRight = function (b, f) {
    return function (as) {
        var len = as.length;
        var out = new Array(len + 1);
        out[len] = b;
        for (var i = len - 1; i >= 0; i--) {
            out[i] = f(as[i], out[i + 1]);
        }
        return out;
    };
};
exports.scanRight = scanRight;
/**
 * Calculate the number of elements in a `Array`.
 *
 * @example
 * import { size } from 'fp-ts/Array'
 *
 * assert.strictEqual(size(["a","b","c"]),3)
 *
 * @since 2.10.0
 */
var size = function (as) { return as.length; };
exports.size = size;
/**
 * Test whether an array contains a particular index
 *
 * @example
 * import { isOutOfBound } from 'fp-ts/Array'
 *
 * assert.strictEqual(isOutOfBound(1,["a","b","c"]),false)
 * assert.strictEqual(isOutOfBound(-1,["a","b","c"]),true)
 * assert.strictEqual(isOutOfBound(3,["a","b","c"]),true)
 *
 * @since 2.0.0
 */
exports.isOutOfBound = NEA.isOutOfBound;
// TODO: remove non-curried overloading in v3
/**
 * This function provides a safe way to read a value at a particular index from an array.
 * It returns a `none` if the index is out of bounds, and a `some` of the element if the
 * index is valid.
 *
 * @example
 * import { lookup } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(pipe([1, 2, 3], lookup(1)), some(2))
 * assert.deepStrictEqual(pipe([1, 2, 3], lookup(3)), none)
 *
 * @since 2.0.0
 */
exports.lookup = RA.lookup;
/**
 * Get the first element in an array, or `None` if the array is empty
 *
 * @example
 * import { head } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * assert.deepStrictEqual(head([1, 2, 3]), some(1))
 * assert.deepStrictEqual(head([]), none)
 *
 * @since 2.0.0
 */
exports.head = RA.head;
/**
 * Get the last element in an array, or `None` if the array is empty
 *
 * @example
 * import { last } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * assert.deepStrictEqual(last([1, 2, 3]), some(3))
 * assert.deepStrictEqual(last([]), none)
 *
 * @since 2.0.0
 */
exports.last = RA.last;
/**
 * Get all but the first element of an array, creating a new array, or `None` if the array is empty
 *
 * @example
 * import { tail } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * assert.deepStrictEqual(tail([1, 2, 3]), some([2, 3]))
 * assert.deepStrictEqual(tail([]), none)
 *
 * @since 2.0.0
 */
var tail = function (as) { return ((0, exports.isNonEmpty)(as) ? _.some(NEA.tail(as)) : _.none); };
exports.tail = tail;
/**
 * Get all but the last element of an array, creating a new array, or `None` if the array is empty
 *
 * @example
 * import { init } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * assert.deepStrictEqual(init([1, 2, 3]), some([1, 2]))
 * assert.deepStrictEqual(init([]), none)
 *
 * @since 2.0.0
 */
var init = function (as) { return ((0, exports.isNonEmpty)(as) ? _.some(NEA.init(as)) : _.none); };
exports.init = init;
/**
 * Keep only a max number of elements from the start of an `Array`, creating a new `Array`.
 *
 * **Note**. `n` is normalized to a non negative integer.
 *
 * @example
 * import { takeLeft } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(takeLeft(2)([1, 2, 3, 4, 5]), [1, 2]);
 * assert.deepStrictEqual(takeLeft(7)([1, 2, 3, 4, 5]), [1, 2, 3, 4, 5]);
 * assert.deepStrictEqual(takeLeft(0)([1, 2, 3, 4, 5]), []);
 * assert.deepStrictEqual(takeLeft(-1)([1, 2, 3, 4, 5]), [1, 2, 3, 4, 5]);
 *
 * @since 2.0.0
 */
var takeLeft = function (n) {
    return function (as) {
        return (0, exports.isOutOfBound)(n, as) ? (0, exports.copy)(as) : as.slice(0, n);
    };
};
exports.takeLeft = takeLeft;
/**
 * Keep only a max number of elements from the end of an `Array`, creating a new `Array`.
 *
 * **Note**. `n` is normalized to a non negative integer.
 *
 * @example
 * import { takeRight } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(takeRight(2)([1, 2, 3, 4, 5]), [4, 5]);
 * assert.deepStrictEqual(takeRight(7)([1, 2, 3, 4, 5]), [1, 2, 3, 4, 5]);
 * assert.deepStrictEqual(takeRight(0)([1, 2, 3, 4, 5]), []);
 * assert.deepStrictEqual(takeRight(-1)([1, 2, 3, 4, 5]), [1, 2, 3, 4, 5]);
 *
 * @since 2.0.0
 */
var takeRight = function (n) {
    return function (as) {
        return (0, exports.isOutOfBound)(n, as) ? (0, exports.copy)(as) : n === 0 ? [] : as.slice(-n);
    };
};
exports.takeRight = takeRight;
function takeLeftWhile(predicate) {
    return function (as) {
        var out = [];
        for (var _i = 0, as_1 = as; _i < as_1.length; _i++) {
            var a = as_1[_i];
            if (!predicate(a)) {
                break;
            }
            out.push(a);
        }
        return out;
    };
}
var spanLeftIndex = function (as, predicate) {
    var l = as.length;
    var i = 0;
    for (; i < l; i++) {
        if (!predicate(as[i])) {
            break;
        }
    }
    return i;
};
function spanLeft(predicate) {
    return function (as) {
        var _a = (0, exports.splitAt)(spanLeftIndex(as, predicate))(as), init = _a[0], rest = _a[1];
        return { init: init, rest: rest };
    };
}
/**
 * Creates a new `Array` which is a copy of the input dropping a max number of elements from the start.
 *
 * **Note**. `n` is normalized to a non negative integer.
 *
 * @example
 * import { dropLeft } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(dropLeft(2)([1, 2, 3]), [3]);
 * assert.deepStrictEqual(dropLeft(5)([1, 2, 3]), []);
 * assert.deepStrictEqual(dropLeft(0)([1, 2, 3]), [1, 2, 3]);
 * assert.deepStrictEqual(dropLeft(-2)([1, 2, 3]), [1, 2, 3]);
 *
 * @since 2.0.0
 */
var dropLeft = function (n) {
    return function (as) {
        return n <= 0 || (0, exports.isEmpty)(as) ? (0, exports.copy)(as) : n >= as.length ? [] : as.slice(n, as.length);
    };
};
exports.dropLeft = dropLeft;
/**
 * Creates a new `Array` which is a copy of the input dropping a max number of elements from the end.
 *
 * **Note**. `n` is normalized to a non negative integer.
 *
 * @example
 * import { dropRight } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(dropRight(2)([1, 2, 3]), [1]);
 * assert.deepStrictEqual(dropRight(5)([1, 2, 3]), []);
 * assert.deepStrictEqual(dropRight(0)([1, 2, 3]), [1, 2, 3]);
 * assert.deepStrictEqual(dropRight(-2)([1, 2, 3]), [1, 2, 3]);
 *
 * @since 2.0.0
 */
var dropRight = function (n) {
    return function (as) {
        return n <= 0 || (0, exports.isEmpty)(as) ? (0, exports.copy)(as) : n >= as.length ? [] : as.slice(0, as.length - n);
    };
};
exports.dropRight = dropRight;
function dropLeftWhile(predicate) {
    return function (as) { return as.slice(spanLeftIndex(as, predicate)); };
}
/**
 * `findIndex` returns an `Option` containing the first index for which a predicate holds.
 * It returns `None` if no element satisfies the predicate.
 * Similar to [`findFirst`](#findFirst) but returning the index instead of the element.
 *
 * @example
 * import { findIndex } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * assert.deepStrictEqual(findIndex((n: number) => n === 2)([1, 2, 3]), some(1))
 * assert.deepStrictEqual(findIndex((n: number) => n === 2)([]), none)
 *
 * @since 2.0.0
 */
exports.findIndex = RA.findIndex;
function findFirst(predicate) {
    return RA.findFirst(predicate);
}
/**
 * Given a selector function which takes an element and returns an option,
 * this function applies the selector to each element of the array and
 * returns the first `Some` result. Otherwise it returns `None`.
 *
 * @example
 * import { findFirstMap } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * interface Person {
 *   readonly name: string;
 *   readonly age: number;
 * }
 *
 * const persons: Array<Person> = [
 *   { name: "John", age: 16 },
 *   { name: "Mary", age: 45 },
 *   { name: "Joey", age: 28 },
 * ];
 *
 * const nameOfPersonAbove18 = (p: Person) => (p.age <= 18 ? none : some(p.name));
 * const nameOfPersonAbove70 = (p: Person) => (p.age <= 70 ? none : some(p.name));
 * assert.deepStrictEqual(findFirstMap(nameOfPersonAbove18)(persons), some("Mary"));
 * assert.deepStrictEqual(findFirstMap(nameOfPersonAbove70)(persons), none);
 *
 * @since 2.0.0
 */
exports.findFirstMap = RA.findFirstMap;
function findLast(predicate) {
    return RA.findLast(predicate);
}
/**
 * Given a selector function which takes an element and returns an option,
 * this function applies the selector to each element of the array starting from the
 * end and returns the last `Some` result. Otherwise it returns `None`.
 *
 * @example
 * import { findLastMap } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * interface Person {
 *   readonly name: string;
 *   readonly age: number;
 * }
 *
 * const persons: Array<Person> = [
 *   { name: "John", age: 16 },
 *   { name: "Mary", age: 45 },
 *   { name: "Joey", age: 28 },
 * ];
 *
 * const nameOfPersonAbove18 = (p: Person) => (p.age <= 18 ? none : some(p.name));
 * const nameOfPersonAbove70 = (p: Person) => (p.age <= 70 ? none : some(p.name));
 * assert.deepStrictEqual(findLastMap(nameOfPersonAbove18)(persons), some("Joey"));
 * assert.deepStrictEqual(findLastMap(nameOfPersonAbove70)(persons), none);
 *
 * @since 2.0.0
 */
exports.findLastMap = RA.findLastMap;
/**
 * Returns the index of the last element of the list which matches the predicate.
 * It returns an `Option` containing the index or `None` if not found.
 *
 * @example
 * import { findLastIndex } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * interface X {
 *   readonly a: number
 *   readonly b: number
 * }
 * const xs: Array<X> = [{ a: 1, b: 0 }, { a: 1, b: 1 }]
 * assert.deepStrictEqual(findLastIndex((x: { readonly a: number }) => x.a === 1)(xs), some(1))
 * assert.deepStrictEqual(findLastIndex((x: { readonly a: number }) => x.a === 4)(xs), none)
 *
 * @since 2.0.0
 */
exports.findLastIndex = RA.findLastIndex;
/**
 * This function takes an array and makes a new array containing the same elements.
 *
 * @since 2.0.0
 */
var copy = function (as) { return as.slice(); };
exports.copy = copy;
/**
 * Insert an element at the specified index, creating a new array,
 * or returning `None` if the index is out of bounds.
 *
 * @example
 * import { insertAt } from 'fp-ts/Array'
 * import { some } from 'fp-ts/Option'
 *
 * assert.deepStrictEqual(insertAt(2, 5)([1, 2, 3, 4]), some([1, 2, 5, 3, 4]))
 *
 * @since 2.0.0
 */
var insertAt = function (i, a) {
    return function (as) {
        return i < 0 || i > as.length ? _.none : _.some((0, exports.unsafeInsertAt)(i, a, as));
    };
};
exports.insertAt = insertAt;
/**
 * Change the element at the specified index, creating a new array,
 * or returning `None` if the index is out of bounds.
 *
 * @example
 * import { updateAt } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * assert.deepStrictEqual(updateAt(1, 1)([1, 2, 3]), some([1, 1, 3]))
 * assert.deepStrictEqual(updateAt(1, 1)([]), none)
 *
 * @since 2.0.0
 */
var updateAt = function (i, a) { return (0, exports.modifyAt)(i, function () { return a; }); };
exports.updateAt = updateAt;
/**
 * Delete the element at the specified index, creating a new array, or returning `None` if the index is out of bounds.
 *
 * @example
 * import { deleteAt } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * assert.deepStrictEqual(deleteAt(0)([1, 2, 3]), some([2, 3]))
 * assert.deepStrictEqual(deleteAt(1)([]), none)
 *
 * @since 2.0.0
 */
var deleteAt = function (i) {
    return function (as) {
        return (0, exports.isOutOfBound)(i, as) ? _.none : _.some((0, exports.unsafeDeleteAt)(i, as));
    };
};
exports.deleteAt = deleteAt;
/**
 * Apply a function to the element at the specified index, creating a new array, or returning `None` if the index is out
 * of bounds.
 *
 * @example
 * import { modifyAt } from 'fp-ts/Array'
 * import { some, none } from 'fp-ts/Option'
 *
 * const double = (x: number): number => x * 2
 * assert.deepStrictEqual(modifyAt(1, double)([1, 2, 3]), some([1, 4, 3]))
 * assert.deepStrictEqual(modifyAt(1, double)([]), none)
 *
 * @since 2.0.0
 */
var modifyAt = function (i, f) {
    return function (as) {
        return (0, exports.isOutOfBound)(i, as) ? _.none : _.some((0, exports.unsafeUpdateAt)(i, f(as[i]), as));
    };
};
exports.modifyAt = modifyAt;
/**
 * Reverse an array, creating a new array
 *
 * @example
 * import { reverse } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(reverse([1, 2, 3]), [3, 2, 1])
 *
 * @since 2.0.0
 */
var reverse = function (as) { return ((0, exports.isEmpty)(as) ? [] : as.slice().reverse()); };
exports.reverse = reverse;
/**
 * Takes an `Array` of `Either` and produces a new `Array` containing
 * the values of all the `Right` elements in the same order.
 *
 * @example
 * import { rights } from 'fp-ts/Array'
 * import { right, left } from 'fp-ts/Either'
 *
 * assert.deepStrictEqual(rights([right(1), left('foo'), right(2)]), [1, 2])
 *
 * @since 2.0.0
 */
var rights = function (as) {
    var r = [];
    for (var i = 0; i < as.length; i++) {
        var a = as[i];
        if (a._tag === 'Right') {
            r.push(a.right);
        }
    }
    return r;
};
exports.rights = rights;
/**
 * Takes an `Array` of `Either` and produces a new `Array` containing
 * the values of all the `Left` elements in the same order.
 *
 * @example
 * import { lefts } from 'fp-ts/Array'
 * import { left, right } from 'fp-ts/Either'
 *
 * assert.deepStrictEqual(lefts([right(1), left('foo'), right(2)]), ['foo'])
 *
 * @since 2.0.0
 */
var lefts = function (as) {
    var r = [];
    for (var i = 0; i < as.length; i++) {
        var a = as[i];
        if (a._tag === 'Left') {
            r.push(a.left);
        }
    }
    return r;
};
exports.lefts = lefts;
/**
 * Sort the elements of an array in increasing order, creating a new array
 *
 * @example
 * import { sort } from 'fp-ts/Array'
 * import * as N from 'fp-ts/number'
 *
 * assert.deepStrictEqual(sort(N.Ord)([3, 2, 1]), [1, 2, 3])
 *
 * @since 2.0.0
 */
var sort = function (O) {
    return function (as) {
        return as.length <= 1 ? (0, exports.copy)(as) : as.slice().sort(O.compare);
    };
};
exports.sort = sort;
/**
 * Apply a function to pairs of elements at the same index in two arrays, collecting the results in a new array. If one
 * input array is short, excess elements of the longer array are discarded.
 *
 * @example
 * import { zipWith } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(zipWith([1, 2, 3], ['a', 'b', 'c', 'd'], (n, s) => s + n), ['a1', 'b2', 'c3'])
 *
 * @since 2.0.0
 */
var zipWith = function (fa, fb, f) {
    var fc = [];
    var len = Math.min(fa.length, fb.length);
    for (var i = 0; i < len; i++) {
        fc[i] = f(fa[i], fb[i]);
    }
    return fc;
};
exports.zipWith = zipWith;
function zip(as, bs) {
    if (bs === undefined) {
        return function (bs) { return zip(bs, as); };
    }
    return (0, exports.zipWith)(as, bs, function (a, b) { return [a, b]; });
}
/**
 * The function is reverse of `zip`. Takes an array of pairs and return two corresponding arrays
 *
 * @example
 * import { unzip } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(unzip([[1, 'a'], [2, 'b'], [3, 'c']]), [[1, 2, 3], ['a', 'b', 'c']])
 *
 * @since 2.0.0
 */
var unzip = function (as) {
    var fa = [];
    var fb = [];
    for (var i = 0; i < as.length; i++) {
        fa[i] = as[i][0];
        fb[i] = as[i][1];
    }
    return [fa, fb];
};
exports.unzip = unzip;
/**
 * Creates a new `Array`, prepending an element to every member of the input `Array`.
 *
 * @example
 * import { prependAll } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(prependAll(9)([1, 2, 3, 4]), [9, 1, 9, 2, 9, 3, 9, 4])
 *
 * @since 2.10.0
 */
var prependAll = function (middle) {
    var f = NEA.prependAll(middle);
    return function (as) { return ((0, exports.isNonEmpty)(as) ? f(as) : []); };
};
exports.prependAll = prependAll;
/**
 * Creates a new `Array` placing an element in between members of the input `Array`.
 *
 * @example
 * import { intersperse } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(intersperse(9)([1, 2, 3, 4]), [1, 9, 2, 9, 3, 9, 4])
 *
 * @since 2.9.0
 */
var intersperse = function (middle) {
    var f = NEA.intersperse(middle);
    return function (as) { return ((0, exports.isNonEmpty)(as) ? f(as) : (0, exports.copy)(as)); };
};
exports.intersperse = intersperse;
/**
 * Creates a new `Array` rotating the input `Array` by `n` steps.
 *
 * @example
 * import { rotate } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(rotate(2)([1, 2, 3, 4, 5]), [4, 5, 1, 2, 3])
 *
 * @since 2.0.0
 */
var rotate = function (n) {
    var f = NEA.rotate(n);
    return function (as) { return ((0, exports.isNonEmpty)(as) ? f(as) : (0, exports.copy)(as)); };
};
exports.rotate = rotate;
// TODO: remove non-curried overloading in v3
/**
 * Test if a value is a member of an `Array`. Takes a `Eq<A>` as a single
 * argument which returns the function to use to search for a value of type `A` in
 * an `Array<A>`.
 *
 * @example
 * import { elem } from 'fp-ts/Array'
 * import * as N from 'fp-ts/number'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.strictEqual(pipe([1, 2, 3], elem(N.Eq)(2)), true)
 * assert.strictEqual(pipe([1, 2, 3], elem(N.Eq)(0)), false)
 *
 * @since 2.0.0
 */
exports.elem = RA.elem;
/**
 * Creates a new `Array` removing duplicate elements, keeping the first occurrence of an element,
 * based on a `Eq<A>`.
 *
 * @example
 * import { uniq } from 'fp-ts/Array'
 * import * as N from 'fp-ts/number'
 *
 * assert.deepStrictEqual(uniq(N.Eq)([1, 2, 1]), [1, 2])
 *
 * @since 2.0.0
 */
var uniq = function (E) {
    var f = NEA.uniq(E);
    return function (as) { return ((0, exports.isNonEmpty)(as) ? f(as) : (0, exports.copy)(as)); };
};
exports.uniq = uniq;
/**
 * Sort the elements of an array in increasing order, where elements are compared using first `ords[0]`, then `ords[1]`,
 * etc...
 *
 * @example
 * import { sortBy } from 'fp-ts/Array'
 * import { contramap } from 'fp-ts/Ord'
 * import * as S from 'fp-ts/string'
 * import * as N from 'fp-ts/number'
 * import { pipe } from 'fp-ts/function'
 *
 * interface Person {
 *   readonly name: string
 *   readonly age: number
 * }
 * const byName = pipe(S.Ord, contramap((p: Person) => p.name))
 * const byAge = pipe(N.Ord, contramap((p: Person) => p.age))
 *
 * const sortByNameByAge = sortBy([byName, byAge])
 *
 * const persons = [{ name: 'a', age: 1 }, { name: 'b', age: 3 }, { name: 'c', age: 2 }, { name: 'b', age: 2 }]
 * assert.deepStrictEqual(sortByNameByAge(persons), [
 *   { name: 'a', age: 1 },
 *   { name: 'b', age: 2 },
 *   { name: 'b', age: 3 },
 *   { name: 'c', age: 2 }
 * ])
 *
 * @since 2.0.0
 */
var sortBy = function (ords) {
    var f = NEA.sortBy(ords);
    return function (as) { return ((0, exports.isNonEmpty)(as) ? f(as) : (0, exports.copy)(as)); };
};
exports.sortBy = sortBy;
/**
 * A useful recursion pattern for processing an array to produce a new array, often used for "chopping" up the input
 * array. Typically chop is called with some function that will consume an initial prefix of the array and produce a
 * value and the rest of the array.
 *
 * @example
 * import { Eq } from 'fp-ts/Eq'
 * import * as A from 'fp-ts/Array'
 * import * as N from 'fp-ts/number'
 * import { pipe } from 'fp-ts/function'
 *
 * const group = <A>(S: Eq<A>): ((as: Array<A>) => Array<Array<A>>) => {
 *   return A.chop(as => {
 *     const { init, rest } = pipe(as, A.spanLeft((a: A) => S.equals(a, as[0])))
 *     return [init, rest]
 *   })
 * }
 * assert.deepStrictEqual(group(N.Eq)([1, 1, 2, 3, 3, 4]), [[1, 1], [2], [3, 3], [4]])
 *
 * @since 2.0.0
 */
var chop = function (f) {
    var g = NEA.chop(f);
    return function (as) { return ((0, exports.isNonEmpty)(as) ? g(as) : []); };
};
exports.chop = chop;
/**
 * Splits an `Array` into two pieces, the first piece has max `n` elements.
 *
 * @example
 * import { splitAt } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(splitAt(2)([1, 2, 3, 4, 5]), [[1, 2], [3, 4, 5]])
 *
 * @since 2.0.0
 */
var splitAt = function (n) {
    return function (as) {
        return n >= 1 && (0, exports.isNonEmpty)(as) ? NEA.splitAt(n)(as) : (0, exports.isEmpty)(as) ? [(0, exports.copy)(as), []] : [[], (0, exports.copy)(as)];
    };
};
exports.splitAt = splitAt;
/**
 * Splits an array into length-`n` pieces. The last piece will be shorter if `n` does not evenly divide the length of
 * the array. Note that `chunksOf(n)([])` is `[]`, not `[[]]`. This is intentional, and is consistent with a recursive
 * definition of `chunksOf`; it satisfies the property that
 *
 * ```ts
 * chunksOf(n)(xs).concat(chunksOf(n)(ys)) == chunksOf(n)(xs.concat(ys)))
 * ```
 *
 * whenever `n` evenly divides the length of `xs`.
 *
 * @example
 * import { chunksOf } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(chunksOf(2)([1, 2, 3, 4, 5]), [[1, 2], [3, 4], [5]])
 *
 * @since 2.0.0
 */
var chunksOf = function (n) {
    var f = NEA.chunksOf(n);
    return function (as) { return ((0, exports.isNonEmpty)(as) ? f(as) : []); };
};
exports.chunksOf = chunksOf;
/**
 * @category lifting
 * @since 2.11.0
 */
var fromOptionK = function (f) {
    return function () {
        var a = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            a[_i] = arguments[_i];
        }
        return (0, exports.fromOption)(f.apply(void 0, a));
    };
};
exports.fromOptionK = fromOptionK;
function comprehension(input, f, g) {
    if (g === void 0) { g = function () { return true; }; }
    var go = function (scope, input) {
        return (0, exports.isNonEmpty)(input)
            ? (0, exports.flatMap)(NEA.head(input), function (a) { return go((0, function_1.pipe)(scope, (0, exports.append)(a)), NEA.tail(input)); })
            : g.apply(void 0, scope) ? [f.apply(void 0, scope)]
                : [];
    };
    return go([], input);
}
/**
 * @since 2.11.0
 */
var concatW = function (second) {
    return function (first) {
        return (0, exports.isEmpty)(first) ? (0, exports.copy)(second) : (0, exports.isEmpty)(second) ? (0, exports.copy)(first) : first.concat(second);
    };
};
exports.concatW = concatW;
/**
 * @since 2.11.0
 */
exports.concat = exports.concatW;
function union(E) {
    var unionE = NEA.union(E);
    return function (first, second) {
        if (second === undefined) {
            var unionE_1 = union(E);
            return function (second) { return unionE_1(second, first); };
        }
        return (0, exports.isNonEmpty)(first) && (0, exports.isNonEmpty)(second)
            ? unionE(second)(first)
            : (0, exports.isNonEmpty)(first)
                ? (0, exports.copy)(first)
                : (0, exports.copy)(second);
    };
}
function intersection(E) {
    var elemE = (0, exports.elem)(E);
    return function (xs, ys) {
        if (ys === undefined) {
            var intersectionE_1 = intersection(E);
            return function (ys) { return intersectionE_1(ys, xs); };
        }
        return xs.filter(function (a) { return elemE(a, ys); });
    };
}
function difference(E) {
    var elemE = (0, exports.elem)(E);
    return function (xs, ys) {
        if (ys === undefined) {
            var differenceE_1 = difference(E);
            return function (ys) { return differenceE_1(ys, xs); };
        }
        return xs.filter(function (a) { return !elemE(a, ys); });
    };
}
var _map = function (fa, f) { return (0, function_1.pipe)(fa, (0, exports.map)(f)); };
/* istanbul ignore next */
var _mapWithIndex = function (fa, f) { return (0, function_1.pipe)(fa, (0, exports.mapWithIndex)(f)); };
var _ap = function (fab, fa) { return (0, function_1.pipe)(fab, (0, exports.ap)(fa)); };
/* istanbul ignore next */
var _filter = function (fa, predicate) { return (0, function_1.pipe)(fa, (0, exports.filter)(predicate)); };
/* istanbul ignore next */
var _filterMap = function (fa, f) { return (0, function_1.pipe)(fa, (0, exports.filterMap)(f)); };
/* istanbul ignore next */
var _partition = function (fa, predicate) {
    return (0, function_1.pipe)(fa, (0, exports.partition)(predicate));
};
/* istanbul ignore next */
var _partitionMap = function (fa, f) { return (0, function_1.pipe)(fa, (0, exports.partitionMap)(f)); };
/* istanbul ignore next */
var _partitionWithIndex = function (fa, predicateWithIndex) { return (0, function_1.pipe)(fa, (0, exports.partitionWithIndex)(predicateWithIndex)); };
/* istanbul ignore next */
var _partitionMapWithIndex = function (fa, f) { return (0, function_1.pipe)(fa, (0, exports.partitionMapWithIndex)(f)); };
/* istanbul ignore next */
var _alt = function (fa, that) { return (0, function_1.pipe)(fa, (0, exports.alt)(that)); };
var _reduce = function (fa, b, f) { return (0, function_1.pipe)(fa, (0, exports.reduce)(b, f)); };
/* istanbul ignore next */
var _foldMap = function (M) {
    var foldMapM = (0, exports.foldMap)(M);
    return function (fa, f) { return (0, function_1.pipe)(fa, foldMapM(f)); };
};
/* istanbul ignore next */
var _reduceRight = function (fa, b, f) { return (0, function_1.pipe)(fa, (0, exports.reduceRight)(b, f)); };
/* istanbul ignore next */
var _reduceWithIndex = function (fa, b, f) {
    return (0, function_1.pipe)(fa, (0, exports.reduceWithIndex)(b, f));
};
/* istanbul ignore next */
var _foldMapWithIndex = function (M) {
    var foldMapWithIndexM = (0, exports.foldMapWithIndex)(M);
    return function (fa, f) { return (0, function_1.pipe)(fa, foldMapWithIndexM(f)); };
};
/* istanbul ignore next */
var _reduceRightWithIndex = function (fa, b, f) {
    return (0, function_1.pipe)(fa, (0, exports.reduceRightWithIndex)(b, f));
};
/* istanbul ignore next */
var _filterMapWithIndex = function (fa, f) { return (0, function_1.pipe)(fa, (0, exports.filterMapWithIndex)(f)); };
/* istanbul ignore next */
var _filterWithIndex = function (fa, predicateWithIndex) { return (0, function_1.pipe)(fa, (0, exports.filterWithIndex)(predicateWithIndex)); };
/* istanbul ignore next */
var _extend = function (fa, f) { return (0, function_1.pipe)(fa, (0, exports.extend)(f)); };
/* istanbul ignore next */
var _traverse = function (F) {
    var traverseF = (0, exports.traverse)(F);
    return function (ta, f) { return (0, function_1.pipe)(ta, traverseF(f)); };
};
/* istanbul ignore next */
var _traverseWithIndex = function (F) {
    var traverseWithIndexF = (0, exports.traverseWithIndex)(F);
    return function (ta, f) { return (0, function_1.pipe)(ta, traverseWithIndexF(f)); };
};
var _chainRecDepthFirst = RA._chainRecDepthFirst;
var _chainRecBreadthFirst = RA._chainRecBreadthFirst;
/**
 * Given an element of the base type, `of` builds an `Array` containing just that
 * element of the base type (this is useful for building a `Monad`).
 *
 * @example
 * import { of } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(of("a"), ["a"]);
 *
 * @category constructors
 * @since 2.0.0
 */
exports.of = NEA.of;
/**
 * Makes an empty `Array`, useful for building a [`Monoid`](#Monoid)
 *
 * @since 2.7.0
 */
var zero = function () { return []; };
exports.zero = zero;
/**
 * `map` can be used to turn functions `(a: A) => B` into functions `(fa: Array<A>) => Array<B>`.
 * In practice it applies the base function to each element of the array and collects the
 * results in a new array.
 *
 * @example
 * import { map } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * const f = (n: number) => n * 2;
 * assert.deepStrictEqual(pipe([1, 2, 3], map(f)), [2, 4, 6]);
 *
 * @category mapping
 * @since 2.0.0
 */
var map = function (f) { return function (fa) { return fa.map(function (a) { return f(a); }); }; };
exports.map = map;
/**
 * @example
 * import { ap, map, of } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * // a curried function with 3 input parameteres
 * const f = (s1: string) => (n: number) => (s2: string) => s1 + n + s2;
 *
 * // let's use `ap` to iterate `f` over an array for each input parameter
 * assert.deepStrictEqual(pipe(["a", "b"], map(f), ap([1, 2]), ap(["😀", "😫", "😎"])), [
 *   "a1😀", "a1😫", "a1😎",
 *   "a2😀", "a2😫", "a2😎",
 *   "b1😀", "b1😫", "b1😎",
 *   "b2😀", "b2😫", "b2😎",
 * ]);
 *
 * // given Array implements the Applicative interface with the `of` method,
 * // we can write exactly the same thing in a more symmetric way
 * // using `of` on `f` and `ap` on each array in input
 * assert.deepStrictEqual(
 *   pipe(of(f), ap(["a", "b"]), ap([1, 2]), ap(["😀", "😫", "😎"])),
 *   pipe(["a", "b"], map(f), ap([1, 2]), ap(["😀", "😫", "😎"]))
 * );
 *
 * @since 2.0.0
 */
var ap = function (fa) {
    return (0, exports.flatMap)(function (f) { return (0, function_1.pipe)(fa, (0, exports.map)(f)); });
};
exports.ap = ap;
/**
 * Composes computations in sequence, using the return value of one computation to
 * determine the next computation.
 *
 * In other words it takes a function `f` that produces an array from a single element of
 * the base type `A` and returns a new function which applies `f` to each element of the
 * input array (like [`map`](#map)) and, instead of returning an array of arrays, concatenates the
 * results into a single array (like [`flatten`](#flatten)).
 *
 * @example
 * import { flatMap, map, replicate } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * const f = (n: number) => replicate(n, `${n}`);
 * assert.deepStrictEqual(pipe([1, 2, 3], map(f)), [["1"], ["2", "2"], ["3", "3", "3"]]);
 * assert.deepStrictEqual(pipe([1, 2, 3], flatMap(f)), ["1", "2", "2", "3", "3", "3"]);
 *
 * @category sequencing
 * @since 2.14.0
 */
exports.flatMap = (0, function_1.dual)(2, function (ma, f) {
    return (0, function_1.pipe)(ma, (0, exports.chainWithIndex)(function (i, a) { return f(a, i); }));
});
/**
 * Takes an array of arrays of `A` and flattens them into an array of `A`
 * by concatenating the elements of each array in order.
 *
 * @example
 * import { flatten } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(flatten([["a"], ["b", "c"], ["d", "e", "f"]]), ["a", "b", "c", "d", "e", "f"]);
 *
 * @category sequencing
 * @since 2.5.0
 */
exports.flatten = (0, exports.flatMap)(function_1.identity);
/**
 * Same as [`map`](#map), but the iterating function takes both the index and the value
 * of the element.
 *
 * @example
 * import { mapWithIndex } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * const f = (i: number, s: string) => `${s} - ${i}`;
 * assert.deepStrictEqual(pipe(["a", "b", "c"], mapWithIndex(f)), ["a - 0", "b - 1", "c - 2"]);
 *
 * @category mapping
 * @since 2.0.0
 */
var mapWithIndex = function (f) { return function (fa) {
    return fa.map(function (a, i) { return f(i, a); });
}; };
exports.mapWithIndex = mapWithIndex;
/**
 * Maps an array with an iterating function that takes the index and the value of
 * each element and returns an `Option`. It keeps only the `Some` values discarding
 * the `None`s.
 *
 * Same as [`filterMap`](#filterMap), but with an iterating function which takes also
 * the index as input.
 *
 * @example
 * import { filterMapWithIndex } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 * import { option } from "fp-ts";
 *
 * const f = (i: number, s: string) => (i % 2 === 1 ? option.some(s.toUpperCase()) : option.none);
 * assert.deepStrictEqual(pipe(["a", "no", "neither", "b"], filterMapWithIndex(f)), ["NO", "B"]);
 *
 * @category filtering
 * @since 2.0.0
 */
var filterMapWithIndex = function (f) {
    return function (fa) {
        var out = [];
        for (var i = 0; i < fa.length; i++) {
            var optionB = f(i, fa[i]);
            if (_.isSome(optionB)) {
                out.push(optionB.value);
            }
        }
        return out;
    };
};
exports.filterMapWithIndex = filterMapWithIndex;
/**
 * Maps an array with an iterating function that returns an `Option`
 * and it keeps only the `Some` values discarding the `None`s.
 *
 * @example
 * import { filterMap } from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 * import { option } from "fp-ts";
 *
 * const f = (s: string) => s.length === 1 ? option.some(s.toUpperCase()) : option.none;
 * assert.deepStrictEqual(pipe(["a", "no", "neither", "b"], filterMap(f)), ["A", "B"]);
 *
 * @category filtering
 * @since 2.0.0
 */
var filterMap = function (f) {
    return (0, exports.filterMapWithIndex)(function (_, a) { return f(a); });
};
exports.filterMap = filterMap;
/**
 * Compact an array of `Option`s discarding the `None` values and
 * keeping the `Some` values. It returns a new array containing the values of
 * the `Some` options.
 *
 * @example
 * import { compact } from 'fp-ts/Array'
 * import { option } from "fp-ts";
 *
 * assert.deepStrictEqual(compact([option.some("a"), option.none, option.some("b")]), ["a", "b"]);
 *
 * @category filtering
 * @since 2.0.0
 */
exports.compact = (0, exports.filterMap)(function_1.identity);
/**
 * Separate an array of `Either`s into `Left`s and `Right`s, creating two new arrays:
 * one containing all the left values and one containing all the right values.
 *
 * @example
 * import { separate } from 'fp-ts/Array'
 * import { either } from "fp-ts";
 *
 * assert.deepStrictEqual(separate([either.right("r1"), either.left("l1"), either.right("r2")]), {
 *   left: ["l1"],
 *   right: ["r1", "r2"],
 * });
 *
 * @category filtering
 * @since 2.0.0
 */
var separate = function (fa) {
    var left = [];
    var right = [];
    for (var _i = 0, fa_1 = fa; _i < fa_1.length; _i++) {
        var e = fa_1[_i];
        if (e._tag === 'Left') {
            left.push(e.left);
        }
        else {
            right.push(e.right);
        }
    }
    return (0, Separated_1.separated)(left, right);
};
exports.separate = separate;
/**
 * Given an iterating function that is a `Predicate` or a `Refinement`,
 * `filter` creates a new `Array` containing the elements of the original
 * `Array` for which the iterating function is `true`.
 *
 * @example
 * import { filter } from 'fp-ts/Array'
 * import { isString } from "fp-ts/string";
 *
 * assert.deepStrictEqual(filter(isString)(["a", 1, {}, "b", 5]), ["a", "b"]);
 * assert.deepStrictEqual(filter((x:number) => x > 0)([-3, 1, -2, 5]), [1, 5]);
 *
 * @category filtering
 * @since 2.0.0
 */
var filter = function (predicate) {
    return function (as) {
        return as.filter(predicate);
    };
};
exports.filter = filter;
/**
 * Given an iterating function that is a `Predicate` or a `Refinement`,
 * `partition` creates two new `Array`s: `right` containing the elements of the original
 * `Array` for which the iterating function is `true`, `left` containing the elements
 * for which it is false.
 *
 * @example
 * import { partition } from 'fp-ts/Array'
 * import { isString } from "fp-ts/string";
 *
 * assert.deepStrictEqual(partition(isString)(["a", 1, {}, "b", 5]), { left: [1, {}, 5], right: ["a", "b"] });
 * assert.deepStrictEqual(partition((x: number) => x > 0)([-3, 1, -2, 5]), { left: [-3, -2], right: [1, 5] });
 *
 * @category filtering
 * @since 2.0.0
 */
var partition = function (predicate) {
    return (0, exports.partitionWithIndex)(function (_, a) { return predicate(a); });
};
exports.partition = partition;
/**
 * Same as [`partition`](#partition), but passing also the index to the iterating function.
 *
 * @example
 * import { partitionWithIndex } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(partitionWithIndex((index, x: number) => index < 3 && x > 0)([-2, 5, 6, 7]), {
 *   left: [-2, 7],
 *   right: [5, 6],
 * });
 *
 * @category filtering
 * @since 2.0.0
 */
var partitionWithIndex = function (predicateWithIndex) {
    return function (as) {
        var left = [];
        var right = [];
        for (var i = 0; i < as.length; i++) {
            var b = as[i];
            if (predicateWithIndex(i, b)) {
                right.push(b);
            }
            else {
                left.push(b);
            }
        }
        return (0, Separated_1.separated)(left, right);
    };
};
exports.partitionWithIndex = partitionWithIndex;
/**
 * Given an iterating function that returns an `Either`,
 * `partitionMap` applies the iterating function to each element and it creates two `Array`s:
 * `right` containing the values of `Right` results, `left` containing the values of `Left` results.
 *
 * @example
 * import { partitionMap } from 'fp-ts/Array'
 * import { Either, left, right } from "fp-ts/Either";
 *
 * const upperIfString = <B>(x: B): Either<B, string> =>
 *   typeof x === "string" ? right(x.toUpperCase()) : left(x);
 * assert.deepStrictEqual(partitionMap(upperIfString)([-2, "hello", 6, 7, "world"]), {
 *   left: [-2, 6, 7],
 *   right: [ 'HELLO', 'WORLD' ],
 * });
 *
 * @category filtering
 * @since 2.0.0
 */
var partitionMap = function (f) { return (0, exports.partitionMapWithIndex)(function (_, a) { return f(a); }); };
exports.partitionMap = partitionMap;
/**
 * Same as [`partitionMap`](#partitionMap), but passing also the index to the iterating function.
 *
 * @example
 * import { partitionMapWithIndex } from 'fp-ts/Array'
 * import { Either, left, right } from "fp-ts/Either";
 *
 * const upperIfStringBefore3 = <B>(index: number, x: B): Either<B, string> =>
 *   index < 3 && typeof x === "string" ? right(x.toUpperCase()) : left(x);
 * assert.deepStrictEqual(partitionMapWithIndex(upperIfStringBefore3)([-2, "hello", 6, 7, "world"]), {
 *   left: [-2, 6, 7, "world"],
 *   right: ["HELLO"],
 * });
 *
 * @category filtering
 * @since 2.0.0
 */
var partitionMapWithIndex = function (f) {
    return function (fa) {
        var left = [];
        var right = [];
        for (var i = 0; i < fa.length; i++) {
            var e = f(i, fa[i]);
            if (e._tag === 'Left') {
                left.push(e.left);
            }
            else {
                right.push(e.right);
            }
        }
        return (0, Separated_1.separated)(left, right);
    };
};
exports.partitionMapWithIndex = partitionMapWithIndex;
/**
 * Less strict version of [`alt`](#alt).
 *
 * The `W` suffix (short for **W**idening) means that the return types will be merged.
 *
 * @example
 * import * as A from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(
 *   pipe(
 *     [1, 2, 3],
 *     A.altW(() => ['a', 'b'])
 *   ),
 *   [1, 2, 3, 'a', 'b']
 * )
 *
 * @category error handling
 * @since 2.9.0
 */
var altW = function (that) {
    return function (fa) {
        return fa.concat(that());
    };
};
exports.altW = altW;
/**
 * Identifies an associative operation on a type constructor. It is similar to `Semigroup`, except that it applies to
 * types of kind `* -> *`.
 *
 * In case of `Array` concatenates the inputs into a single array.
 *
 * @example
 * import * as A from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(
 *   pipe(
 *     [1, 2, 3],
 *     A.alt(() => [4, 5])
 *   ),
 *   [1, 2, 3, 4, 5]
 * )
 *
 * @category error handling
 * @since 2.0.0
 */
exports.alt = exports.altW;
/**
 * Same as [`filter`](#filter), but passing also the index to the iterating function.
 *
 * @example
 * import { filterWithIndex } from 'fp-ts/Array';
 *
 * const f = (index: number, x: number) => x > 0 && index <= 2;
 * assert.deepStrictEqual(filterWithIndex(f)([-3, 1, -2, 5]), [1]);
 *
 * @category filtering
 * @since 2.0.0
 */
var filterWithIndex = function (predicateWithIndex) {
    return function (as) {
        return as.filter(function (b, i) { return predicateWithIndex(i, b); });
    };
};
exports.filterWithIndex = filterWithIndex;
/**
 * Given an iterating function that takes `Array<A>` as input, `extend` returns
 * an array containing the results of the iterating function applied to the whole input
 * `Array`, then to the input `Array` without the first element, then to the input
 * `Array` without the first two elements, etc.
 *
 * @example
 * import { extend } from 'fp-ts/Array'
 *
 * const f = (a: string[]) => a.join(",");
 * assert.deepStrictEqual(extend(f)(["a", "b", "c"]), ["a,b,c", "b,c", "c"]);
 *
 * @since 2.0.0
 */
var extend = function (f) { return function (wa) {
    return wa.map(function (_, i) { return f(wa.slice(i)); });
}; };
exports.extend = extend;
/**
 * `duplicate` returns an array containing the whole input `Array`,
 * then to the input `Array` dropping the first element, then to the input
 * `Array` dropping the first two elements, etc.
 *
 * @example
 * import { duplicate } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(duplicate(["a", "b", "c"]), [["a", "b", "c"], ["b", "c"], ["c"]]);
 *
 * @since 2.0.0
 */
exports.duplicate = (0, exports.extend)(function_1.identity);
/**
 * Map and fold an `Array`.
 * Map the `Array` passing each value to the iterating function.
 * Then fold the results using the provided `Monoid`.
 *
 * @example
 * import { foldMap } from 'fp-ts/Array'
 *
 * const monoid = { concat: (a: string, b: string) => a + b, empty: "" };
 * const f = (s: string) => s.toUpperCase()
 * assert.deepStrictEqual(foldMap(monoid)(f)(["a", "b", "c"]), "ABC");
 *
 * @category folding
 * @since 2.0.0
 */
exports.foldMap = RA.foldMap;
/**
 * Same as [`foldMap`](#foldMap) but passing also the index to the iterating function.
 *
 * @example
 * import { foldMapWithIndex } from 'fp-ts/Array'
 *
 * const monoid = { concat: (a: string, b: string) => a + b, empty: "" };
 * const f = (index:number, s: string) => `${s.toUpperCase()}(${index})`
 * assert.deepStrictEqual(foldMapWithIndex(monoid)(f)(["a", "b", "c"]), "A(0)B(1)C(2)");
 *
 * @category folding
 * @since 2.0.0
 */
exports.foldMapWithIndex = RA.foldMapWithIndex;
/**
 * Reduces an `Array`.
 *
 * `reduce` executes the supplied iterating function on each element of the array,
 * in order, passing in the element and the return value from the calculation on the preceding element.
 *
 * The first time that the iterating function is called there is no "return value of the
 * previous calculation", the initial value is used in its place.
 *
 * @example
 * import { reduce } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(reduce(5, (acc: number, cur: number) => acc * cur)([2, 3]), 5 * 2 * 3);
 *
 * @category folding
 * @since 2.0.0
 */
exports.reduce = RA.reduce;
/**
 * Same as [`reduce`](#reduce) but passing also the index to the iterating function.
 *
 * @example
 * import { reduceWithIndex } from 'fp-ts/Array'
 *
 * const f = (index: number, acc: string, cur: unknown) =>
 *   acc + (typeof cur === "string" ? cur.toUpperCase() + index : "");
 * assert.deepStrictEqual(reduceWithIndex("", f)([2, "a", "b", null]), "A1B2");
 *
 * @category folding
 * @since 2.0.0
 */
exports.reduceWithIndex = RA.reduceWithIndex;
/**
 * Same as [`reduce`](#reduce) but applied from the end to the start.
 *
 * *Note*: the iterating function in this case takes the accumulator as the last argument.
 *
 * @example
 * import { reduceRight } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(reduceRight("", (cur: string, acc: string) => acc + cur)(["a", "b", "c"]), "cba");
 *
 * @category folding
 * @since 2.0.0
 */
exports.reduceRight = RA.reduceRight;
/**
 * Same as [`reduceRight`](#reduceRight) but passing also the index to the iterating function.
 *
 * @example
 * import { reduceRightWithIndex } from 'fp-ts/Array'
 *
 * const f = (index: number, cur: unknown, acc: string) =>
 *   acc + (typeof cur === "string" ? cur.toUpperCase() + index : "");
 * assert.deepStrictEqual(reduceRightWithIndex("", f)([2, "a", "b", null]), "B2A1");
 *
 * @category folding
 * @since 2.0.0
 */
exports.reduceRightWithIndex = RA.reduceRightWithIndex;
/**
 * Given an iterating function that returns a `HKT` (higher kinded type), `traverse`
 * applies the iterating function to each element of the `Array` and then [`sequence`](#sequence)-s
 * the results using the provided `Applicative`.
 *
 * E.g. suppose you have an `Array` and you want to format each element with a function
 * that returns a result or an error as `f = (a: A) => Either<Error, B>`, using `traverse`
 * you can apply `f` to all elements and directly obtain as a result an `Either<Error,Array<B>>`
 * i.e. an `Array<B>` if all the results are `B`, or an `Error` if some of the results
 * are `Error`s.
 *
 * @example
 * import { traverse } from 'fp-ts/Array'
 * import { Applicative, left, right } from "fp-ts/Either";
 *
 * const f = (x: unknown) =>
 *   typeof x === "string" ? right(x.toUpperCase()) : left(new Error("not a string"));
 * assert.deepStrictEqual(traverse(Applicative)(f)(["a", "b"]), right(["A", "B"]));
 * assert.deepStrictEqual(traverse(Applicative)(f)(["a", 5]), left(new Error("not a string")));
 *
 * @category traversing
 * @since 2.6.3
 */
var traverse = function (F) {
    var traverseWithIndexF = (0, exports.traverseWithIndex)(F);
    return function (f) { return traverseWithIndexF(function (_, a) { return f(a); }); };
};
exports.traverse = traverse;
/**
 * `sequence` takes an `Array` where elements are `HKT<A>` (higher kinded type) and,
 * using an applicative of that `HKT`, returns an `HKT` of `Array<A>`.
 * E.g. it can turn an `Array<Either<Error, string>>` into an `Either<Error, Array<string>>`.
 *
 * `sequence` requires an `Applicative` of the `HKT` you are targeting, e.g. to turn an
 * `Array<Either<E, A>>` into an `Either<E, Array<A>>`, it needs an
 * `Applicative` for `Either`, to to turn an `Array<Option<A>>` into an `Option<Array<A>>`,
 * it needs an `Applicative` for `Option`.
 *
 * @example
 * import { sequence } from 'fp-ts/Array'
 * import { Applicative, left, right } from "fp-ts/Either";
 *
 * assert.deepStrictEqual(sequence(Applicative)([right("a"), right("b")]), right(["a", "b"]));
 * assert.deepStrictEqual(
 *   sequence(Applicative)([right("a"), left(new Error("not a string"))]),
 *   left(new Error("not a string"))
 * );
 *
 * @category traversing
 * @since 2.6.3
 */
var sequence = function (F) {
    return function (ta) {
        return _reduce(ta, F.of((0, exports.zero)()), function (fas, fa) {
            return F.ap(F.map(fas, function (as) { return function (a) { return (0, function_1.pipe)(as, (0, exports.append)(a)); }; }), fa);
        });
    };
};
exports.sequence = sequence;
/**
 * Same as [`traverse`](#traverse) but passing also the index to the iterating function.
 *
 * @example
 * import { traverseWithIndex } from 'fp-ts/Array'
 * import { Applicative, left, right } from "fp-ts/Either";
 *
 * const f = (index:number, x:unknown) =>
 *   typeof x === "string" ? right(x.toUpperCase() + index) : left(new Error("not a string"));
 * assert.deepStrictEqual(traverseWithIndex(Applicative)(f)(["a", "b"]), right(["A0", "B1"]));
 * assert.deepStrictEqual(traverseWithIndex(Applicative)(f)(["a", 5]), left(new Error("not a string")));
 *
 * @category sequencing
 * @since 2.6.3
 */
var traverseWithIndex = function (F) {
    return function (f) {
        return (0, exports.reduceWithIndex)(F.of((0, exports.zero)()), function (i, fbs, a) {
            return F.ap(F.map(fbs, function (bs) { return function (b) { return (0, function_1.pipe)(bs, (0, exports.append)(b)); }; }), f(i, a));
        });
    };
};
exports.traverseWithIndex = traverseWithIndex;
/**
 * @category filtering
 * @since 2.6.5
 */
var wither = function (F) {
    var _witherF = _wither(F);
    return function (f) { return function (fa) { return _witherF(fa, f); }; };
};
exports.wither = wither;
/**
 * @category filtering
 * @since 2.6.5
 */
var wilt = function (F) {
    var _wiltF = _wilt(F);
    return function (f) { return function (fa) { return _wiltF(fa, f); }; };
};
exports.wilt = wilt;
/**
 * `unfold` takes a function `f` which returns an `Option` of a tuple containing an outcome
 * value and an input for the following iteration.
 * `unfold` applies `f` to the initial value `b` and then recursively to the second
 * element of the tuple contained in the returned `option` of the previous
 * calculation until `f` returns `Option.none`.
 *
 * @example
 * import { unfold } from 'fp-ts/Array'
 * import { option } from 'fp-ts'
 *
 * const f = (n: number) => {
 *   if (n <= 0) return option.none;
 *   const returnValue = n * 2;
 *   const inputForNextRound = n - 1;
 *   return option.some([returnValue, inputForNextRound] as const);
 * };
 * assert.deepStrictEqual(unfold(5, f), [10, 8, 6, 4, 2]);
 *
 * @since 2.6.6
 */
var unfold = function (b, f) {
    var out = [];
    var bb = b;
    // eslint-disable-next-line no-constant-condition
    while (true) {
        var mt = f(bb);
        if (_.isSome(mt)) {
            var _a = mt.value, a = _a[0], b_1 = _a[1];
            out.push(a);
            bb = b_1;
        }
        else {
            break;
        }
    }
    return out;
};
exports.unfold = unfold;
/**
 * @category type lambdas
 * @since 2.0.0
 */
exports.URI = 'Array';
/**
 * `getShow` makes a `Show` for an `Array<A>` from a `Show` for
 * an `A`.
 *
 * @example
 * import { getShow } from 'fp-ts/Array'
 *
 * const numShow = { show: (n: number) => (n >= 0 ? `${n}` : `(${-n})`) };
 * assert.deepStrictEqual(getShow(numShow).show([-2, -1, 0, 1]), "[(2), (1), 0, 1]");
 *
 * @category instances
 * @since 2.0.0
 */
exports.getShow = RA.getShow;
/**
 * Get a `Semigroup` based on the concatenation of `Array`s.
 * See also [`getMonoid`](#getMonoid).
 *
 * @example
 * import { getSemigroup } from 'fp-ts/Array'
 *
 * const S = getSemigroup<number>();
 * assert.deepStrictEqual(S.concat([1, 2], [2, 3]), [1, 2, 2, 3]);
 *
 * @category instances
 * @since 2.10.0
 */
var getSemigroup = function () { return ({
    concat: function (first, second) { return first.concat(second); }
}); };
exports.getSemigroup = getSemigroup;
/**
 * Returns a `Monoid` for `Array<A>` based on the concatenation of `Array`s.
 *
 * @example
 * import { getMonoid } from 'fp-ts/Array'
 *
 * const M = getMonoid<number>()
 * assert.deepStrictEqual(M.concat([1, 2], [3, 4]), [1, 2, 3, 4])
 *
 * @category instances
 * @since 2.0.0
 */
var getMonoid = function () { return ({
    concat: (0, exports.getSemigroup)().concat,
    empty: []
}); };
exports.getMonoid = getMonoid;
/**
 * Derives an `Eq` over the `Array` of a given element type from the `Eq` of that type. The derived `Eq` defines two
 * arrays as equal if all elements of both arrays are compared equal pairwise with the given `E`. In case of arrays of
 * different lengths, the result is non equality.
 *
 * @example
 * import * as S from 'fp-ts/string'
 * import { getEq } from 'fp-ts/Array'
 *
 * const E = getEq(S.Eq)
 * assert.strictEqual(E.equals(['a', 'b'], ['a', 'b']), true)
 * assert.strictEqual(E.equals(['a'], []), false)
 *
 * @category instances
 * @since 2.0.0
 */
exports.getEq = RA.getEq;
/**
 * Derives an `Ord` over the `Array` of a given element type from the `Ord` of that type. The ordering between two such
 * arrays is equal to: the first non equal comparison of each arrays elements taken pairwise in increasing order, in
 * case of equality over all the pairwise elements; the longest array is considered the greatest, if both arrays have
 * the same length, the result is equality.
 *
 * @example
 * import { getOrd } from 'fp-ts/Array'
 * import * as S from 'fp-ts/string'
 *
 * const O = getOrd(S.Ord)
 * assert.strictEqual(O.compare(['b'], ['a']), 1)
 * assert.strictEqual(O.compare(['a'], ['a']), 0)
 * assert.strictEqual(O.compare(['a'], ['b']), -1)
 *
 * @category instances
 * @since 2.0.0
 */
exports.getOrd = RA.getOrd;
/**
 * Get a `Semigroup` based on the union of the elements of `Array`s.
 * Elements which equal according to the provided `Eq` are included
 * only once in the result.
 * See also [`getUnionMonoid`](#getUnionMonoid).
 *
 * @example
 * import { getUnionSemigroup } from 'fp-ts/Array';
 * import { Eq } from 'fp-ts/number';
 *
 * const S = getUnionSemigroup<number>(Eq);
 * assert.deepStrictEqual(S.concat([1, 2], [2, 3]), [1, 2, 3]);
 *
 * @category instances
 * @since 2.11.0
 */
var getUnionSemigroup = function (E) {
    var unionE = union(E);
    return {
        concat: function (first, second) { return unionE(second)(first); }
    };
};
exports.getUnionSemigroup = getUnionSemigroup;
/**
 * Get a `Monoid` based on the union of the elements of `Array`s.
 * Elements which equal according to the provided `Eq` are included
 * only once in the result.
 *
 * @example
 * import { getUnionMonoid } from 'fp-ts/Array'
 * import { Eq } from 'fp-ts/number';
 *
 * const M = getUnionMonoid<number>(Eq);
 * assert.deepStrictEqual(M.concat([1, 2], [2, 3]), [1, 2, 3]);
 * assert.deepStrictEqual(M.empty,[]);
 *
 * @category instances
 * @since 2.11.0
 */
var getUnionMonoid = function (E) { return ({
    concat: (0, exports.getUnionSemigroup)(E).concat,
    empty: []
}); };
exports.getUnionMonoid = getUnionMonoid;
/**
 * Get a `Semigroup` based on the intersection of the elements of `Array`s.
 * Only elements present in the two arrays which are equal according to the
 * provided `Eq` are included in the result.
 *
 * @example
 * import { getIntersectionSemigroup } from 'fp-ts/Array'
 * import { Eq } from 'fp-ts/number';
 *
 * const S = getIntersectionSemigroup<number>(Eq);
 * assert.deepStrictEqual(S.concat([1, 2], [2, 3]), [2]);
 *
 * @category instances
 * @since 2.11.0
 */
var getIntersectionSemigroup = function (E) {
    var intersectionE = intersection(E);
    return {
        concat: function (first, second) { return intersectionE(second)(first); }
    };
};
exports.getIntersectionSemigroup = getIntersectionSemigroup;
/**
 * Get a `Magma` for `Array` where the `concat` function is the differnce between
 * the first and the second array, i.e. the result contains all the elements of the
 * first array for which their is no equal element in the second array according
 * to the `Eq` provided.
 *
 *
 * @example
 * import { getDifferenceMagma } from 'fp-ts/Array'
 * import { Eq } from 'fp-ts/number';
 *
 * const S = getDifferenceMagma<number>(Eq);
 * assert.deepStrictEqual(S.concat([1, 2], [2, 3]), [1]);
 *
 * @category instances
 * @since 2.11.0
 */
var getDifferenceMagma = function (E) {
    var differenceE = difference(E);
    return {
        concat: function (first, second) { return differenceE(second)(first); }
    };
};
exports.getDifferenceMagma = getDifferenceMagma;
/**
 * @category instances
 * @since 2.7.0
 */
exports.Functor = {
    URI: exports.URI,
    map: _map
};
/**
 * Given an input an `Array` of functions, `flap` returns an `Array` containing
 * the results of applying each function to the given input.
 *
 * @example
 * import { flap } from 'fp-ts/Array'
 *
 * const funs = [
 *   (n: number) => `Double: ${n * 2}`,
 *   (n: number) => `Triple: ${n * 3}`,
 *   (n: number) => `Square: ${n * n}`,
 * ];
 * assert.deepStrictEqual(flap(4)(funs), ['Double: 8', 'Triple: 12', 'Square: 16']);
 *
 * @category mapping
 * @since 2.10.0
 */
exports.flap = (0, Functor_1.flap)(exports.Functor);
/**
 * @category instances
 * @since 2.10.0
 */
exports.Pointed = {
    URI: exports.URI,
    of: exports.of
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.FunctorWithIndex = {
    URI: exports.URI,
    map: _map,
    mapWithIndex: _mapWithIndex
};
/**
 * @category instances
 * @since 2.10.0
 */
exports.Apply = {
    URI: exports.URI,
    map: _map,
    ap: _ap
};
/**
 * Combine two effectful actions, keeping only the result of the first.
 *
 * @since 2.5.0
 */
exports.apFirst = (0, Apply_1.apFirst)(exports.Apply);
/**
 * Combine two effectful actions, keeping only the result of the second.
 *
 * @since 2.5.0
 */
exports.apSecond = (0, Apply_1.apSecond)(exports.Apply);
/**
 * @category instances
 * @since 2.7.0
 */
exports.Applicative = {
    URI: exports.URI,
    map: _map,
    ap: _ap,
    of: exports.of
};
/**
 * @category instances
 * @since 2.10.0
 */
exports.Chain = {
    URI: exports.URI,
    map: _map,
    ap: _ap,
    chain: exports.flatMap
};
/**
 * Composes computations in sequence, using the return value of one computation to determine the next computation and
 * keeping only the result of the first.
 *
 * @example
 * import * as A from 'fp-ts/Array'
 * import { pipe } from 'fp-ts/function'
 *
 * assert.deepStrictEqual(
 *   pipe(
 *     [1, 2, 3],
 *     A.chainFirst(() => ['a', 'b'])
 *   ),
 *   [1, 1, 2, 2, 3, 3]
 * )
 * assert.deepStrictEqual(
 *   pipe(
 *     [1, 2, 3],
 *     A.chainFirst(() => [])
 *   ),
 *   []
 * )
 *
 * @category sequencing
 * @since 2.0.0
 */
exports.chainFirst = 
/*#__PURE__*/ (0, Chain_1.chainFirst)(exports.Chain);
/**
 * @category instances
 * @since 2.7.0
 */
exports.Monad = {
    URI: exports.URI,
    map: _map,
    ap: _ap,
    of: exports.of,
    chain: exports.flatMap
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.Unfoldable = {
    URI: exports.URI,
    unfold: exports.unfold
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.Alt = {
    URI: exports.URI,
    map: _map,
    alt: _alt
};
/**
 * @category instances
 * @since 2.11.0
 */
exports.Zero = {
    URI: exports.URI,
    zero: exports.zero
};
/**
 * @category do notation
 * @since 2.11.0
 */
exports.guard = (0, Zero_1.guard)(exports.Zero, exports.Pointed);
/**
 * @category instances
 * @since 2.7.0
 */
exports.Alternative = {
    URI: exports.URI,
    map: _map,
    ap: _ap,
    of: exports.of,
    alt: _alt,
    zero: exports.zero
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.Extend = {
    URI: exports.URI,
    map: _map,
    extend: _extend
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.Compactable = {
    URI: exports.URI,
    compact: exports.compact,
    separate: exports.separate
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.Filterable = {
    URI: exports.URI,
    map: _map,
    compact: exports.compact,
    separate: exports.separate,
    filter: _filter,
    filterMap: _filterMap,
    partition: _partition,
    partitionMap: _partitionMap
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.FilterableWithIndex = {
    URI: exports.URI,
    map: _map,
    mapWithIndex: _mapWithIndex,
    compact: exports.compact,
    separate: exports.separate,
    filter: _filter,
    filterMap: _filterMap,
    partition: _partition,
    partitionMap: _partitionMap,
    partitionMapWithIndex: _partitionMapWithIndex,
    partitionWithIndex: _partitionWithIndex,
    filterMapWithIndex: _filterMapWithIndex,
    filterWithIndex: _filterWithIndex
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.Foldable = {
    URI: exports.URI,
    reduce: _reduce,
    foldMap: _foldMap,
    reduceRight: _reduceRight
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.FoldableWithIndex = {
    URI: exports.URI,
    reduce: _reduce,
    foldMap: _foldMap,
    reduceRight: _reduceRight,
    reduceWithIndex: _reduceWithIndex,
    foldMapWithIndex: _foldMapWithIndex,
    reduceRightWithIndex: _reduceRightWithIndex
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.Traversable = {
    URI: exports.URI,
    map: _map,
    reduce: _reduce,
    foldMap: _foldMap,
    reduceRight: _reduceRight,
    traverse: _traverse,
    sequence: exports.sequence
};
/**
 * @category instances
 * @since 2.7.0
 */
exports.TraversableWithIndex = {
    URI: exports.URI,
    map: _map,
    mapWithIndex: _mapWithIndex,
    reduce: _reduce,
    foldMap: _foldMap,
    reduceRight: _reduceRight,
    reduceWithIndex: _reduceWithIndex,
    foldMapWithIndex: _foldMapWithIndex,
    reduceRightWithIndex: _reduceRightWithIndex,
    traverse: _traverse,
    sequence: exports.sequence,
    traverseWithIndex: _traverseWithIndex
};
var _wither = /*#__PURE__*/ (0, Witherable_1.witherDefault)(exports.Traversable, exports.Compactable);
var _wilt = /*#__PURE__*/ (0, Witherable_1.wiltDefault)(exports.Traversable, exports.Compactable);
/**
 * @category instances
 * @since 2.7.0
 */
exports.Witherable = {
    URI: exports.URI,
    map: _map,
    compact: exports.compact,
    separate: exports.separate,
    filter: _filter,
    filterMap: _filterMap,
    partition: _partition,
    partitionMap: _partitionMap,
    reduce: _reduce,
    foldMap: _foldMap,
    reduceRight: _reduceRight,
    traverse: _traverse,
    sequence: exports.sequence,
    wither: _wither,
    wilt: _wilt
};
/**
 * @category sequencing
 * @since 2.11.0
 */
exports.chainRecDepthFirst = RA.chainRecDepthFirst;
/**
 * @category instances
 * @since 2.11.0
 */
exports.ChainRecDepthFirst = {
    URI: exports.URI,
    map: _map,
    ap: _ap,
    chain: exports.flatMap,
    chainRec: _chainRecDepthFirst
};
/**
 * @category sequencing
 * @since 2.11.0
 */
exports.chainRecBreadthFirst = RA.chainRecBreadthFirst;
/**
 * @category instances
 * @since 2.11.0
 */
exports.ChainRecBreadthFirst = {
    URI: exports.URI,
    map: _map,
    ap: _ap,
    chain: exports.flatMap,
    chainRec: _chainRecBreadthFirst
};
/**
 * Filter values inside a context.
 *
 * @since 2.11.0
 */
exports.filterE = (0, Witherable_1.filterE)(exports.Witherable);
/**
 * @category instances
 * @since 2.11.0
 */
exports.FromEither = {
    URI: exports.URI,
    fromEither: exports.fromEither
};
/**
 * @category lifting
 * @since 2.11.0
 */
exports.fromEitherK = (0, FromEither_1.fromEitherK)(exports.FromEither);
// -------------------------------------------------------------------------------------
// unsafe
// -------------------------------------------------------------------------------------
/**
 * @category unsafe
 * @since 2.0.0
 */
exports.unsafeInsertAt = NEA.unsafeInsertAt;
/**
 * @category unsafe
 * @since 2.0.0
 */
var unsafeUpdateAt = function (i, a, as) {
    return (0, exports.isNonEmpty)(as) ? NEA.unsafeUpdateAt(i, a, as) : [];
};
exports.unsafeUpdateAt = unsafeUpdateAt;
/**
 * @category unsafe
 * @since 2.0.0
 */
var unsafeDeleteAt = function (i, as) {
    var xs = as.slice();
    xs.splice(i, 1);
    return xs;
};
exports.unsafeDeleteAt = unsafeDeleteAt;
// -------------------------------------------------------------------------------------
// utils
// -------------------------------------------------------------------------------------
/**
 * `every` tells if the provided predicate holds true for every element in the `Array`.
 *
 * @example
 * import { every } from 'fp-ts/Array'
 *
 * assert.equal(every((x: number) => x >= 0)([1, 2, 3]), true);
 * assert.equal(every((x: number) => x >= 0)([-1, 2, 3]), false);
 *
 * @since 2.9.0
 */
exports.every = RA.every;
/**
 * `some` tells if the provided predicate holds true at least for one element in the `Array`.
 *
 * @example
 * import { some } from 'fp-ts/Array'
 *
 * assert.equal(some((x: number) => x >= 0)([1, 2, 3]), true);
 * assert.equal(some((x: number) => x >= 10)([1, 2, 3]), false);
 *
 * @since 2.9.0
 */
var some = function (predicate) {
    return function (as) {
        return as.some(predicate);
    };
};
exports.some = some;
/**
 * Alias of [`some`](#some)
 *
 * @since 2.11.0
 */
exports.exists = exports.some;
/**
 * Places an element in between members of an `Array`, then folds the results using the provided `Monoid`.
 *
 * @example
 * import * as S from 'fp-ts/string'
 * import { intercalate } from 'fp-ts/Array'
 *
 * assert.deepStrictEqual(intercalate(S.Monoid)('-')(['a', 'b', 'c']), 'a-b-c')
 *
 * @since 2.12.0
 */
exports.intercalate = RA.intercalate;
// -------------------------------------------------------------------------------------
// do notation
// -------------------------------------------------------------------------------------
/**
 * @category do notation
 * @since 2.9.0
 */
exports.Do = (0, exports.of)(_.emptyRecord);
/**
 * @category do notation
 * @since 2.8.0
 */
exports.bindTo = (0, Functor_1.bindTo)(exports.Functor);
var let_ = /*#__PURE__*/ (0, Functor_1.let)(exports.Functor);
exports.let = let_;
/**
 * @category do notation
 * @since 2.8.0
 */
exports.bind = (0, Chain_1.bind)(exports.Chain);
/**
 * @category do notation
 * @since 2.8.0
 */
exports.apS = (0, Apply_1.apS)(exports.Apply);
// -------------------------------------------------------------------------------------
// legacy
// -------------------------------------------------------------------------------------
/**
 * Alias of `flatMap`.
 *
 * @category legacy
 * @since 2.0.0
 */
exports.chain = exports.flatMap;
// -------------------------------------------------------------------------------------
// deprecated
// -------------------------------------------------------------------------------------
/**
 * Use `NonEmptyArray` module instead.
 *
 * @category zone of death
 * @since 2.0.0
 * @deprecated
 */
exports.range = NEA.range;
/**
 * Use a new `[]` instead.
 *
 * @category zone of death
 * @since 2.0.0
 * @deprecated
 */
exports.empty = [];
/**
 * Use `prepend` instead.
 *
 * @category zone of death
 * @since 2.0.0
 * @deprecated
 */
exports.cons = NEA.cons;
/**
 * Use `append` instead.
 *
 * @category zone of death
 * @since 2.0.0
 * @deprecated
 */
exports.snoc = NEA.snoc;
/**
 * Use `prependAll` instead
 *
 * @category zone of death
 * @since 2.9.0
 * @deprecated
 */
exports.prependToAll = exports.prependAll;
/**
 * This instance is deprecated, use small, specific instances instead.
 * For example if a function needs a `Functor` instance, pass `A.Functor` instead of `A.array`
 * (where `A` is from `import A from 'fp-ts/Array'`)
 *
 * @category zone of death
 * @since 2.0.0
 * @deprecated
 */
exports.array = {
    URI: exports.URI,
    compact: exports.compact,
    separate: exports.separate,
    map: _map,
    ap: _ap,
    of: exports.of,
    chain: exports.flatMap,
    filter: _filter,
    filterMap: _filterMap,
    partition: _partition,
    partitionMap: _partitionMap,
    mapWithIndex: _mapWithIndex,
    partitionMapWithIndex: _partitionMapWithIndex,
    partitionWithIndex: _partitionWithIndex,
    filterMapWithIndex: _filterMapWithIndex,
    filterWithIndex: _filterWithIndex,
    alt: _alt,
    zero: exports.zero,
    unfold: exports.unfold,
    reduce: _reduce,
    foldMap: _foldMap,
    reduceRight: _reduceRight,
    traverse: _traverse,
    sequence: exports.sequence,
    reduceWithIndex: _reduceWithIndex,
    foldMapWithIndex: _foldMapWithIndex,
    reduceRightWithIndex: _reduceRightWithIndex,
    traverseWithIndex: _traverseWithIndex,
    extend: _extend,
    wither: _wither,
    wilt: _wilt
};
