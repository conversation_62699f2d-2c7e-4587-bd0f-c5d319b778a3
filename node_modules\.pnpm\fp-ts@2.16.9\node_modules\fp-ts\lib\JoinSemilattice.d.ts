/**
 * A join-semilattice (or upper semilattice) is a semilattice whose operation is called `join`, and which can be thought
 * of as a least upper bound.
 *
 * A `Join<PERSON><PERSON><PERSON><PERSON><PERSON>` must satisfy the following laws:
 *
 * - Associativity: `a ∨ (b ∨ c) <-> (a ∨ b) ∨ c`
 * - Commutativity: `a ∨ b <-> b ∨ a`
 * - Idempotency:   `a ∨ a <-> a`
 *
 * @since 2.0.0
 */
/**
 * @category model
 * @since 2.0.0
 */
export interface JoinSemilattice<A> {
  readonly join: (x: A, y: A) => A
}
