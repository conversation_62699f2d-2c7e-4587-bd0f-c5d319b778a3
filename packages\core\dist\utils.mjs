import { tmpdir } from 'node:os';
import { Buffer } from 'node:buffer';
import { existsSync } from 'node:fs';
import { join } from 'pathe';
import { ensureDir } from 'fs-extra';

async function saveFileBox(file, dir = tmpdir()) {
  const dirPath = join(dir, "wechatferry");
  const filePath = join(dirPath, `${( new Date()).getTime()}-${file.name}`);
  if (!existsSync(dirPath)) {
    await ensureDir(dirPath);
  }
  await file.toFile(filePath);
  return filePath;
}
function parseDbField(type, content) {
  switch (type) {
    case 1:
      return Number.parseInt(uint8Array2str(content), 10);
    case 2:
      return Number.parseFloat(uint8Array2str(content));
    case 4:
      return Buffer.from(content);
    case 5:
      return void 0;
    case 3:
    default:
      return uint8Array2str(content);
  }
}
function uint8Array2str(arr) {
  return Buffer.from(arr).toString();
}

export { parseDbField, saveFileBox, uint8Array2str };
