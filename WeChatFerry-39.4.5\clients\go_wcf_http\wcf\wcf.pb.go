// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.12
// source: wcf.proto

package wcf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Functions int32

const (
	Functions_FUNC_RESERVED         Functions = 0
	Functions_FUNC_IS_LOGIN         Functions = 1
	Functions_FUNC_GET_SELF_WXID    Functions = 16
	Functions_FUNC_GET_MSG_TYPES    Functions = 17
	Functions_FUNC_GET_CONTACTS     Functions = 18
	Functions_FUNC_GET_DB_NAMES     Functions = 19
	Functions_FUNC_GET_DB_TABLES    Functions = 20
	Functions_FUNC_GET_USER_INFO    Functions = 21
	Functions_FUNC_GET_AUDIO_MSG    Functions = 22
	Functions_FUNC_SEND_TXT         Functions = 32
	Functions_FUNC_SEND_IMG         Functions = 33
	Functions_FUNC_SEND_FILE        Functions = 34
	Functions_FUNC_SEND_XML         Functions = 35
	Functions_FUNC_SEND_EMOTION     Functions = 36
	Functions_FUNC_SEND_RICH_TXT    Functions = 37
	Functions_FUNC_SEND_PAT_MSG     Functions = 38
	Functions_FUNC_FORWARD_MSG      Functions = 39
	Functions_FUNC_ENABLE_RECV_TXT  Functions = 48
	Functions_FUNC_DISABLE_RECV_TXT Functions = 64
	Functions_FUNC_EXEC_DB_QUERY    Functions = 80
	Functions_FUNC_ACCEPT_FRIEND    Functions = 81
	Functions_FUNC_RECV_TRANSFER    Functions = 82
	Functions_FUNC_REFRESH_PYQ      Functions = 83
	Functions_FUNC_DOWNLOAD_ATTACH  Functions = 84
	Functions_FUNC_GET_CONTACT_INFO Functions = 85
	Functions_FUNC_REVOKE_MSG       Functions = 86
	Functions_FUNC_DECRYPT_IMAGE    Functions = 96
	Functions_FUNC_EXEC_OCR         Functions = 97
	Functions_FUNC_ADD_ROOM_MEMBERS Functions = 112
	Functions_FUNC_DEL_ROOM_MEMBERS Functions = 113
	Functions_FUNC_INV_ROOM_MEMBERS Functions = 114
)

// Enum value maps for Functions.
var (
	Functions_name = map[int32]string{
		0:   "FUNC_RESERVED",
		1:   "FUNC_IS_LOGIN",
		16:  "FUNC_GET_SELF_WXID",
		17:  "FUNC_GET_MSG_TYPES",
		18:  "FUNC_GET_CONTACTS",
		19:  "FUNC_GET_DB_NAMES",
		20:  "FUNC_GET_DB_TABLES",
		21:  "FUNC_GET_USER_INFO",
		22:  "FUNC_GET_AUDIO_MSG",
		32:  "FUNC_SEND_TXT",
		33:  "FUNC_SEND_IMG",
		34:  "FUNC_SEND_FILE",
		35:  "FUNC_SEND_XML",
		36:  "FUNC_SEND_EMOTION",
		37:  "FUNC_SEND_RICH_TXT",
		38:  "FUNC_SEND_PAT_MSG",
		39:  "FUNC_FORWARD_MSG",
		48:  "FUNC_ENABLE_RECV_TXT",
		64:  "FUNC_DISABLE_RECV_TXT",
		80:  "FUNC_EXEC_DB_QUERY",
		81:  "FUNC_ACCEPT_FRIEND",
		82:  "FUNC_RECV_TRANSFER",
		83:  "FUNC_REFRESH_PYQ",
		84:  "FUNC_DOWNLOAD_ATTACH",
		85:  "FUNC_GET_CONTACT_INFO",
		86:  "FUNC_REVOKE_MSG",
		96:  "FUNC_DECRYPT_IMAGE",
		97:  "FUNC_EXEC_OCR",
		112: "FUNC_ADD_ROOM_MEMBERS",
		113: "FUNC_DEL_ROOM_MEMBERS",
		114: "FUNC_INV_ROOM_MEMBERS",
	}
	Functions_value = map[string]int32{
		"FUNC_RESERVED":         0,
		"FUNC_IS_LOGIN":         1,
		"FUNC_GET_SELF_WXID":    16,
		"FUNC_GET_MSG_TYPES":    17,
		"FUNC_GET_CONTACTS":     18,
		"FUNC_GET_DB_NAMES":     19,
		"FUNC_GET_DB_TABLES":    20,
		"FUNC_GET_USER_INFO":    21,
		"FUNC_GET_AUDIO_MSG":    22,
		"FUNC_SEND_TXT":         32,
		"FUNC_SEND_IMG":         33,
		"FUNC_SEND_FILE":        34,
		"FUNC_SEND_XML":         35,
		"FUNC_SEND_EMOTION":     36,
		"FUNC_SEND_RICH_TXT":    37,
		"FUNC_SEND_PAT_MSG":     38,
		"FUNC_FORWARD_MSG":      39,
		"FUNC_ENABLE_RECV_TXT":  48,
		"FUNC_DISABLE_RECV_TXT": 64,
		"FUNC_EXEC_DB_QUERY":    80,
		"FUNC_ACCEPT_FRIEND":    81,
		"FUNC_RECV_TRANSFER":    82,
		"FUNC_REFRESH_PYQ":      83,
		"FUNC_DOWNLOAD_ATTACH":  84,
		"FUNC_GET_CONTACT_INFO": 85,
		"FUNC_REVOKE_MSG":       86,
		"FUNC_DECRYPT_IMAGE":    96,
		"FUNC_EXEC_OCR":         97,
		"FUNC_ADD_ROOM_MEMBERS": 112,
		"FUNC_DEL_ROOM_MEMBERS": 113,
		"FUNC_INV_ROOM_MEMBERS": 114,
	}
)

func (x Functions) Enum() *Functions {
	p := new(Functions)
	*p = x
	return p
}

func (x Functions) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Functions) Descriptor() protoreflect.EnumDescriptor {
	return file_wcf_proto_enumTypes[0].Descriptor()
}

func (Functions) Type() protoreflect.EnumType {
	return &file_wcf_proto_enumTypes[0]
}

func (x Functions) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Functions.Descriptor instead.
func (Functions) EnumDescriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{0}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Func Functions `protobuf:"varint,1,opt,name=func,proto3,enum=wcf.Functions" json:"func,omitempty"`
	// Types that are assignable to Msg:
	//
	//	*Request_Empty
	//	*Request_Str
	//	*Request_Txt
	//	*Request_File
	//	*Request_Query
	//	*Request_V
	//	*Request_M
	//	*Request_Xml
	//	*Request_Dec
	//	*Request_Tf
	//	*Request_Ui64
	//	*Request_Flag
	//	*Request_Att
	//	*Request_Am
	//	*Request_Rt
	//	*Request_Pm
	//	*Request_Fm
	Msg isRequest_Msg `protobuf_oneof:"msg"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetFunc() Functions {
	if x != nil {
		return x.Func
	}
	return Functions_FUNC_RESERVED
}

func (m *Request) GetMsg() isRequest_Msg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (x *Request) GetEmpty() *Empty {
	if x, ok := x.GetMsg().(*Request_Empty); ok {
		return x.Empty
	}
	return nil
}

func (x *Request) GetStr() string {
	if x, ok := x.GetMsg().(*Request_Str); ok {
		return x.Str
	}
	return ""
}

func (x *Request) GetTxt() *TextMsg {
	if x, ok := x.GetMsg().(*Request_Txt); ok {
		return x.Txt
	}
	return nil
}

func (x *Request) GetFile() *PathMsg {
	if x, ok := x.GetMsg().(*Request_File); ok {
		return x.File
	}
	return nil
}

func (x *Request) GetQuery() *DbQuery {
	if x, ok := x.GetMsg().(*Request_Query); ok {
		return x.Query
	}
	return nil
}

func (x *Request) GetV() *Verification {
	if x, ok := x.GetMsg().(*Request_V); ok {
		return x.V
	}
	return nil
}

func (x *Request) GetM() *MemberMgmt {
	if x, ok := x.GetMsg().(*Request_M); ok {
		return x.M
	}
	return nil
}

func (x *Request) GetXml() *XmlMsg {
	if x, ok := x.GetMsg().(*Request_Xml); ok {
		return x.Xml
	}
	return nil
}

func (x *Request) GetDec() *DecPath {
	if x, ok := x.GetMsg().(*Request_Dec); ok {
		return x.Dec
	}
	return nil
}

func (x *Request) GetTf() *Transfer {
	if x, ok := x.GetMsg().(*Request_Tf); ok {
		return x.Tf
	}
	return nil
}

func (x *Request) GetUi64() uint64 {
	if x, ok := x.GetMsg().(*Request_Ui64); ok {
		return x.Ui64
	}
	return 0
}

func (x *Request) GetFlag() bool {
	if x, ok := x.GetMsg().(*Request_Flag); ok {
		return x.Flag
	}
	return false
}

func (x *Request) GetAtt() *AttachMsg {
	if x, ok := x.GetMsg().(*Request_Att); ok {
		return x.Att
	}
	return nil
}

func (x *Request) GetAm() *AudioMsg {
	if x, ok := x.GetMsg().(*Request_Am); ok {
		return x.Am
	}
	return nil
}

func (x *Request) GetRt() *RichText {
	if x, ok := x.GetMsg().(*Request_Rt); ok {
		return x.Rt
	}
	return nil
}

func (x *Request) GetPm() *PatMsg {
	if x, ok := x.GetMsg().(*Request_Pm); ok {
		return x.Pm
	}
	return nil
}

func (x *Request) GetFm() *ForwardMsg {
	if x, ok := x.GetMsg().(*Request_Fm); ok {
		return x.Fm
	}
	return nil
}

type isRequest_Msg interface {
	isRequest_Msg()
}

type Request_Empty struct {
	Empty *Empty `protobuf:"bytes,2,opt,name=empty,proto3,oneof"`
}

type Request_Str struct {
	Str string `protobuf:"bytes,3,opt,name=str,proto3,oneof"`
}

type Request_Txt struct {
	Txt *TextMsg `protobuf:"bytes,4,opt,name=txt,proto3,oneof"`
}

type Request_File struct {
	File *PathMsg `protobuf:"bytes,5,opt,name=file,proto3,oneof"`
}

type Request_Query struct {
	Query *DbQuery `protobuf:"bytes,6,opt,name=query,proto3,oneof"`
}

type Request_V struct {
	V *Verification `protobuf:"bytes,7,opt,name=v,proto3,oneof"`
}

type Request_M struct {
	M *MemberMgmt `protobuf:"bytes,8,opt,name=m,proto3,oneof"` // 群成员管理，添加、删除、邀请
}

type Request_Xml struct {
	Xml *XmlMsg `protobuf:"bytes,9,opt,name=xml,proto3,oneof"`
}

type Request_Dec struct {
	Dec *DecPath `protobuf:"bytes,10,opt,name=dec,proto3,oneof"`
}

type Request_Tf struct {
	Tf *Transfer `protobuf:"bytes,11,opt,name=tf,proto3,oneof"`
}

type Request_Ui64 struct {
	Ui64 uint64 `protobuf:"varint,12,opt,name=ui64,proto3,oneof"` // 64 位整数，通用
}

type Request_Flag struct {
	Flag bool `protobuf:"varint,13,opt,name=flag,proto3,oneof"`
}

type Request_Att struct {
	Att *AttachMsg `protobuf:"bytes,14,opt,name=att,proto3,oneof"`
}

type Request_Am struct {
	Am *AudioMsg `protobuf:"bytes,15,opt,name=am,proto3,oneof"`
}

type Request_Rt struct {
	Rt *RichText `protobuf:"bytes,16,opt,name=rt,proto3,oneof"`
}

type Request_Pm struct {
	Pm *PatMsg `protobuf:"bytes,17,opt,name=pm,proto3,oneof"`
}

type Request_Fm struct {
	Fm *ForwardMsg `protobuf:"bytes,18,opt,name=fm,proto3,oneof"`
}

func (*Request_Empty) isRequest_Msg() {}

func (*Request_Str) isRequest_Msg() {}

func (*Request_Txt) isRequest_Msg() {}

func (*Request_File) isRequest_Msg() {}

func (*Request_Query) isRequest_Msg() {}

func (*Request_V) isRequest_Msg() {}

func (*Request_M) isRequest_Msg() {}

func (*Request_Xml) isRequest_Msg() {}

func (*Request_Dec) isRequest_Msg() {}

func (*Request_Tf) isRequest_Msg() {}

func (*Request_Ui64) isRequest_Msg() {}

func (*Request_Flag) isRequest_Msg() {}

func (*Request_Att) isRequest_Msg() {}

func (*Request_Am) isRequest_Msg() {}

func (*Request_Rt) isRequest_Msg() {}

func (*Request_Pm) isRequest_Msg() {}

func (*Request_Fm) isRequest_Msg() {}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Func Functions `protobuf:"varint,1,opt,name=func,proto3,enum=wcf.Functions" json:"func,omitempty"`
	// Types that are assignable to Msg:
	//
	//	*Response_Status
	//	*Response_Str
	//	*Response_Wxmsg
	//	*Response_Types
	//	*Response_Contacts
	//	*Response_Dbs
	//	*Response_Tables
	//	*Response_Rows
	//	*Response_Ui
	//	*Response_Ocr
	Msg isResponse_Msg `protobuf_oneof:"msg"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetFunc() Functions {
	if x != nil {
		return x.Func
	}
	return Functions_FUNC_RESERVED
}

func (m *Response) GetMsg() isResponse_Msg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (x *Response) GetStatus() int32 {
	if x, ok := x.GetMsg().(*Response_Status); ok {
		return x.Status
	}
	return 0
}

func (x *Response) GetStr() string {
	if x, ok := x.GetMsg().(*Response_Str); ok {
		return x.Str
	}
	return ""
}

func (x *Response) GetWxmsg() *WxMsg {
	if x, ok := x.GetMsg().(*Response_Wxmsg); ok {
		return x.Wxmsg
	}
	return nil
}

func (x *Response) GetTypes() *MsgTypes {
	if x, ok := x.GetMsg().(*Response_Types); ok {
		return x.Types
	}
	return nil
}

func (x *Response) GetContacts() *RpcContacts {
	if x, ok := x.GetMsg().(*Response_Contacts); ok {
		return x.Contacts
	}
	return nil
}

func (x *Response) GetDbs() *DbNames {
	if x, ok := x.GetMsg().(*Response_Dbs); ok {
		return x.Dbs
	}
	return nil
}

func (x *Response) GetTables() *DbTables {
	if x, ok := x.GetMsg().(*Response_Tables); ok {
		return x.Tables
	}
	return nil
}

func (x *Response) GetRows() *DbRows {
	if x, ok := x.GetMsg().(*Response_Rows); ok {
		return x.Rows
	}
	return nil
}

func (x *Response) GetUi() *UserInfo {
	if x, ok := x.GetMsg().(*Response_Ui); ok {
		return x.Ui
	}
	return nil
}

func (x *Response) GetOcr() *OcrMsg {
	if x, ok := x.GetMsg().(*Response_Ocr); ok {
		return x.Ocr
	}
	return nil
}

type isResponse_Msg interface {
	isResponse_Msg()
}

type Response_Status struct {
	Status int32 `protobuf:"varint,2,opt,name=status,proto3,oneof"` // Int 状态，通用
}

type Response_Str struct {
	Str string `protobuf:"bytes,3,opt,name=str,proto3,oneof"` // 字符串
}

type Response_Wxmsg struct {
	Wxmsg *WxMsg `protobuf:"bytes,4,opt,name=wxmsg,proto3,oneof"` // 微信消息
}

type Response_Types struct {
	Types *MsgTypes `protobuf:"bytes,5,opt,name=types,proto3,oneof"` // 消息类型
}

type Response_Contacts struct {
	Contacts *RpcContacts `protobuf:"bytes,6,opt,name=contacts,proto3,oneof"` // 联系人
}

type Response_Dbs struct {
	Dbs *DbNames `protobuf:"bytes,7,opt,name=dbs,proto3,oneof"` // 数据库列表
}

type Response_Tables struct {
	Tables *DbTables `protobuf:"bytes,8,opt,name=tables,proto3,oneof"` // 表列表
}

type Response_Rows struct {
	Rows *DbRows `protobuf:"bytes,9,opt,name=rows,proto3,oneof"` // 行列表
}

type Response_Ui struct {
	Ui *UserInfo `protobuf:"bytes,10,opt,name=ui,proto3,oneof"` // 个人信息
}

type Response_Ocr struct {
	Ocr *OcrMsg `protobuf:"bytes,11,opt,name=ocr,proto3,oneof"` // OCR 结果
}

func (*Response_Status) isResponse_Msg() {}

func (*Response_Str) isResponse_Msg() {}

func (*Response_Wxmsg) isResponse_Msg() {}

func (*Response_Types) isResponse_Msg() {}

func (*Response_Contacts) isResponse_Msg() {}

func (*Response_Dbs) isResponse_Msg() {}

func (*Response_Tables) isResponse_Msg() {}

func (*Response_Rows) isResponse_Msg() {}

func (*Response_Ui) isResponse_Msg() {}

func (*Response_Ocr) isResponse_Msg() {}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{2}
}

type WxMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSelf  bool   `protobuf:"varint,1,opt,name=is_self,json=isSelf,proto3" json:"is_self,omitempty"`    // 是否自己发送的
	IsGroup bool   `protobuf:"varint,2,opt,name=is_group,json=isGroup,proto3" json:"is_group,omitempty"` // 是否群消息
	Id      uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                          // 消息 id
	Type    uint32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`                      // 消息类型
	Ts      uint32 `protobuf:"varint,5,opt,name=ts,proto3" json:"ts,omitempty"`                          // 消息类型
	Roomid  string `protobuf:"bytes,6,opt,name=roomid,proto3" json:"roomid,omitempty"`                   // 群 id（如果是群消息的话）
	Content string `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`                 // 消息内容
	Sender  string `protobuf:"bytes,8,opt,name=sender,proto3" json:"sender,omitempty"`                   // 消息发送者
	Sign    string `protobuf:"bytes,9,opt,name=sign,proto3" json:"sign,omitempty"`                       // Sign
	Thumb   string `protobuf:"bytes,10,opt,name=thumb,proto3" json:"thumb,omitempty"`                    // 缩略图
	Extra   string `protobuf:"bytes,11,opt,name=extra,proto3" json:"extra,omitempty"`                    // 附加内容
	Xml     string `protobuf:"bytes,12,opt,name=xml,proto3" json:"xml,omitempty"`                        // 消息 xml
}

func (x *WxMsg) Reset() {
	*x = WxMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxMsg) ProtoMessage() {}

func (x *WxMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxMsg.ProtoReflect.Descriptor instead.
func (*WxMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{3}
}

func (x *WxMsg) GetIsSelf() bool {
	if x != nil {
		return x.IsSelf
	}
	return false
}

func (x *WxMsg) GetIsGroup() bool {
	if x != nil {
		return x.IsGroup
	}
	return false
}

func (x *WxMsg) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WxMsg) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *WxMsg) GetTs() uint32 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *WxMsg) GetRoomid() string {
	if x != nil {
		return x.Roomid
	}
	return ""
}

func (x *WxMsg) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *WxMsg) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *WxMsg) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *WxMsg) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *WxMsg) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *WxMsg) GetXml() string {
	if x != nil {
		return x.Xml
	}
	return ""
}

type TextMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg      string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`           // 要发送的消息内容
	Receiver string `protobuf:"bytes,2,opt,name=receiver,proto3" json:"receiver,omitempty"` // 消息接收人，当为群时可@
	Aters    string `protobuf:"bytes,3,opt,name=aters,proto3" json:"aters,omitempty"`       // 要@的人列表，逗号分隔
}

func (x *TextMsg) Reset() {
	*x = TextMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextMsg) ProtoMessage() {}

func (x *TextMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextMsg.ProtoReflect.Descriptor instead.
func (*TextMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{4}
}

func (x *TextMsg) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TextMsg) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *TextMsg) GetAters() string {
	if x != nil {
		return x.Aters
	}
	return ""
}

type PathMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path     string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`         // 要发送的图片的路径
	Receiver string `protobuf:"bytes,2,opt,name=receiver,proto3" json:"receiver,omitempty"` // 消息接收人
}

func (x *PathMsg) Reset() {
	*x = PathMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PathMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PathMsg) ProtoMessage() {}

func (x *PathMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PathMsg.ProtoReflect.Descriptor instead.
func (*PathMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{5}
}

func (x *PathMsg) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *PathMsg) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

type XmlMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Receiver string `protobuf:"bytes,1,opt,name=receiver,proto3" json:"receiver,omitempty"` // 消息接收人
	Content  string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`   // xml 内容
	Path     string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`         // 图片路径
	Type     int32  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`        // 消息类型
}

func (x *XmlMsg) Reset() {
	*x = XmlMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *XmlMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*XmlMsg) ProtoMessage() {}

func (x *XmlMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use XmlMsg.ProtoReflect.Descriptor instead.
func (*XmlMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{6}
}

func (x *XmlMsg) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *XmlMsg) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *XmlMsg) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *XmlMsg) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type MsgTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Types map[int32]string `protobuf:"bytes,1,rep,name=types,proto3" json:"types,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MsgTypes) Reset() {
	*x = MsgTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgTypes) ProtoMessage() {}

func (x *MsgTypes) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgTypes.ProtoReflect.Descriptor instead.
func (*MsgTypes) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{7}
}

func (x *MsgTypes) GetTypes() map[int32]string {
	if x != nil {
		return x.Types
	}
	return nil
}

type RpcContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wxid     string `protobuf:"bytes,1,opt,name=wxid,proto3" json:"wxid"`         // 微信 id
	Code     string `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`         // 微信号
	Remark   string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`     // 备注
	Name     string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`         // 微信昵称
	Country  string `protobuf:"bytes,5,opt,name=country,proto3" json:"country"`   // 国家
	Province string `protobuf:"bytes,6,opt,name=province,proto3" json:"province"` // 省/州
	City     string `protobuf:"bytes,7,opt,name=city,proto3" json:"city"`         // 城市
	Gender   int32  `protobuf:"varint,8,opt,name=gender,proto3" json:"gender"`    // 性别
}

func (x *RpcContact) Reset() {
	*x = RpcContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcContact) ProtoMessage() {}

func (x *RpcContact) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcContact.ProtoReflect.Descriptor instead.
func (*RpcContact) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{8}
}

func (x *RpcContact) GetWxid() string {
	if x != nil {
		return x.Wxid
	}
	return ""
}

func (x *RpcContact) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *RpcContact) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *RpcContact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RpcContact) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *RpcContact) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *RpcContact) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *RpcContact) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type RpcContacts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Contacts []*RpcContact `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
}

func (x *RpcContacts) Reset() {
	*x = RpcContacts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcContacts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcContacts) ProtoMessage() {}

func (x *RpcContacts) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcContacts.ProtoReflect.Descriptor instead.
func (*RpcContacts) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{9}
}

func (x *RpcContacts) GetContacts() []*RpcContact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

type DbNames struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Names []string `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
}

func (x *DbNames) Reset() {
	*x = DbNames{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DbNames) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbNames) ProtoMessage() {}

func (x *DbNames) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbNames.ProtoReflect.Descriptor instead.
func (*DbNames) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{10}
}

func (x *DbNames) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

type DbTable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 表名
	Sql  string `protobuf:"bytes,2,opt,name=sql,proto3" json:"sql,omitempty"`   // 建表 SQL
}

func (x *DbTable) Reset() {
	*x = DbTable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DbTable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbTable) ProtoMessage() {}

func (x *DbTable) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbTable.ProtoReflect.Descriptor instead.
func (*DbTable) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{11}
}

func (x *DbTable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DbTable) GetSql() string {
	if x != nil {
		return x.Sql
	}
	return ""
}

type DbTables struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tables []*DbTable `protobuf:"bytes,1,rep,name=tables,proto3" json:"tables,omitempty"`
}

func (x *DbTables) Reset() {
	*x = DbTables{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DbTables) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbTables) ProtoMessage() {}

func (x *DbTables) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbTables.ProtoReflect.Descriptor instead.
func (*DbTables) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{12}
}

func (x *DbTables) GetTables() []*DbTable {
	if x != nil {
		return x.Tables
	}
	return nil
}

type DbQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db  string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`   // 目标数据库
	Sql string `protobuf:"bytes,2,opt,name=sql,proto3" json:"sql,omitempty"` // 查询 SQL
}

func (x *DbQuery) Reset() {
	*x = DbQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DbQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbQuery) ProtoMessage() {}

func (x *DbQuery) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbQuery.ProtoReflect.Descriptor instead.
func (*DbQuery) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{13}
}

func (x *DbQuery) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *DbQuery) GetSql() string {
	if x != nil {
		return x.Sql
	}
	return ""
}

type DbField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    int32  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`      // 字段类型
	Column  string `protobuf:"bytes,2,opt,name=column,proto3" json:"column,omitempty"`   // 字段名称
	Content []byte `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"` // 字段内容
}

func (x *DbField) Reset() {
	*x = DbField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DbField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbField) ProtoMessage() {}

func (x *DbField) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbField.ProtoReflect.Descriptor instead.
func (*DbField) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{14}
}

func (x *DbField) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DbField) GetColumn() string {
	if x != nil {
		return x.Column
	}
	return ""
}

func (x *DbField) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

type DbRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fields []*DbField `protobuf:"bytes,1,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *DbRow) Reset() {
	*x = DbRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DbRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbRow) ProtoMessage() {}

func (x *DbRow) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbRow.ProtoReflect.Descriptor instead.
func (*DbRow) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{15}
}

func (x *DbRow) GetFields() []*DbField {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DbRows struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rows []*DbRow `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *DbRows) Reset() {
	*x = DbRows{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DbRows) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbRows) ProtoMessage() {}

func (x *DbRows) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbRows.ProtoReflect.Descriptor instead.
func (*DbRows) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{16}
}

func (x *DbRows) GetRows() []*DbRow {
	if x != nil {
		return x.Rows
	}
	return nil
}

type Verification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V3    string `protobuf:"bytes,1,opt,name=v3,proto3" json:"v3,omitempty"`        // 加密的用户名
	V4    string `protobuf:"bytes,2,opt,name=v4,proto3" json:"v4,omitempty"`        // Ticket
	Scene int32  `protobuf:"varint,3,opt,name=scene,proto3" json:"scene,omitempty"` // 添加方式：17 名片，30 扫码
}

func (x *Verification) Reset() {
	*x = Verification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Verification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Verification) ProtoMessage() {}

func (x *Verification) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Verification.ProtoReflect.Descriptor instead.
func (*Verification) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{17}
}

func (x *Verification) GetV3() string {
	if x != nil {
		return x.V3
	}
	return ""
}

func (x *Verification) GetV4() string {
	if x != nil {
		return x.V4
	}
	return ""
}

func (x *Verification) GetScene() int32 {
	if x != nil {
		return x.Scene
	}
	return 0
}

type MemberMgmt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roomid string `protobuf:"bytes,1,opt,name=roomid,proto3" json:"roomid,omitempty"` // 要加的群ID
	Wxids  string `protobuf:"bytes,2,opt,name=wxids,proto3" json:"wxids,omitempty"`   // 要加群的人列表，逗号分隔
}

func (x *MemberMgmt) Reset() {
	*x = MemberMgmt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberMgmt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberMgmt) ProtoMessage() {}

func (x *MemberMgmt) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberMgmt.ProtoReflect.Descriptor instead.
func (*MemberMgmt) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{18}
}

func (x *MemberMgmt) GetRoomid() string {
	if x != nil {
		return x.Roomid
	}
	return ""
}

func (x *MemberMgmt) GetWxids() string {
	if x != nil {
		return x.Wxids
	}
	return ""
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wxid   string `protobuf:"bytes,1,opt,name=wxid,proto3" json:"wxid,omitempty"`     // 微信ID
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`     // 昵称
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"` // 手机号
	Home   string `protobuf:"bytes,4,opt,name=home,proto3" json:"home,omitempty"`     // 文件/图片等父路径
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{19}
}

func (x *UserInfo) GetWxid() string {
	if x != nil {
		return x.Wxid
	}
	return ""
}

func (x *UserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UserInfo) GetHome() string {
	if x != nil {
		return x.Home
	}
	return ""
}

type DecPath struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Src string `protobuf:"bytes,1,opt,name=src,proto3" json:"src,omitempty"` // 源路径
	Dst string `protobuf:"bytes,2,opt,name=dst,proto3" json:"dst,omitempty"` // 目标路径
}

func (x *DecPath) Reset() {
	*x = DecPath{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DecPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecPath) ProtoMessage() {}

func (x *DecPath) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecPath.ProtoReflect.Descriptor instead.
func (*DecPath) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{20}
}

func (x *DecPath) GetSrc() string {
	if x != nil {
		return x.Src
	}
	return ""
}

func (x *DecPath) GetDst() string {
	if x != nil {
		return x.Dst
	}
	return ""
}

type Transfer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wxid string `protobuf:"bytes,1,opt,name=wxid,proto3" json:"wxid,omitempty"` // 转账人
	Tfid string `protobuf:"bytes,2,opt,name=tfid,proto3" json:"tfid,omitempty"` // 转账id transferid
	Taid string `protobuf:"bytes,3,opt,name=taid,proto3" json:"taid,omitempty"` // Transaction id
}

func (x *Transfer) Reset() {
	*x = Transfer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Transfer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transfer) ProtoMessage() {}

func (x *Transfer) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transfer.ProtoReflect.Descriptor instead.
func (*Transfer) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{21}
}

func (x *Transfer) GetWxid() string {
	if x != nil {
		return x.Wxid
	}
	return ""
}

func (x *Transfer) GetTfid() string {
	if x != nil {
		return x.Tfid
	}
	return ""
}

func (x *Transfer) GetTaid() string {
	if x != nil {
		return x.Taid
	}
	return ""
}

type AttachMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`      // 消息 id
	Thumb string `protobuf:"bytes,2,opt,name=thumb,proto3" json:"thumb,omitempty"` // 消息中的 thumb
	Extra string `protobuf:"bytes,3,opt,name=extra,proto3" json:"extra,omitempty"` // 消息中的 extra
}

func (x *AttachMsg) Reset() {
	*x = AttachMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttachMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttachMsg) ProtoMessage() {}

func (x *AttachMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttachMsg.ProtoReflect.Descriptor instead.
func (*AttachMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{22}
}

func (x *AttachMsg) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AttachMsg) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *AttachMsg) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

type AudioMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`  // 语音消息 id
	Dir string `protobuf:"bytes,2,opt,name=dir,proto3" json:"dir,omitempty"` // 存放目录
}

func (x *AudioMsg) Reset() {
	*x = AudioMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudioMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioMsg) ProtoMessage() {}

func (x *AudioMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioMsg.ProtoReflect.Descriptor instead.
func (*AudioMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{23}
}

func (x *AudioMsg) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AudioMsg) GetDir() string {
	if x != nil {
		return x.Dir
	}
	return ""
}

type RichText struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`         // 显示名字
	Account  string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`   // 公众号 id
	Title    string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`       // 标题
	Digest   string `protobuf:"bytes,4,opt,name=digest,proto3" json:"digest,omitempty"`     // 摘要
	Url      string `protobuf:"bytes,5,opt,name=url,proto3" json:"url,omitempty"`           // 链接
	Thumburl string `protobuf:"bytes,6,opt,name=thumburl,proto3" json:"thumburl,omitempty"` // 缩略图
	Receiver string `protobuf:"bytes,7,opt,name=receiver,proto3" json:"receiver,omitempty"` // 接收人
}

func (x *RichText) Reset() {
	*x = RichText{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RichText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RichText) ProtoMessage() {}

func (x *RichText) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RichText.ProtoReflect.Descriptor instead.
func (*RichText) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{24}
}

func (x *RichText) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RichText) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *RichText) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RichText) GetDigest() string {
	if x != nil {
		return x.Digest
	}
	return ""
}

func (x *RichText) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RichText) GetThumburl() string {
	if x != nil {
		return x.Thumburl
	}
	return ""
}

func (x *RichText) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

type PatMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roomid string `protobuf:"bytes,1,opt,name=roomid,proto3" json:"roomid,omitempty"` // 群 id
	Wxid   string `protobuf:"bytes,2,opt,name=wxid,proto3" json:"wxid,omitempty"`     // wxid
}

func (x *PatMsg) Reset() {
	*x = PatMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PatMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PatMsg) ProtoMessage() {}

func (x *PatMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PatMsg.ProtoReflect.Descriptor instead.
func (*PatMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{25}
}

func (x *PatMsg) GetRoomid() string {
	if x != nil {
		return x.Roomid
	}
	return ""
}

func (x *PatMsg) GetWxid() string {
	if x != nil {
		return x.Wxid
	}
	return ""
}

type OcrMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 状态
	Result string `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`  // 结果
}

func (x *OcrMsg) Reset() {
	*x = OcrMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OcrMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OcrMsg) ProtoMessage() {}

func (x *OcrMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OcrMsg.ProtoReflect.Descriptor instead.
func (*OcrMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{26}
}

func (x *OcrMsg) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *OcrMsg) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

type ForwardMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`            // 待转发消息 ID
	Receiver string `protobuf:"bytes,2,opt,name=receiver,proto3" json:"receiver,omitempty"` // 转发接收目标，群为 roomId，个人为 wxid
}

func (x *ForwardMsg) Reset() {
	*x = ForwardMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wcf_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForwardMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMsg) ProtoMessage() {}

func (x *ForwardMsg) ProtoReflect() protoreflect.Message {
	mi := &file_wcf_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMsg.ProtoReflect.Descriptor instead.
func (*ForwardMsg) Descriptor() ([]byte, []int) {
	return file_wcf_proto_rawDescGZIP(), []int{27}
}

func (x *ForwardMsg) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ForwardMsg) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

var File_wcf_proto protoreflect.FileDescriptor

var file_wcf_proto_rawDesc = []byte{
	0x0a, 0x09, 0x77, 0x63, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x77, 0x63, 0x66,
	0x22, 0xd4, 0x04, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x04,
	0x66, 0x75, 0x6e, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x77, 0x63, 0x66,
	0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x04, 0x66, 0x75, 0x6e, 0x63,
	0x12, 0x22, 0x0a, 0x05, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x05, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x03, 0x73, 0x74, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x03, 0x73, 0x74, 0x72, 0x12, 0x20, 0x0a, 0x03, 0x74, 0x78, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x4d, 0x73, 0x67, 0x48, 0x00, 0x52, 0x03, 0x74, 0x78, 0x74, 0x12, 0x22, 0x0a, 0x04, 0x66, 0x69,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x50,
	0x61, 0x74, 0x68, 0x4d, 0x73, 0x67, 0x48, 0x00, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x24,
	0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x77, 0x63, 0x66, 0x2e, 0x44, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x00, 0x52, 0x05, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x01, 0x76, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x01, 0x76, 0x12, 0x1f, 0x0a, 0x01, 0x6d, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d,
	0x67, 0x6d, 0x74, 0x48, 0x00, 0x52, 0x01, 0x6d, 0x12, 0x1f, 0x0a, 0x03, 0x78, 0x6d, 0x6c, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x58, 0x6d, 0x6c, 0x4d,
	0x73, 0x67, 0x48, 0x00, 0x52, 0x03, 0x78, 0x6d, 0x6c, 0x12, 0x20, 0x0a, 0x03, 0x64, 0x65, 0x63,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x44, 0x65, 0x63,
	0x50, 0x61, 0x74, 0x68, 0x48, 0x00, 0x52, 0x03, 0x64, 0x65, 0x63, 0x12, 0x1f, 0x0a, 0x02, 0x74,
	0x66, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x48, 0x00, 0x52, 0x02, 0x74, 0x66, 0x12, 0x14, 0x0a, 0x04,
	0x75, 0x69, 0x36, 0x34, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x04, 0x75, 0x69,
	0x36, 0x34, 0x12, 0x14, 0x0a, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x03, 0x61, 0x74, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x4d, 0x73, 0x67, 0x48, 0x00, 0x52, 0x03, 0x61, 0x74, 0x74, 0x12, 0x1f, 0x0a, 0x02,
	0x61, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x41,
	0x75, 0x64, 0x69, 0x6f, 0x4d, 0x73, 0x67, 0x48, 0x00, 0x52, 0x02, 0x61, 0x6d, 0x12, 0x1f, 0x0a,
	0x02, 0x72, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x77, 0x63, 0x66, 0x2e,
	0x52, 0x69, 0x63, 0x68, 0x54, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52, 0x02, 0x72, 0x74, 0x12, 0x1d,
	0x0a, 0x02, 0x70, 0x6d, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x77, 0x63, 0x66,
	0x2e, 0x50, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x48, 0x00, 0x52, 0x02, 0x70, 0x6d, 0x12, 0x21, 0x0a,
	0x02, 0x66, 0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x77, 0x63, 0x66, 0x2e,
	0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x73, 0x67, 0x48, 0x00, 0x52, 0x02, 0x66, 0x6d,
	0x42, 0x05, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x8e, 0x03, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x66, 0x75, 0x6e, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x04, 0x66, 0x75, 0x6e, 0x63, 0x12, 0x18, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x12, 0x0a, 0x03, 0x73, 0x74, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x03, 0x73, 0x74, 0x72, 0x12, 0x22, 0x0a, 0x05, 0x77, 0x78, 0x6d, 0x73, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x57, 0x78, 0x4d, 0x73,
	0x67, 0x48, 0x00, 0x52, 0x05, 0x77, 0x78, 0x6d, 0x73, 0x67, 0x12, 0x25, 0x0a, 0x05, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x77, 0x63, 0x66, 0x2e,
	0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x48, 0x00, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x2e, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x52, 0x70, 0x63, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x73, 0x48, 0x00, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x73, 0x12, 0x20, 0x0a, 0x03, 0x64, 0x62, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x77, 0x63, 0x66, 0x2e, 0x44, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x48, 0x00, 0x52, 0x03,
	0x64, 0x62, 0x73, 0x12, 0x27, 0x0a, 0x06, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x44, 0x62, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x48, 0x00, 0x52, 0x06, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x04,
	0x72, 0x6f, 0x77, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x77, 0x63, 0x66,
	0x2e, 0x44, 0x62, 0x52, 0x6f, 0x77, 0x73, 0x48, 0x00, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x12,
	0x1f, 0x0a, 0x02, 0x75, 0x69, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x77, 0x63,
	0x66, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x02, 0x75, 0x69,
	0x12, 0x1f, 0x0a, 0x03, 0x6f, 0x63, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x77, 0x63, 0x66, 0x2e, 0x4f, 0x63, 0x72, 0x4d, 0x73, 0x67, 0x48, 0x00, 0x52, 0x03, 0x6f, 0x63,
	0x72, 0x42, 0x05, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x8b, 0x02, 0x0a, 0x05, 0x57, 0x78, 0x4d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x69,
	0x73, 0x5f, 0x73, 0x65, 0x6c, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73,
	0x53, 0x65, 0x6c, 0x66, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x02, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x67,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x10, 0x0a,
	0x03, 0x78, 0x6d, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x78, 0x6d, 0x6c, 0x22,
	0x4d, 0x0a, 0x07, 0x54, 0x65, 0x78, 0x74, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x65, 0x72, 0x73, 0x22, 0x39,
	0x0a, 0x07, 0x50, 0x61, 0x74, 0x68, 0x4d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x22, 0x66, 0x0a, 0x06, 0x58, 0x6d, 0x6c,
	0x4d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x22, 0x74, 0x0a, 0x08, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2e, 0x0a,
	0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77,
	0x63, 0x66, 0x2e, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x38, 0x0a,
	0x0a, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc2, 0x01, 0x0a, 0x0a, 0x52, 0x70, 0x63, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x78, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x77, 0x78, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x3a, 0x0a, 0x0b,
	0x52, 0x70, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x08, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x77, 0x63, 0x66, 0x2e, 0x52, 0x70, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x22, 0x1f, 0x0a, 0x07, 0x44, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x2f, 0x0a, 0x07, 0x44, 0x62, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x71, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x71, 0x6c, 0x22, 0x30, 0x0a, 0x08, 0x44, 0x62,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x06, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x44, 0x62, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x06, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x22, 0x2b, 0x0a, 0x07,
	0x44, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x71, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x71, 0x6c, 0x22, 0x4f, 0x0a, 0x07, 0x44, 0x62, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x2d, 0x0a, 0x05, 0x44, 0x62,
	0x52, 0x6f, 0x77, 0x12, 0x24, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x44, 0x62, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x28, 0x0a, 0x06, 0x44, 0x62, 0x52,
	0x6f, 0x77, 0x73, 0x12, 0x1e, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0a, 0x2e, 0x77, 0x63, 0x66, 0x2e, 0x44, 0x62, 0x52, 0x6f, 0x77, 0x52, 0x04, 0x72,
	0x6f, 0x77, 0x73, 0x22, 0x44, 0x0a, 0x0c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x76, 0x33, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x76, 0x33, 0x12, 0x0e, 0x0a, 0x02, 0x76, 0x34, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x76, 0x34, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x3a, 0x0a, 0x0a, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x4d, 0x67, 0x6d, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x77, 0x78, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x77, 0x78, 0x69, 0x64, 0x73, 0x22, 0x5e, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x78, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x77, 0x78, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x6f, 0x6d, 0x65, 0x22, 0x2d, 0x0a, 0x07, 0x44, 0x65, 0x63, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x72, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73,
	0x72, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x64, 0x73, 0x74, 0x22, 0x46, 0x0a, 0x08, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x77, 0x78, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x77, 0x78, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x66, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x66, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x69, 0x64, 0x22, 0x47, 0x0a, 0x09,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x4d, 0x73, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x68, 0x75,
	0x6d, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x2c, 0x0a, 0x08, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x4d, 0x73,
	0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x64, 0x69, 0x72, 0x22, 0xb0, 0x01, 0x0a, 0x08, 0x52, 0x69, 0x63, 0x68, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a,
	0x0a, 0x08, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x22, 0x34, 0x0a, 0x06, 0x50, 0x61, 0x74, 0x4d, 0x73, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x78, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x77, 0x78, 0x69, 0x64, 0x22, 0x38, 0x0a, 0x06,
	0x4f, 0x63, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x38, 0x0a, 0x0a, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72,
	0x64, 0x4d, 0x73, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x2a, 0xd9, 0x05, 0x0a, 0x09, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x11,
	0x0a, 0x0d, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x49, 0x53, 0x5f, 0x4c, 0x4f, 0x47,
	0x49, 0x4e, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x57, 0x58, 0x49, 0x44, 0x10, 0x10, 0x12, 0x16, 0x0a, 0x12,
	0x46, 0x55, 0x4e, 0x43, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x53, 0x10, 0x11, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x53, 0x10, 0x12, 0x12, 0x15, 0x0a, 0x11, 0x46,
	0x55, 0x4e, 0x43, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x44, 0x42, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x53,
	0x10, 0x13, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x44,
	0x42, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x53, 0x10, 0x14, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x55,
	0x4e, 0x43, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x10, 0x15, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x41,
	0x55, 0x44, 0x49, 0x4f, 0x5f, 0x4d, 0x53, 0x47, 0x10, 0x16, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x55,
	0x4e, 0x43, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x54, 0x58, 0x54, 0x10, 0x20, 0x12, 0x11, 0x0a,
	0x0d, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x49, 0x4d, 0x47, 0x10, 0x21,
	0x12, 0x12, 0x0a, 0x0e, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x10, 0x22, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x53, 0x45, 0x4e,
	0x44, 0x5f, 0x58, 0x4d, 0x4c, 0x10, 0x23, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x55, 0x4e, 0x43, 0x5f,
	0x53, 0x45, 0x4e, 0x44, 0x5f, 0x45, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x24, 0x12, 0x16,
	0x0a, 0x12, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x52, 0x49, 0x43, 0x48,
	0x5f, 0x54, 0x58, 0x54, 0x10, 0x25, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x53,
	0x45, 0x4e, 0x44, 0x5f, 0x50, 0x41, 0x54, 0x5f, 0x4d, 0x53, 0x47, 0x10, 0x26, 0x12, 0x14, 0x0a,
	0x10, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x53,
	0x47, 0x10, 0x27, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x56, 0x5f, 0x54, 0x58, 0x54, 0x10, 0x30, 0x12, 0x19, 0x0a,
	0x15, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x52, 0x45,
	0x43, 0x56, 0x5f, 0x54, 0x58, 0x54, 0x10, 0x40, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x55, 0x4e, 0x43,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x5f, 0x44, 0x42, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x10, 0x50,
	0x12, 0x16, 0x0a, 0x12, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x5f,
	0x46, 0x52, 0x49, 0x45, 0x4e, 0x44, 0x10, 0x51, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x55, 0x4e, 0x43,
	0x5f, 0x52, 0x45, 0x43, 0x56, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0x52,
	0x12, 0x14, 0x0a, 0x10, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x5f, 0x50, 0x59, 0x51, 0x10, 0x53, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x44,
	0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x10, 0x54,
	0x12, 0x19, 0x0a, 0x15, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x41, 0x43, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x55, 0x12, 0x13, 0x0a, 0x0f, 0x46,
	0x55, 0x4e, 0x43, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x4d, 0x53, 0x47, 0x10, 0x56,
	0x12, 0x16, 0x0a, 0x12, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x44, 0x45, 0x43, 0x52, 0x59, 0x50, 0x54,
	0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x60, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x55, 0x4e, 0x43,
	0x5f, 0x45, 0x58, 0x45, 0x43, 0x5f, 0x4f, 0x43, 0x52, 0x10, 0x61, 0x12, 0x19, 0x0a, 0x15, 0x46,
	0x55, 0x4e, 0x43, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x4d, 0x45, 0x4d,
	0x42, 0x45, 0x52, 0x53, 0x10, 0x70, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x44,
	0x45, 0x4c, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x10,
	0x71, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x49, 0x4e, 0x56, 0x5f, 0x52, 0x4f,
	0x4f, 0x4d, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x10, 0x72, 0x42, 0x15, 0x0a, 0x0b,
	0x63, 0x6f, 0x6d, 0x2e, 0x69, 0x61, 0x6d, 0x74, 0x65, 0x65, 0x72, 0x5a, 0x06, 0x2e, 0x2e, 0x2f,
	0x77, 0x63, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_wcf_proto_rawDescOnce sync.Once
	file_wcf_proto_rawDescData = file_wcf_proto_rawDesc
)

func file_wcf_proto_rawDescGZIP() []byte {
	file_wcf_proto_rawDescOnce.Do(func() {
		file_wcf_proto_rawDescData = protoimpl.X.CompressGZIP(file_wcf_proto_rawDescData)
	})
	return file_wcf_proto_rawDescData
}

var file_wcf_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_wcf_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_wcf_proto_goTypes = []interface{}{
	(Functions)(0),       // 0: wcf.Functions
	(*Request)(nil),      // 1: wcf.Request
	(*Response)(nil),     // 2: wcf.Response
	(*Empty)(nil),        // 3: wcf.Empty
	(*WxMsg)(nil),        // 4: wcf.WxMsg
	(*TextMsg)(nil),      // 5: wcf.TextMsg
	(*PathMsg)(nil),      // 6: wcf.PathMsg
	(*XmlMsg)(nil),       // 7: wcf.XmlMsg
	(*MsgTypes)(nil),     // 8: wcf.MsgTypes
	(*RpcContact)(nil),   // 9: wcf.RpcContact
	(*RpcContacts)(nil),  // 10: wcf.RpcContacts
	(*DbNames)(nil),      // 11: wcf.DbNames
	(*DbTable)(nil),      // 12: wcf.DbTable
	(*DbTables)(nil),     // 13: wcf.DbTables
	(*DbQuery)(nil),      // 14: wcf.DbQuery
	(*DbField)(nil),      // 15: wcf.DbField
	(*DbRow)(nil),        // 16: wcf.DbRow
	(*DbRows)(nil),       // 17: wcf.DbRows
	(*Verification)(nil), // 18: wcf.Verification
	(*MemberMgmt)(nil),   // 19: wcf.MemberMgmt
	(*UserInfo)(nil),     // 20: wcf.UserInfo
	(*DecPath)(nil),      // 21: wcf.DecPath
	(*Transfer)(nil),     // 22: wcf.Transfer
	(*AttachMsg)(nil),    // 23: wcf.AttachMsg
	(*AudioMsg)(nil),     // 24: wcf.AudioMsg
	(*RichText)(nil),     // 25: wcf.RichText
	(*PatMsg)(nil),       // 26: wcf.PatMsg
	(*OcrMsg)(nil),       // 27: wcf.OcrMsg
	(*ForwardMsg)(nil),   // 28: wcf.ForwardMsg
	nil,                  // 29: wcf.MsgTypes.TypesEntry
}
var file_wcf_proto_depIdxs = []int32{
	0,  // 0: wcf.Request.func:type_name -> wcf.Functions
	3,  // 1: wcf.Request.empty:type_name -> wcf.Empty
	5,  // 2: wcf.Request.txt:type_name -> wcf.TextMsg
	6,  // 3: wcf.Request.file:type_name -> wcf.PathMsg
	14, // 4: wcf.Request.query:type_name -> wcf.DbQuery
	18, // 5: wcf.Request.v:type_name -> wcf.Verification
	19, // 6: wcf.Request.m:type_name -> wcf.MemberMgmt
	7,  // 7: wcf.Request.xml:type_name -> wcf.XmlMsg
	21, // 8: wcf.Request.dec:type_name -> wcf.DecPath
	22, // 9: wcf.Request.tf:type_name -> wcf.Transfer
	23, // 10: wcf.Request.att:type_name -> wcf.AttachMsg
	24, // 11: wcf.Request.am:type_name -> wcf.AudioMsg
	25, // 12: wcf.Request.rt:type_name -> wcf.RichText
	26, // 13: wcf.Request.pm:type_name -> wcf.PatMsg
	28, // 14: wcf.Request.fm:type_name -> wcf.ForwardMsg
	0,  // 15: wcf.Response.func:type_name -> wcf.Functions
	4,  // 16: wcf.Response.wxmsg:type_name -> wcf.WxMsg
	8,  // 17: wcf.Response.types:type_name -> wcf.MsgTypes
	10, // 18: wcf.Response.contacts:type_name -> wcf.RpcContacts
	11, // 19: wcf.Response.dbs:type_name -> wcf.DbNames
	13, // 20: wcf.Response.tables:type_name -> wcf.DbTables
	17, // 21: wcf.Response.rows:type_name -> wcf.DbRows
	20, // 22: wcf.Response.ui:type_name -> wcf.UserInfo
	27, // 23: wcf.Response.ocr:type_name -> wcf.OcrMsg
	29, // 24: wcf.MsgTypes.types:type_name -> wcf.MsgTypes.TypesEntry
	9,  // 25: wcf.RpcContacts.contacts:type_name -> wcf.RpcContact
	12, // 26: wcf.DbTables.tables:type_name -> wcf.DbTable
	15, // 27: wcf.DbRow.fields:type_name -> wcf.DbField
	16, // 28: wcf.DbRows.rows:type_name -> wcf.DbRow
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_wcf_proto_init() }
func file_wcf_proto_init() {
	if File_wcf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_wcf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PathMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*XmlMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RpcContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RpcContacts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DbNames); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DbTable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DbTables); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DbQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DbField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DbRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DbRows); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Verification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberMgmt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DecPath); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Transfer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttachMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudioMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RichText); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PatMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OcrMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wcf_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForwardMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_wcf_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Request_Empty)(nil),
		(*Request_Str)(nil),
		(*Request_Txt)(nil),
		(*Request_File)(nil),
		(*Request_Query)(nil),
		(*Request_V)(nil),
		(*Request_M)(nil),
		(*Request_Xml)(nil),
		(*Request_Dec)(nil),
		(*Request_Tf)(nil),
		(*Request_Ui64)(nil),
		(*Request_Flag)(nil),
		(*Request_Att)(nil),
		(*Request_Am)(nil),
		(*Request_Rt)(nil),
		(*Request_Pm)(nil),
		(*Request_Fm)(nil),
	}
	file_wcf_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Response_Status)(nil),
		(*Response_Str)(nil),
		(*Response_Wxmsg)(nil),
		(*Response_Types)(nil),
		(*Response_Contacts)(nil),
		(*Response_Dbs)(nil),
		(*Response_Tables)(nil),
		(*Response_Rows)(nil),
		(*Response_Ui)(nil),
		(*Response_Ocr)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_wcf_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wcf_proto_goTypes,
		DependencyIndexes: file_wcf_proto_depIdxs,
		EnumInfos:         file_wcf_proto_enumTypes,
		MessageInfos:      file_wcf_proto_msgTypes,
	}.Build()
	File_wcf_proto = out.File
	file_wcf_proto_rawDesc = nil
	file_wcf_proto_goTypes = nil
	file_wcf_proto_depIdxs = nil
}
