'use strict';

const client = require('./client.cjs');
const sdk = require('./sdk.cjs');
const wcf = require('./proto/wcf.cjs');
const roomData = require('./proto/room-data.cjs');
const bytesExtra = require('./proto/bytes-extra.cjs');
const types = require('./types.cjs');
const utils = require('./utils.cjs');



exports.Wechatferry = client.Wechatferry;
exports.resolvedWechatferryOptions = client.resolvedWechatferryOptions;
exports.WechatferrySDK = sdk.WechatferrySDK;
exports.resolvedWechatferrySDKOptions = sdk.resolvedWechatferrySDKOptions;
Object.defineProperty(exports, 'wcf', {
	enumerable: true,
	get: function () { return wcf.wcf; }
});
Object.defineProperty(exports, 'RoomData', {
	enumerable: true,
	get: function () { return roomData.RoomData; }
});
Object.defineProperty(exports, 'BytesExtra', {
	enumerable: true,
	get: function () { return bytesExtra.BytesExtra; }
});
exports.WechatAppMessageType = types.WechatAppMessageType;
exports.WechatMessageType = types.WechatMessageType;
exports.parseDbField = utils.parseDbField;
exports.saveFileBox = utils.saveFileBox;
exports.uint8Array2str = utils.uint8Array2str;
