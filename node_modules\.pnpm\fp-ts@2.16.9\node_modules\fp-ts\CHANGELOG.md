# Changelog

> **Tags:**
>
> - [New Feature]
> - [Bug Fix]
> - [Breaking Change]
> - [Documentation]
> - [Internal]
> - [Polish]
> - [Experimental]
> - [Deprecation]

**Note**: Gaps between patch versions are faulty/broken releases.

**Note**: A feature tagged as <PERSON> is in a
high state of flux, you're at risk of it changing without notice.

# 2.16.9

**Polish**

Support `strictBuiltinIteratorReturn`, #1949

# 2.16.8

**Polish**

Remove useless pure comment, closes #1937

# 2.16.7

**Polish**

Add tap for Identity #1943

# 2.16.6

**Polish**

Allow dependencies to be widen when using orLeft #1938

# 2.16.5

**Polish**

Resolved a RangeError where the maximum call stack size was exceeded when invoking `chainWithIndex`, #1931

# 2.16.4

**Polish**

Fix `__PURE__` annotation placement, closes #1926

# 2.16.3

**Polish**

`Either.ts`: `toError`: Don't fail when a value can't be converted to a primitive, #1924

# 2.16.2

**Polish**

Fix `"__PURE__"` annotation placement #1919, closes #1916

# 2.16.1

**Bug Fix**

- fix type signature of `ReaderTaskEither.tapReaderIO`, #1895

# 2.16.0

- `tapEither` (dual) (aka `chainFirstEitherK` / `chainFirstEitherKW`) https://github.com/gcanti/fp-ts/pull/1864
- `tapIO` (dual) (aka `chainFirstIOK`) https://github.com/gcanti/fp-ts/pull/1865
- `as` / `asUnit` https://github.com/gcanti/fp-ts/pull/1867
- `tapTask` (dual) (aka `chainFirstTaskK`) https://github.com/gcanti/fp-ts/pull/1869
- `mapError` (dual) (aka `mapLeft`) https://github.com/gcanti/fp-ts/pull/1870
- `mapBoth` (dual) (aka `bimap`) https://github.com/gcanti/fp-ts/pull/1872
- `tapReader` (dual) (aka `chainFirstReaderK` / `chainFirstIReaderKW`) https://github.com/gcanti/fp-ts/pull/1871
- `tapReaderEither` (dual) (aka `chainFirstReaderEitherK` / `chainFirstEitherKW`) https://github.com/gcanti/fp-ts/pull/1873
- `tapReaderIO` (dual) (aka `chainFirstReaderIOK`) https://github.com/gcanti/fp-ts/pull/1873
- `tapReaderTask` (dual) (aka `chainFirstReaderTaskK`) https://github.com/gcanti/fp-ts/pull/1873
- `tapTaskEither` (dual) (aka `chainFirstTaskEitherK` / `chainFirstTaskEitherKW`) https://github.com/gcanti/fp-ts/pull/1873
- `flatMapReaderTask` (dual) (aka `chainReaderTaskK` / `chainReaderTaskKW`) https://github.com/gcanti/fp-ts/pull/1874
- `flatMapTaskEither` (dual) (aka `chainTaskEitherK` / `chainTaskEitherKW`) https://github.com/gcanti/fp-ts/pull/1874
- `flatMapIO` (dual) (aka `chainIOK`) https://github.com/gcanti/fp-ts/pull/1874
- `Option.orElse` (dual) (aka `alt` / `altW`) https://github.com/gcanti/fp-ts/pull/1868
- `flatMapTask` (dual) (aka `chainTaskK`) https://github.com/gcanti/fp-ts/pull/1876
- `flatMapReader` (dual) (aka `chainReaderK` / `chainReaderKW`) https://github.com/gcanti/fp-ts/pull/1876
- `flatMapReaderIO` (dual) (aka `chainReaderIOK` / `chainReaderIOKW`) https://github.com/gcanti/fp-ts/pull/1879
- `flatMap*` (aka `chain*K` / `chain*KW`) https://github.com/gcanti/fp-ts/pull/1880

# 2.15.0

**New Feature**

- `function`
  - add `LazyArg`
- add `tap` to
  - `Either`
  - `IO`
  - `IOEither`
  - `IOOption`
  - `Option`
  - `Reader`
  - `ReaderEither`
  - `ReaderIO`
  - `ReaderTask`
  - `ReaderTaskEither`
  - `State`
  - `StateReaderTaskEither`
  - `Task`
  - `TaskEither`
  - `TaskOption`
- add `flatMapNullable` (dual) to
  - `Either`
  - `IOEither`
  - `ReaderEither`
  - `ReaderTaskEither`
  - `StateReaderTaskEither`
  - `TaskEither`
- add `flatMapOption` (dual) to
  - `Either`
  - `IOEither`
  - `ReaderEither`
  - `ReaderTaskEither`
  - `StateReaderTaskEither`
  - `TaskEither`
- add `liftNullable` to
  - `Either`
  - `IOEither`
  - `ReaderEither`
  - `ReaderTaskEither`
  - `StateReaderTaskEither`
  - `TaskEither`
- add `liftOption` to
  - `Either`
  - `IOEither`
  - `ReaderEither`
  - `ReaderTaskEither`
  - `StateReaderTaskEither`
  - `TaskEither`
- add `flatMapEither` (dual) to
  - `IOEither`
  - `ReaderEither`
  - `ReaderTaskEither`
  - `TaskEither`
- `Array`
  - add index to `flatMap`
- `NonEmptyArray`
  - add index to `flatMap`
- `ReadonlyArray`
  - add index to `flatMap`
- `ReadonlyNonEmptyArray`
  - add index to `flatMap`

# 2.14.0

**New Feature**

- add `flatMap` to
  - `Either`
  - `IOEither`
  - `Reader`
  - `ReaderEither`
  - `ReaderIO`
  - `ReaderTask`
  - `ReaderTaskEither`
  - `StateReaderTaskEither`
  - `TaskEither`
  - `NonEmptyArray`
  - `ReadonlyNonEmptyArray`
  - `Tree`
  - `Array`
  - `Identity`
  - `IO`
  - `IOOption`
  - `Option`
  - `ReadonlyArray`
  - `State`
  - `Task`
  - `TaskOption`

# 2.13.2

- add `chainOptionKW`, #1846 (@DenisFrezzato)

# 2.13.1

- **New Feature**
  - new modules:
    - `ReaderIO`, #1738 (@sukovanej)
  - do notation:
    - add `let` helpers, #1725 (@Punie)
  - `Alternative`
    - add `getAlternativeMonoid`, #1717 (@willheslam)
  - `function`
    - `flip` is now curried, #1748 (@thewilkybarkid)
  - `pipeable`
    - add pipeable helpers, #1764 (@gcanti)
      - `alt`
      - `ap`
      - `bimap`
      - `chain`
      - `compose`
      - `contramap`
      - `extend`
      - `filter`
      - `filterMap`
      - `filterMapWithIndex`
      - `filterWithIndex`
      - `foldMap`
      - `foldMapWithIndex`
      - `map`
      - `mapLeft`
      - `mapWithIndex`
      - `partition`
      - `partitionMap`
      - `partitionMapWithIndex`
      - `partitionWithIndex`
      - `promap`
      - `reduce`
      - `reduceRight`
      - `reduceRightWithIndex`
      - `reduceWithIndex`
  - `ReaderTask`
    - add `ReaderIO` constructors/combinators, #1773 (@thewilkybarkid)
  - `ReaderTaskEither`
    - add `ReaderIO` constructors/combinators, #1774 (@thewilkybarkid)
  - `TaskEither`
    - add `chainTaskOptionKW`, #1744 (@AmirabbasJ)
- **Polish**
  - backport from v3 some handy defaults defined in natural transformations/combinators/constructors (@gcanti)
  - fix wrong type parameter order:
    - `FromIO`
      - `chainIOK`
    - `FromTask`
      - `chainTaskK`
      - `chainFirstTaskK`
    - `Whiterable`
      - `FilterE1`
    - `TheseT`
      - `both`
    - `Either`
      - `apFirstW`
      - `apSecondW`
    - `IOEither`
      - `apFirstW`
      - `apSecondW`
    - `Reader`
      - `apFirstW`
      - `apSecondW`
    - `ReaderEither`
      - `apFirstW`
      - `apSecondW`
    - `ReaderTaskEither`
      - `apFirstW`
      - `apSecondW`
    - `StateReaderTaskEither`
      - `apFirstW`
      - `apSecondW`
    - `TaskEither`
      - `apFirstW`
      - `apSecondW`
  - `Apply`
    - add `ap` overloadings, #1721 (@waynevanson)
  - `Either`
    - remove useless type parameter in `exists` (@gcanti)
  - `ReadonlyRecord`
    - fix `fromEntries` return type, closes #1745 (@gcanti)
  - `TaskEither`
    - fix `fromPredicate` signature (@gcanti)
  - `These`
    - add missing `fromPredicate` (@gcanti)
    - remove useless type parameter in `exists` (@gcanti)
  - `Traversable`
    - add more overloadings to traverse / sequence helpers, #1758 (@gcanti)
  - `Writer`
    - `getChain` requires a `Semigroup` instead of a `Monoid` (@gcanti)
- **Deprecation**
  - `function`
    - deprecate uncurried `flip` function, #1748 (@thewilkybarkid)

# 2.12.2

- **Polish**
  - `ReadonlyRecord` / `Record`
    - add `Applicative4` overloadings to `traverse` / `traverseWithIndex`, #1733 (@mlegenhausen)

# 2.12.1

- **Bug Fix**
  - `ReadonlyRecord`
    - fix `traverseWithIndex` implementation, #1704 (@waynevanson)

# 2.12.0

- **New Feature**
  - add `IOOption` module (@gcanti)
  - `Array`
    - add `intercalate`, #1678 (@thewilkybarkid)
  - `Bounded`
    - add `clamp` (@gcanti)
    - add `reverse` (@gcanti)
  - `Either`
    - add `apFirstW`, #1564 (@DenisFrezzato)
    - add `apSecondW`, #1564 (@DenisFrezzato)
  - `EitherT`
    - add `chainNullableK`, #1619 (@cwstra)
    - add `fromNullable`, #1619 (@cwstra)
    - add `fromNullableK`, #1619 (@cwstra)
  - `FromEither`
    - add `chainFirstEitherK` (@gcanti)
  - `IOEither`
    - add `apFirstW`, #1564 (@DenisFrezzato)
    - add `apSecondW`, #1564 (@DenisFrezzato)
    - add `chainFirstEitherK` (@gcanti)
    - add `chainFirstEitherKW` (@gcanti)
    - add `orElseFirstIOK`, #1655 (@thewilkybarkid)
    - add `bracketW`, #1627 (@thomasvargiu)
  - `NonEmptyArray`
    - add `intercalate`, #1678 (@thewilkybarkid)
  - `Option`
    - add `chainFirstEitherK` (@gcanti)
  - `Ordering`
    - add `matchW`, #1535 (@mlegenhausen)
  - `Reader`
    - add `apFirstW`, #1564 (@DenisFrezzato)
    - add `apSecondW`, #1564 (@DenisFrezzato)
  - `ReaderEither`
    - add `apFirstW`, #1564 (@DenisFrezzato)
    - add `apSecondW`, #1564 (@DenisFrezzato)
    - add `chainFirstEitherK` (@gcanti)
    - add `chainFirstEitherKW` (@gcanti)
  - `ReaderTaskEither`
    - add `apFirstW`, #1564 (@DenisFrezzato)
    - add `apSecondW`, #1564 (@DenisFrezzato)
    - add `chainFirstEitherK`, #1562 (@DenisFrezzato)
    - add `chainFirstEitherKW`, #1562 (@DenisFrezzato)
    - add `bracketW`, #1627 (@thomasvargiu)
    - add `chainNullableK` (@gcanti)
    - add `fromNullable` (@gcanti)
    - add `fromNullableK` (@gcanti)
  - `ReadonlyArray`
    - add `intercalate`, #1678 (@thewilkybarkid)
  - `ReadonlyNonEmptyArray`
    - add `intercalate`, #1678 (@thewilkybarkid)
  - `ReadonlyRecord`
    - add `toEntries`, #1552 (@bravely)
    - add `fromEntries`, #1552 (@bravely)
  - `Record`
    - add `toEntries`, #1552 (@bravely)
    - add `fromEntries`, #1552 (@bravely)
  - `StateReaderTaskEither`
    - add `apFirstW`, #1564 (@DenisFrezzato)
    - add `apSecondW`, #1564 (@DenisFrezzato)
    - add `chainFirstEitherK` (@gcanti)
    - add `chainFirstEitherKW` (@gcanti)
  - `TaskEither`
    - add `apFirstW`, #1564 (@DenisFrezzato)
    - add `apSecondW`, #1564 (@DenisFrezzato)
    - add `chainFirstEitherK`, #1659 (@vinassefranche)
    - add `chainFirstEitherKW`, #1659 (@vinassefranche)
    - add `orElseFirstIOK`, #1655 (@thewilkybarkid)
    - add `orElseFirstTaskK`, #1655 (@thewilkybarkid)
    - add `bracketW`, #1627 (@thomasvargiu)
    - add `chainNullableK`, #1619 (@cwstra)
    - add `fromNullable`, #1619 (@cwstra)
    - add `fromNullableK`, #1619 (@cwstra)
  - `TaskOption`
    - add `fromEitherK` (@gcanti)
    - add `chainEitherK` (@gcanti)
    - add `chainFirstEitherK` (@gcanti)

# 2.11.10

- **Polish**
  - `TaskEither` / `TaskOption`
    - now `tryCatch` / `tryCatchK` will catch synchronous errors too, #1676 (@thewilkybarkid)

# 2.11.9

- **Polish**
  - add `Refinement` overload to 'every' functions, #1681 (@mlegenhausen)

# 2.11.8

- **Polish**
  - Add missing pure annotations, #1658 (@OliverJAsh)

# 2.11.7

- **Polish**
  - more `Functor.map` overloadings, closes #1623 (@gcanti)

# 2.11.6

- **Polish**
  - `ReaderEither`
    - `orElseFirstW`: intersect dependencies like `orElseW` (@gcanti)
  - `ReaderTaskEither`
    - `orElseFirstW`: intersect dependencies like `orElseW`, #1635 (@OliverJAsh)

# 2.11.5

- **Polish**
  - more curried `elem` overloads, #1602 (@ammut)

# 2.11.4

- **Polish**
  - fix `chainNullableK` signature, #1589 (@bwlt)
  - stack safety for `Task` via microtasks suspension, #1591 (@mikearnaldi)

# 2.11.3

- **Polish**
  - pipe: fix v8 performance cliff for >10 case stmts, #1585 (@pbadenski)

# 2.11.2

- **Polish**
  - Add/fix pure comments, #1555 (@OliverJAsh)

# 2.11.1

- **Deprecation**
  - `Array`
    - deprecate `range`, use `NonEmptyArray` module instead.
  - `function`
    - deprecate `Endomorphism`, use `Endomorphism` module instead.
    - deprecate `getEndomorphismMonoid`, use `Endomorphism` module instead.
    - deprecate `Predicate`, use `Predicate` module instead.
    - deprecate `not`, use `Predicate` module instead.
    - deprecate `Refinement`, use `Refinement` module instead.
  - `Monoid`
    - deprecate `monoidVoid`, use `void` module instead.
  - `NonEmptyArray`
    - deprecate `groupSort` (it's just `sort` followed by `group`)
  - `Option`
    - deprecate `getRefinement`, use `Refinement` module instead.
    - deprecate `getFirstMonoid`, use `getMonoid` module instead.
    - deprecate `getLastMonoid`, use `getMonoid` module instead.
  - `ReadonlyArray`
    - deprecate `range`, use `ReadonlyNonEmptyArray` module instead.
  - `ReadonlyNonEmptyArray`
    - deprecate `groupSort` (it's just `sort` followed by `group`)
  - `Record` / `ReadonlyRecord`: deprecate overloads without `Ord` constraint (@anthonyjoeseph):
    - `collect`
    - `reduce`
    - `foldMap`
    - `reduceRight`
    - `reduceWithIndex`
    - `foldMapWithIndex`
    - `reduceRightWithIndex`
    - `getShow`
    - deprecate `Foldable` in favour of `getFoldable` (@anthonyjoeseph)
    - deprecate `FoldableWithIndex` in favour of `getFoldableWithIndex` (@anthonyjoeseph)
    - deprecate `Traversable` in favour of `getTraversable` (@anthonyjoeseph)
    - deprecate `TraversableWithIndex` in favour of `getTraversableWithIndex` (@anthonyjoeseph)
    - deprecate `Witherable` in favour of `getWitherable` (@anthonyjoeseph)
  - `Semigroup`
    - deprecate `semigroupVoid`, use `void` module instead.
- **New Feature**
  - add `Endomorphism` module
  - add `Predicate` module
  - add `Refinement` module
  - add `FromState` module
  - add `FromThese` module
  - add `void` module
  - add `FromReader` module
  - add `NaturalTransformation` module
  - add `Zero` module
  - `Alt`
    - add `altAll`
  - `Alternative`
    - add `altAll`
  - `Array`
    - add `prependW`, `appendW` (@thewilkybarkid)
    - add `fromOption`, `fromPredicate` (@cdimitroulas)
    - add `filterE`
    - add `ChainRecDepthFirst` instance (@qlonik)
    - add `chainRecDepthFirst`
    - add `ChainRecBreadthFirst` instance (@qlonik)
    - add `chainRecBreadthFirst`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
    - add `fromEither`
    - add `FromEither` instance
    - add `fromEitherK`
    - make `isEmpty` a user defined guard
    - add `concat` / `concatW`
    - add `match`, `matchW`, `matchLeftW`, `matchRightW`
    - add `fromOptionK`
    - add `Zero` instance
    - add `guard` constructor
    - add `exists` alias
  - `boolean`
    - add `isBoolean`
  - `Either`
    - add `chainOptionK`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `EitherT`
    - add `orElseFirst`
    - add `orLeft`
  - `function`
    - add `SK` (@cdimitroulas)
    - add `apply`
  - `IO`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `IOEither`
    - add `orElseFirst` / `orElseFirstW`
    - add `orLeft`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `Magma`
    - add `reverse`
    - add `filterFirst`
    - add `filterSecond`
    - add `endo`
    - add `concatAll`
  - `Map`
    - add `union`
    - add `intersection`
    - add `difference`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
    - add `getFoldable`
    - add `foldMap`
    - add `reduceRight`
    - add `reduceWithIndex`
    - add `foldMapWithIndex`
    - add `reduceRightWithIndex`
  - `NonEmptyArray`
    - add `matchLeft`, `matchRight`, `modifyHead`, `modifyLast` (@cdimitroulas)
    - add `union`
    - add `getUnionSemigroup`
    - add `makeBy`
    - add `range`
    - make `concat` pipeable
  - `number`
    - add `MagmaSub`
    - add `isNumber`
  - `string`
    - add `isString`
  - `Option`
    - add `FromEither` instance
    - add `fromEitherK`
    - add `chainEitherK`
    - add `Zero` instance
    - add `guard` constructor
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `Ord`
    - add `trivial` instance
    - add `equals`
  - `Reader`
    - add `asksReaderW`, `asksReader`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `ReaderEither`
    - add `asksReaderEitherW`, `asksReaderEither`
    - add `orElseFirst` / `orElseFirstW`
    - add `orLeft`
    - add `chainReaderKW`
    - add `chainFirstReaderK`, `chainFirstReaderKW`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `ReaderTask`
    - add `asksReaderTaskW`, `asksReaderTask`
    - add `chainReaderKW`
    - add `chainFirstReaderK`, `chainFirstReaderKW`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `ReaderTaskEither`
    - add `asksReaderTaskEitherW`, `asksReaderTaskEither`
    - add `orElseFirst` / `orElseFirstW`
    - add `orLeft`
    - add `fromReaderTaskK`
    - add `fromReaderEitherK`
    - add `chainReaderKW`
    - add `chainReaderTaskK`, `chainReaderTaskKW`
    - add `chainFirstReaderK`, `chainFirstReaderKW`
    - add `chainFirstReaderTaskK`, `chainFirstReaderTaskKW`
    - add `chainReaderEitherK`, `chainReaderEitherKW`
    - add `chainFirstReaderEitherK`, `chainFirstReaderEitherKW`
    - add `chainFirstTaskEitherK`, `chainFirstTaskEitherKW`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `ReadonlyArray`
    - add `prependW`, `appendW` (@thewilkybarkid)
    - add `filterE`
    - add `ChainRecDepthFirst` instance (@qlonik)
    - add `chainRecDepthFirst`
    - add `ChainRecBreadthFirst` instance (@qlonik)
    - add `chainRecBreadthFirst`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
    - add `fromOption`
    - add `fromPredicate`
    - add `fromEither`
    - add `FromEither` instance
    - add `fromEitherK`
    - make `isEmpty` a user defined guard
    - add `concat` / `concatW`
    - add `match`, `matchW`, `matchLeftW`, `matchRightW`
    - add `fromOptionK`
    - add `Zero` instance
    - add `guard` constructor
    - add `exists` alias
  - `ReadonlyMap`
    - add `union`
    - add `intersection`
    - add `difference`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
    - add `reduce`
    - add `foldMap`
    - add `reduceRight`
    - add `reduceWithIndex`
    - add `foldMapWithIndex`
    - add `reduceRightWithIndex`
  - `ReadonlyNonEmptyArray`
    - add `matchLeft`, `matchRight`, `modifyHead`, `modifyLast` (@cdimitroulas)
    - add `union`
    - add `getUnionSemigroup`
    - add `makeBy`
    - add `range`
    - make `concat` pipeable
  - `ReadonlyRecord`
    - add `union` (@anthonyjoeseph)
    - add `intersection` (@anthonyjoeseph)
    - add `difference` (@anthonyjoeseph)
    - add `getUnionSemigroup` (@anthonyjoeseph)
    - add `getUnionMonoid` (@anthonyjoeseph)
    - add `getIntersectionSemigroup` (@anthonyjoeseph)
    - add `getDifferenceMagma` (@anthonyjoeseph)
  - `ReadonlySet`
    - add `getUnionSemigroup`
    - add `getDifferenceMagma`
  - `Record`
    - add `union`
    - add `intersection`
    - add `difference`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
  - `Set`
    - add `getUnionSemigroup`
    - add `getDifferenceMagma`
  - `State`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `StateReaderTaskEither`
    - add `fromStateK`
    - add `chainStateK`
    - add `local`
    - add `asksStateReaderTaskEitherW`, `asksStateReaderTaskEither`
    - add `chainReaderKW`
    - add `chainFirstReaderK`, `chainFirstReaderKW`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `string`
    - add `toUpperCase`
    - add `toLowerCase`
    - add `replace`
    - add `split`
    - add `trim`
    - add `trimLeft`
    - add `trimRight`
    - add `includes`
    - add `startsWith`
    - add `endsWith`
    - add `slice`
  - `struct`
    - add `evolve`
  - `Task`
    - add `ApT`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `TaskEither`
    - add `fromTaskOption` (@thewilkybarkid)
    - add `fromTaskOptionK`
    - add `chainTaskOptionK`
    - add `orElseFirst` / `orElseFirstW`
    - add `orLeft`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `TaskOption`
    - add `fromTaskEither` (@thewilkybarkid)
    - add `Zero` instance
    - add `guard` constructor
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
    - add missing `FromEither` instance
  - `TaskThese`
    - add `ApT`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `These`
    - add `elem`
    - add `exists`
    - add `ApT`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `Tree`
    - add `exists`
  - `Witherable`
    - add `filterE`, #1458 (@vinassefranche)
    - add `wiltDefault`
    - add `witherDefault`
- **Polish**
  - remove unnecessary type parameters
    - `Either`
      - `isLeft`
      - `isRight`
    - `Option`
      - `isNone`
    - `These`
      - `isLeft`
      - `isRight`

# 2.11.0-rc.2

- `string`
  - `split` now returns a `ReadonlyNonEmptyArray`
- `TaskOption`
  - add missing `FromEither` instance
- some signature changes in `2.11.0-rc.1` caused type inference issues
  - `Array` / `ReadonlyArray`
    - revert `isOutOfBound` signature change
    - revert `isEmpty` signature change
    - revert `size` signature change
  - `Either`
    - revert `exists` signature change
    - revert `elem` signature change
  - `These`
    - revert `exists` signature change
    - revert `elem` signature change
  - `NonEmptyArray` / `ReadonlyNonEmptyArray`
    - revert `isOutOfBound` signature change
  - `Set` / `ReadonlySet`
    - revert `isEmpty` signature change
    - revert `size` signature change
  - `Map` / `ReadonlyMap`
    - revert `isEmpty` signature change
    - revert `size` signature change

# 2.11.0-rc.1

- **Deprecation**
  - `Array`
    - deprecate `range`, use `NonEmptyArray` module instead.
  - `function`
    - deprecate `Endomorphism`, use `Endomorphism` module instead.
    - deprecate `getEndomorphismMonoid`, use `Endomorphism` module instead.
    - deprecate `Predicate`, use `Predicate` module instead.
    - deprecate `not`, use `Predicate` module instead.
    - deprecate `Refinement`, use `Refinement` module instead.
  - `Monoid`
    - deprecate `monoidVoid`, use `void` module instead.
  - `NonEmptyArray`
    - deprecate `groupSort` (it's just `sort` followed by `group`)
  - `Option`
    - deprecate `getRefinement`, use `Refinement` module instead.
    - deprecate `getFirstMonoid`, use `getMonoid` module instead.
    - deprecate `getLastMonoid`, use `getMonoid` module instead.
  - `ReadonlyArray`
    - deprecate `range`, use `ReadonlyNonEmptyArray` module instead.
  - `ReadonlyNonEmptyArray`
    - deprecate `groupSort` (it's just `sort` followed by `group`)
  - `Record` / `ReadonlyRecord`: deprecate overloads without `Ord` constraint (@anthonyjoeseph):
    - `collect`
    - `reduce`
    - `foldMap`
    - `reduceRight`
    - `reduceWithIndex`
    - `foldMapWithIndex`
    - `reduceRightWithIndex`
    - `getShow`
    - deprecate `Foldable` in favour of `getFoldable` (@anthonyjoeseph)
    - deprecate `FoldableWithIndex` in favour of `getFoldableWithIndex` (@anthonyjoeseph)
    - deprecate `Traversable` in favour of `getTraversable` (@anthonyjoeseph)
    - deprecate `TraversableWithIndex` in favour of `getTraversableWithIndex` (@anthonyjoeseph)
    - deprecate `Witherable` in favour of `getWitherable` (@anthonyjoeseph)
  - `Semigroup`
    - deprecate `semigroupVoid`, use `void` module instead.
- **New Feature**
  - add `Endomorphism` module
  - add `Predicate` module
  - add `Refinement` module
  - add `FromState` module
  - add `FromThese` module
  - add `void` module
  - add `FromReader` module
  - add `NaturalTransformation` module
  - add `Zero` module
  - `Alt`
    - add `altAll`
  - `Alternative`
    - add `altAll`
  - `Array`
    - add `prependW`, `appendW` (@thewilkybarkid)
    - add `fromOption`, `fromPredicate` (@cdimitroulas)
    - add `filterE`
    - add `ChainRecDepthFirst` instance (@qlonik)
    - add `chainRecDepthFirst`
    - add `ChainRecBreadthFirst` instance (@qlonik)
    - add `chainRecBreadthFirst`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
    - add `fromEither`
    - add `FromEither` instance
    - add `fromEitherK`
    - make `isEmpty` a user defined guard
    - add `concat` / `concatW`
    - add `match`, `matchW`, `matchLeftW`, `matchRightW`
    - add `fromOptionK`
    - add `Zero` instance
    - add `guard` constructor
    - add `exists` alias
  - `boolean`
    - add `isBoolean`
  - `Either`
    - add `chainOptionK`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `EitherT`
    - add `orElseFirst`
    - add `orLeft`
  - `function`
    - add `SK` (@cdimitroulas)
    - add `apply`
  - `IO`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `IOEither`
    - add `orElseFirst` / `orElseFirstW`
    - add `orLeft`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `Magma`
    - add `reverse`
    - add `filterFirst`
    - add `filterSecond`
    - add `endo`
    - add `concatAll`
  - `Map`
    - add `union`
    - add `intersection`
    - add `difference`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
    - add `getFoldable`
    - add `foldMap`
    - add `reduceRight`
    - add `reduceWithIndex`
    - add `foldMapWithIndex`
    - add `reduceRightWithIndex`
  - `NonEmptyArray`
    - add `matchLeft`, `matchRight`, `modifyHead`, `modifyLast` (@cdimitroulas)
    - add `union`
    - add `getUnionSemigroup`
    - add `makeBy`
    - add `range`
    - make `concat` pipeable
  - `number`
    - add `MagmaSub`
    - add `isNumber`
  - `string`
    - add `isString`
  - `Option`
    - add `FromEither` instance
    - add `fromEitherK`
    - add `chainEitherK`
    - add `Zero` instance
    - add `guard` constructor
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `Ord`
    - add `trivial` instance
    - add `equals`
  - `Reader`
    - add `asksReaderW`, `asksReader`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `ReaderEither`
    - add `asksReaderEitherW`, `asksReaderEither`
    - add `orElseFirst` / `orElseFirstW`
    - add `orLeft`
    - add `chainReaderKW`
    - add `chainFirstReaderK`, `chainFirstReaderKW`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `ReaderTask`
    - add `asksReaderTaskW`, `asksReaderTask`
    - add `chainReaderKW`
    - add `chainFirstReaderK`, `chainFirstReaderKW`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `ReaderTaskEither`
    - add `asksReaderTaskEitherW`, `asksReaderTaskEither`
    - add `orElseFirst` / `orElseFirstW`
    - add `orLeft`
    - add `fromReaderTaskK`
    - add `fromReaderEitherK`
    - add `chainReaderKW`
    - add `chainReaderTaskK`, `chainReaderTaskKW`
    - add `chainFirstReaderK`, `chainFirstReaderKW`
    - add `chainFirstReaderTaskK`, `chainFirstReaderTaskKW`
    - add `chainReaderEitherK`, `chainReaderEitherKW`
    - add `chainFirstReaderEitherK`, `chainFirstReaderEitherKW`
    - add `chainFirstTaskEitherK`, `chainFirstTaskEitherKW`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `ReadonlyArray`
    - add `prependW`, `appendW` (@thewilkybarkid)
    - add `filterE`
    - add `ChainRecDepthFirst` instance (@qlonik)
    - add `chainRecDepthFirst`
    - add `ChainRecBreadthFirst` instance (@qlonik)
    - add `chainRecBreadthFirst`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
    - add `fromOption`
    - add `fromPredicate`
    - add `fromEither`
    - add `FromEither` instance
    - add `fromEitherK`
    - make `isEmpty` a user defined guard
    - add `concat` / `concatW`
    - add `match`, `matchW`, `matchLeftW`, `matchRightW`
    - add `fromOptionK`
    - add `Zero` instance
    - add `guard` constructor
    - add `exists` alias
  - `ReadonlyMap`
    - add `union`
    - add `intersection`
    - add `difference`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
    - add `reduce`
    - add `foldMap`
    - add `reduceRight`
    - add `reduceWithIndex`
    - add `foldMapWithIndex`
    - add `reduceRightWithIndex`
  - `ReadonlyNonEmptyArray`
    - add `matchLeft`, `matchRight`, `modifyHead`, `modifyLast` (@cdimitroulas)
    - add `union`
    - add `getUnionSemigroup`
    - add `makeBy`
    - add `range`
    - make `concat` pipeable
  - `ReadonlyRecord`
    - add `union` (@anthonyjoeseph)
    - add `intersection` (@anthonyjoeseph)
    - add `difference` (@anthonyjoeseph)
    - add `getUnionSemigroup` (@anthonyjoeseph)
    - add `getUnionMonoid` (@anthonyjoeseph)
    - add `getIntersectionSemigroup` (@anthonyjoeseph)
    - add `getDifferenceMagma` (@anthonyjoeseph)
  - `ReadonlySet`
    - add `getUnionSemigroup`
    - add `getDifferenceMagma`
  - `Record`
    - add `union`
    - add `intersection`
    - add `difference`
    - add `getUnionSemigroup`
    - add `getUnionMonoid`
    - add `getIntersectionSemigroup`
    - add `getDifferenceMagma`
  - `Set`
    - add `getUnionSemigroup`
    - add `getDifferenceMagma`
  - `State`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `StateReaderTaskEither`
    - add `fromStateK`
    - add `chainStateK`
    - add `local`
    - add `asksStateReaderTaskEitherW`, `asksStateReaderTaskEither`
    - add `chainReaderKW`
    - add `chainFirstReaderK`, `chainFirstReaderKW`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `string`
    - add `toUpperCase`
    - add `toLowerCase`
    - add `replace`
    - add `split`
    - add `trim`
    - add `trimLeft`
    - add `trimRight`
    - add `includes`
    - add `startsWith`
    - add `endsWith`
    - add `slice`
  - `struct`
    - add `evolve`
  - `Task`
    - add `ApT`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `TaskEither`
    - add `fromTaskOption` (@thewilkybarkid)
    - add `fromTaskOptionK`
    - add `chainTaskOptionK`
    - add `orElseFirst` / `orElseFirstW`
    - add `orLeft`
    - add `flattenW`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `TaskOption`
    - add `fromTaskEither` (@thewilkybarkid)
    - add `Zero` instance
    - add `guard` constructor
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `TaskThese`
    - add `ApT`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
    - add `traverseReadonlyNonEmptyArrayWithIndexSeq`
    - add `traverseReadonlyArrayWithIndexSeq`
  - `These`
    - add `elem`
    - add `exists`
    - add `ApT`
    - add `traverseReadonlyNonEmptyArrayWithIndex`
    - add `traverseReadonlyArrayWithIndex`
  - `Tree`
    - add `exists`
  - `Witherable`
    - add `filterE`, #1458 (@vinassefranche)
    - add `wiltDefault`
    - add `witherDefault`
- **Polish**
  - remove unnecessary type parameters
    - `Either`
      - `exists`
      - `isLeft`
      - `isRight`
      - `elem`
    - `Option`
      - `isNone`
    - `These`
      - `isLeft`
      - `isRight`
    - `Set` / `ReadonlySet`
      - `isEmpty`
      - `size`
    - `Array` / `ReadonlyArray`
      - `isEmpty`
      - `isOutOfBound`
      - `size`
    - `Map` / `ReadonlyMap`
      - `isEmpty`
      - `size`
    - `NonEmptyArray` / `ReadonlyNonEmptyArray`
      - `isOutOfBound`

# 2.10.5

- **Bug Fix**
  - `StateT`
    - fix typo in `fromF`, #1503 (@DKurilo)

# 2.10.4

- **Bug Fix**
  - `altW` left type is too wide in `Either`-based data types, #1488 (@wmaurer)

# 2.10.3

- **Bug Fix**
  - `StateReaderTaskEither`
    - `traverseArrayWithIndex` does not pass the output state from each step to the subsequent step, #1486

# 2.10.2

- **Bug Fix**
  - `Record`
    - `hasOwnProperty` should be an alias of `ReadonlyRecord.hasOwnProperty`

# 2.10.1

- **Bug Fix**
  - rename `hasOwnProperty` in `internal` module, #1481 (@OliverJAsh)

# 2.10.0

- **Deprecations**
  - deprecate `pipeable` module, use the specific helpers instead
  - deprecate `ValidationT` module, use `EitherT` instead
  - deprecate "mega instances", use small, specific instances instead
  - deprecate the old monad transformers, use the specific helpers instead
  - `Applicative`
    - deprecate `getApplicativeComposition`, use `ap` helper instead
  - `Array`
    - deprecate `prependToAll`, use `prependAll` instead
    - deprecate `cons` in favour of `prepend`
    - deprecate `snoc` in favour of `append`
    - deprecate `empty`
  - `BooleanAlgebra`
    - deprecate `booleanAlgebraBoolean`, use `boolean.BooleanAlgebra` instead
    - deprecate `getFunctionBooleanAlgebra`, use `function.getBooleanAlgebra` instead
    - deprecate `getDualBooleanAlgebra`, use `reverse` instead
  - `Bounded`
    - deprecate `boundedNumber`, use `number.Bounded` instead
  - `Choice`
    - deprecate `splitChoice` in favour of `split`
    - deprecate `fanin` in favour of `fanIn`
  - `Compactable`
    - deprecate `getCompactableComposition`, use `compact`, `separate` helpers instead
    - deprecate `Separated`, use `Separated.Separated` instead
  - `Either`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getValidationSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getValidationMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getValidation`, use `getApplicativeValidation` and `getAltValidation` instead
    - deprecate `Json` type, use the `Json` module instead
    - deprecate `parseJSON` type, use the `Json` module instead
    - deprecate `stringifyJSON` type, use the `Json` module instead
  - `Eq`
    - deprecate `eqBoolean`, use `boolean.Eq` instead
    - deprecate `eqString`, use `string.Eq` instead
    - deprecate `eqNumber`, use `number.Eq` instead
    - deprecate `eqDate`, use `Date.Eq` instead
    - deprecate `getStructEq`, use `struct` instead
    - deprecate `getTupleEq`, use `tuple` instead
  - `Filterable`
    - deprecate `getFilterableComposition`, use `filter`, `filterMap`, `partition`, `partitionMap` helpers instead
  - `Foldable`
    - deprecate `toArray` in favour of `toReadonlyArray`
    - deprecate `getFoldableComposition`, use `reduce`, `foldMap`, `reduceRight` helpers instead
  - `FoldableWithIndex`
    - deprecate `getFoldableWithIndexComposition`, use `reduceWithIndex`, `foldMapWithIndex`, `reduceRightWithIndex` helpers instead
  - `Functor`
    - deprecate `getFunctorComposition`, use `map` helper instead
  - `FunctorWithIndex`
    - deprecate `getFunctorWithIndexComposition`, use `mapWithIndex` helper instead
  - `IO`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `fromIO`
  - `IOEither`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - daprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getIOValidation`, use `getApplicativeIOValidation` and `getAltIOValidation` instead
  - `Map`
    - deprecate `insertAt` in favour of `upsertAt`
    - deprecate `empty`
  - `Monoid`
    - deprecate `monoidAll`, use `boolean.MonoidAll` instead
    - deprecate `monoidAny`, use `boolean.MonoidAny` instead
    - deprecate `getFunctionMonoid`, use `function.getMonoid` instead
    - deprecate `getEndomorphismMonoid`, use `function.getEndomorphismMonoid` instead (**Note**. The execution order in
      `function.getEndomorphismMonoid` is reversed)
    - deprecate `monoidString`, use `string.Monoid` instead
    - deprecate `monoidSum`, use `number.MonoidSum` instead
    - deprecate `monoidProduct`, use `number.MonoidProduct` instead
    - deprecate `fold`, use `concatAll` instead
    - deprecate `getMeetMonoid`, use `min` instead
    - deprecate `getJoinMonoid`, use `max` instead
    - deprecate `getDualMonoid`, use `reverse` instead
    - deprecate `getStructMonoid`, use `struct` instead
    - deprecate `getTupleMonoid`, use `tuple` instead
  - `NonEmptyArray`
    - deprecate `fold`, use `concatAll` instead
    - deprecate `prependToAll`, use `prependAll` instead
    - deprecate `cons` in favour of `prepend`
    - deprecate `snoc` in favour of `append`
    - deprecate `uncons` in favour of `unprepend`
    - deprecate `unsnoc` in favour of `unappend`
    - deprecate `filter` in favour of `Array`'s `filter`
    - deprecate `filterWithIndex` in favour of `Array`'s `filterWithIndex`
  - `Option`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
  - `Ord`
    - deprecate `ordBoolean`, use `boolean.Ord` instead
    - deprecate `ordString`, use `string.Ord` instead
    - deprecate `ordNumber`, use `number.Ord` instead
    - deprecate `ordDate`, use `Date.Ord` instead
    - deprecate `getDualOrd`, use `reverse` instead
    - deprecate `getTupleOrd`, use `tuple` instead
  - `Ordering`
    - deprecate `eqOrdering`, use `Eq` instead
    - deprecate `monoidOrdering`, use `Monoid` instead
    - deprecate `invert` in favour of `reverse`
  - `Ring`
    - deprecate `getFunctionRing`, use `function.getRing` instead
  - `Reader`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getMonoid` in favour of `Applicative.getApplicativeMonoid`
  - `ReaderEither`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getReaderValidation`, use `getApplicativeReaderValidation` and `getAltReaderValidation` instead
    - deprecate `local`, Use `Reader`'s `local` instead
  - `ReaderTask`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `run`
    - deprecate `local`, Use `Reader`'s `local` instead
  - `ReaderTaskEither`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getReaderTaskValidation`, use `getApplicativeReaderTaskValidation` and `getAltReaderTaskValidation` instead
    - deprecate `run`
    - deprecate `local`, Use `Reader`'s `local` instead
  - `ReaderTaskEither`
    - deprecate `run`
  - `ReadonlyArray`
    - deprecate `prependToAll`, use `prependAll` instead
    - deprecate `cons` in favour of `prepend`
    - deprecate `snoc` in favour of `append`
  - `ReadonlyNonEmptyArray`
    - deprecate `fold`, use `concatAll` instead
    - deprecate `prependToAll`, use `prependAll` instead
    - deprecate `insertAt`, Use `ReadonlyArray`'s `insertAt` instead
    - deprecate `cons` in favour of `prepend`
    - deprecate `snoc` in favour of `append`
    - deprecate `uncons` in favour of `unprepend`
    - deprecate `unsnoc` in favour of `unappend`
    - deprecate `filter` in favour of `ReadonlyArray`'s `filter`
    - deprecate `filterWithIndex` in favour of `ReadonlyArray`'s `filterWithIndex`
  - `ReadonlyMap`
    - deprecate `insertAt` in favour of `upsertAt`
  - `ReadonlyRecord`
    - deprecate `hasOwnProperty`, use `has` instead
    - deprecate `insertAt` in favour of `upsertAt`
  - `ReadonlySet`
    - deprecate `fromArray` in favour of `fromReadonlyArray`
  - `ReadonlyTuple`
    - deprecate `mapLeft` in favour of `mapSnd`
    - deprecate `map` in favour of `mapFst`
  - `Record`
    - deprecate `hasOwnProperty`, use `has` instead
    - deprecate `insertAt` in favour of `upsertAt`
    - deprecate `empty`
  - `Ring`
    - deprecate `getTupleRing`, use `tuple` instead
  - `Semigroup`
    - deprecate `semigroupAll`, use `boolean.SemigroupAll` instead
    - deprecate `semigroupAny`, use `boolean.SemigroupAny` instead
    - deprecate `getFunctionSemigroup`, use `function.getSemigroup` instead
    - deprecate `semigroupString`, use `string.Semigroup` instead
    - deprecate `semigroupSum`, use `number.SemigroupSum` instead
    - deprecate `semigroupProduct`, use `number.SemigroupProduct` instead
    - deprecate `fold`, use `concatAll` instead
    - deprecate `getIntercalateSemigroup`, use `intercalate` instead
    - deprecate `getMeetSemigroup`, use `min` instead
    - deprecate `getJoinSemigroup`, use `max` instead
    - deprecate `getDualSemigroup`, use `reverse` instead
    - deprecate `getStructSemigroup`, use `struct` instead
    - deprecate `getTupleSemigroup`, use `tuple` instead
    - deprecate `getFirstSemigroup`, use `first` instead
    - deprecate `getLastSemigroup`, use `last` instead
    - deprecate `getObjectSemigroup`, use `assign` instead
  - `Set`
    - deprecate `subset` in favour of `isSubset`
  - `Show`
    - deprecate `showBoolean`, use `boolean.Show` instead
    - deprecate `showString`, use `string.Show` instead
    - deprecate `showNumber`, use `number.Show` instead
    - deprecate `getStructShow`, use `struct` instead
    - deprecate `getTupleShow`, use `tuple` instead
  - `Strong`
    - deprecate `splitStrong` in favour of `split`
    - deprecate `fanout` in favour of `fanOut`
  - `Task`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `fromTask`
  - `TaskEither`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getTaskValidation`, use `getApplicativeTaskValidation` and `getAltTaskValidation` instead
  - `TaskThese`
    - deprecate `functorTaskThese` instance in favour of `Functor`
    - deprecate `bifunctorTaskThese` instance in favour of `Bifunctor`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `toTuple` in favour of `toTuple2`
  - `These`
    - deprecate `toTuple` in favour of `toTuple2`
  - `Traversable`
    - deprecate `getTraversableComposition`, use `traverse`, `sequence` helpers instead
  - `Tuple`
    - deprecate `mapLeft` in favour of `mapSnd`
    - deprecate `map` in favour of `mapFst`
- **New Feature**
  - add `Pointed` type class
  - add `FromEither` type class
  - add `FromIO` type class
  - add `FromTask` type class
  - add `TaskOption` module
  - add `string` module
  - add `number` module
  - add `Separated` module (@YBogomolov)
  - add `Json` module
  - `Apply`
    - add `ap` helper
    - add `apS` helper
    - add `apFirst` helper
    - add `apSecond` helper
    - add `getApplySemigroup`
  - `Applicative`
    - add `getApplicativeMonoid`
  - `Array`
    - add `Pointed` instance
    - add `Apply` instance
    - add `matchLeft` alias
    - add `matchRight` alias
    - add `size`
    - better `unsafeInsertAt` signature
    - better `chunksOf` signature
    - add `getSemigroup`
  - `boolean`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `Eq` instance
    - add `BooleanAlgebra` instance
    - add `SemigroupAll` instance
    - add `SemigroupAny` instance
    - add `MonoidAll` instance
    - add `MonoidAny` instance
    - add `Ord` instance
    - add `Show` instance
  - `BooleanAlgebra`
    - add `reverse`
  - `Chain`
    - add `bind` helper
    - add `chainFirst` helper
  - `Compactable`
    - add `compact` helper
    - add `separate` helper
    - add `separated` constructor
  - `Date`
    - add `Eq` instance
    - add `Ord` instance
  - `Filterable`
    - add `filter` helper
    - add `filterMap` helper
    - add `partition` helper
    - add `partitionMap` helper
  - `Foldable`
    - add `reduce` helper
    - add `foldaMap` helper
    - add `reduceRight` helper
  - `FoldableWithIndex`
    - add `reduceWithIndex` helper
    - add `foldaMapWithIndex` helper
    - add `reduceRightWithIndex` helper
  - `function`
    - add `getBooleanAlgebra`
    - add `getSemigroup`
    - add `getMonoid`
    - add `getSemiring`
    - add `getRing`
    - add `getEndomorphismMonoid`
  - `Functor`
    - add `map` helper
    - add `bindTo` helper
    - add `flap` derivable, #1393 (@williamareynolds)
  - `FunctorWithIndex`
    - add `mapWithIndex` helper
  - `Either`
    - add `Pointed` instance
    - add `Apply` instance
    - add `getCompactable`
    - add `getFilterable`
    - add `FromEither` instance
    - add `toUnion`, closes #1362
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `tryCatchK`
    - add `fromOptionK`
    - add `chainOptionK`
  - `EitherT`
    - split `getEitherM` into separated functions
  - `Eq`
    - add `getSemigroup`
    - add `struct`
    - add `tuple`
  - `Identity`
    - add `Pointed` instance
    - add `Apply` instance
  - `IO`
    - add `Pointed` instance
    - add `Apply` instance
    - add `FromIO` instance
  - `IOEither`
    - add `Pointed` instance
    - add `getCompactable`
    - add `FromIO` instance
    - add `FromEither` instance
    - add `tryCatchK` combinator
    - add `toUnion`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `fromOptionK`
    - add `chainOptionK`
  - `Map`
    - add `filterWithIndex` combinator
    - add `filterMapWithIndex` combinator
    - add `partitionWithIndex` combinator
    - add `partitionMapWithIndex` combinator
    - add `getTraversableWithIndex` instance
    - add `getFoldableWithIndex` instance
  - `Monoid`
    - add `concatAll`
    - add `min`
    - add `max`
    - add `reverse`
    - add `struct`
    - add `tuple`
  - `NonEmptyArray`
    - add `Pointed` instance
    - add `Apply` instance
    - add `concatAll`
    - add `isNonEmpty` guard
    - add `fromReadonlyNonEmptyArray` constructor
    - add `chainWithIndex` combinator
    - add `chop`
    - add `splitAt`
    - add `chunksOf`
  - `Option`
    - add `Pointed` instance
    - add `Apply` instance
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `tryCatchK`
  - `OptionT`
    - split `getOptionM` into separated functions
  - `Ord`
    - add `equalsDefault`
    - add `reverse`
    - add `tuple`
  - `Ordering`
    - add `Eq` instance
    - add `Semigroup` instance
    - add `Monoid` instance
    - add `match`
  - `Random`
    - add `randomElem`
  - `Reader`
    - add `Pointed` instance
    - add `Apply` instance
    - export `first`, `second`, `left`, `right`
  - `ReaderT`
    - split `getReaderM` into separated functions
  - `ReaderEither`
    - add `Pointed` instance
    - add `Apply` instance
    - add `FromEither` instance
    - add `getCompactable`
    - add `getFilterable`
    - add `toUnion`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `fromOptionK`
    - add `chainOptionK`
  - `ReaderTask`
    - add `Pointed` instance
    - add `ApplyPar` instance
    - add `ApplySeq` instance
    - add `FromIO` instance
    - add `FromTask` instance
    - add `traverseSeqArrayWithIndex`
    - add `traverseSeqArray`
    - add `sequenceSeqArray`
    - export `Chain` instance
    - export `Monad` instance
    - export `MonadIO` instance
    - export `MonadTask` instance
  - `ReaderTaskEither`
    - add `Pointed` instance
    - add `ApplyPar` instance
    - add `ApplySeq` instance
    - add `FromIO` instance
    - add `FromTask` instance
    - add `FromEither` instance
    - add `getCompactable`
    - add `getFilterable`
    - add `toUnion`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `fromOptionK`
    - add `chainOptionK`
    - export `Chain` instance
    - export `Monad` instance
    - export `MonadIO` instance
    - export `MonadTask` instance
    - export `MonadThrow` instance
  - `ReadonlyArray`
    - add `Pointed` instance
    - add `Apply` instance
    - add `getSemigroup`
    - add `matchLeft` instance
    - add `matchRight` instance
    - add `size`
    - better `unsafeInsertAt` signature
    - better `chunksOf` signature, closes #1407
  - `ReadonlyMap`
    - add `filterWithIndex` combinator
    - add `filterMapWithIndex` combinator
    - add `partitionWithIndex` combinator
    - add `partitionMapWithIndex` combinator
    - add `getFunctorWithIndex`
    - add `getFoldable`
    - add `getFoldableWithIndex`
    - add `getTraversable`
    - add `getTraversableWithIndex`
  - `ReadonlyNonEmptyArray`
    - add `Pointed` instance
    - add `Apply` instance
    - add `concatAll`
    - add `isNonEmpty`
    - add `chainWithIndex`
    - add `chop`
    - add `splitAt`
    - add `chunksOf`
  - `ReadonlySet`
    - add `isEmpty`
    - add `size`
    - add `toggle`
  - `Ring`
    - add `tuple`
  - `Set`
    - add `isEmpty`
    - add `size`
  - `Semigroup`
    - add `constant`
    - add `concatAll`
    - add `intercalate`
    - add `min`
    - add `max`
    - add `struct`
    - add `tuple`
    - add `first`
    - add `last`
    - add `assign`
  - `Show`
    - add `struct`
    - add `tuple`
  - `State`
    - add `Pointed` instance
    - add `Apply` instance
  - `StateT`
    - split `getStateM` into separated functions
  - `StateReaderTaskEither`
    - add `Pointed` instance
    - add `Apply` instance
    - add `Monad` instance
    - add `FromIO` instance
    - add `FromTask` instance
    - add `FromEither` instance
    - add `fromOptionK`
    - add `chainOptionK`
    - export `MonadIO` instance
    - export `MonadTask` instance
    - export `MonadThrow` instance
  - `Task`
    - add `Pointed` instance
    - add `ApplyPar` instance
    - add `ApplySeq` instance
    - add `FromIO` instance
    - add `FromTask` instance
    - export `Chain` instance
    - export `Monad` instance
    - export `MonadIO` instance
    - export `MonadTask` instance
  - `TaskEither`
    - add `Pointed` instance
    - add `ApplyPar` instance
    - add `ApplySeq` instance
    - add `getCompactable`
    - add `FromIO` instance
    - add `FromTask` instance
    - add `FromEither` instance
    - add `toUnion`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `fromOptionK`
    - add `chainOptionK`
    - export `Chain` instance
    - export `Monad` instance
    - export `MonadIO` instance
    - export `MonadTask` instance
    - export `MonadThrow` instance
  - `TaskThese`
    - add `Functor` instance
    - add `Pointed` instance
    - add `Bifunctor` instance
    - add `toTuple2`
    - add `getApply`
    - add `FromIO` instance
    - add `FromTask` instance
    - add `fromEither` function
    - add `FromEither` instance
    - add `getChain` instance
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `fromOption`
    - add `fromOptionK`
    - add `fromPredicate`
  - `These`
    - add `Pointed` instance
    - add `of` function
    - add `getApply`
    - add `toTuple2`
    - add `FromEither` instance
    - add `fromOption` constructor
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `fromOptionK`
  - `TaskOption`
    - export `Chain` instance
    - export `Monad` instance
  - `TheseT`
    - split `getTheseM` into separated functions
  - `Traversable`
    - add `traverse` helper
    - add `sequence` helper
  - `Tree`
    - add `Pointed` instance
    - add `Apply` instance
  - `Writer`
    - add `getPointed`
    - add `getApply`
    - add `getApplicative`
    - add `getChain`
- **Bug Fix**
  - `ReadonlyRecord` / `Record`: remove `extends string` constraints in `singleton` / `insertAt`, closes #1413
  - `TaskOption`
    - fix `getOrElseW` signature
- **Internal**
  - add `internal` module
- **Polish**
  - the scope in `bindTo`, `bind`, `bindW`, `apS`, `apSW` is now correctly `readonly`
  - fix `FromEither` type parameter order
  - `Array` / `ReadonlyArray`
    - assert arrays as non-empty when using `some`, #1424 (@thewilkybarkid)
    - fix `matchLeft`, `matchRight` type parameter order
  - `EitherT`
    - add overloads for `Kind2`, `Kind3`, `Kind4`
  - `OptionT`
    - add overloads for `Kind2`, `Kind3`, `Kind4`
  - `ReaderT`
    - add overloads for `Kind2`, `Kind3`, `Kind4`
  - `ReadonlyMap`
    - sort keys in `getShow`
  - `ReadonlySet`
    - sort keys in `getShow`
  - `StateT`
    - add overloads for `Kind2`, `Kind3`
  - `TheseT`
    - add overloads for `Kind2`, `Kind3`, `Kind4`

# 2.10.0-rc.8

- **Polish**
  - the scope in `bindTo`, `bind`, `bindW`, `apS`, `apSW` is now correctly `readonly`
  - fix `FromEither` type parameter order

# 2.10.0-rc.7

- **Breaking Change** (with respect to `2.10.0-rc`)
  - rename module `object` to `struct`

# 2.10.0-rc.6

- **Bug Fix**
  - `ReadonlyNonEmptyArray`
    - remove circular dependency on `NonEmptyArray`, closes #1443
- **Breaking Change** (with respect to `2.10.0-rc`)
  - `EitherT`
    - rename `match` to `matchE` and add `match`
  - `IOEither`
    - rename `match` / `matchW` to `matchE` / `matchWE` and add `match` / `matchW`
  - `OptionT`
    - rename `match` to `matchE` and add `match`
  - `ReaderEither`
    - rename `match` / `matchW` to `matchE` / `matchWE` and add `match` / `matchW`
  - `ReaderTaskEither`
    - rename `match` / `matchW` to `matchE` / `matchWE` and add `match` / `matchW`
  - `TheseT`
    - rename `match` to `matchE` and add `match`
  - `TaskEither`
    - rename `match` / `matchW` to `matchE` / `matchWE` and add `match` / `matchW`
  - `TaskOption`
    - rename `match` / `matchW` to `matchE` / `matchWE` and add `match` / `matchW`
  - `TaskThese`
    - rename `match` / `matchW` to `matchE` / `matchWE` and add `match` / `matchW`

# 2.10.0-rc.5

- **Bug Fix**
  - `TaskOption`
    - fix `getOrElseW` signature
- **Breaking Change** (with respect to `2.10.0-rc`)
  - `OptionT`
    - rename `none` to `zero` and change signature
  - `ReaderT`
    - remove `ask`, `asks` (they will be derived from the `FromReader` type-class in `2.11`)
  - `Semigroup`
    - move `assign` to `object` module and rename to `getAssignSemigroup`
  - `ReaderT`
    - remove `get`, `put`, `modify`, `gets` (they will be derived from the `FromState` type-class in `2.11`)
- **Deprecation**
  - `Tuple`
    - deprecate `mapLeft` in favour of `mapSnd`
    - deprecate `map` in favour of `mapFst`
  - `ReadonlyTuple`
    - deprecate `mapLeft` in favour of `mapSnd`
    - deprecate `map` in favour of `mapFst`
- **Polish**
  - `Array` / `ReadonlyArray`
    - assert arrays as non-empty when using `some`, #1424 (@thewilkybarkid)
    - fix `matchLeft`, `matchRight` type parameter order
  - `EitherT`
    - add overloads for `Kind2`, `Kind3`, `Kind4`
  - `OptionT`
    - add overloads for `Kind2`, `Kind3`, `Kind4`
  - `ReaderT`
    - add overloads for `Kind2`, `Kind3`, `Kind4`
  - `ReadonlyMap`
    - sort keys in `getShow`
  - `ReadonlySet`
    - sort keys in `getShow`
  - `StateT`
    - add overloads for `Kind2`, `Kind3`
  - `TheseT`
    - add overloads for `Kind2`, `Kind3`, `Kind4`
- **Internal**
  - add `internal` module

# 2.10.0-rc.4

- **Deprecations**
  - `Array`
    - deprecate `empty`
  - `Map`
    - deprecate `empty`
  - `NonEmptyArray`
    - deprecate `filter` in favour of `Array`'s `filter`
    - deprecate `filterWithIndex` in favour of `Array`'s `filterWithIndex`
  - `ReadonlyNonEmptyArray`
    - deprecate `filter` in favour of `ReadonlyArray`'s `filter`
    - deprecate `filterWithIndex` in favour of `ReadonlyArray`'s `filterWithIndex`
  - `Record`
    - deprecate `empty`
- **Polish**
  - `NonEmptyArray`
    - remove duplicated `append`, `prepend`, `isNonEmpty`
  - `ReadonlyNonEmptyArray`
    - remove duplicated `append`, `prepend`, `isNonEmpty`

# 2.10.0-rc.3

- **Deprecations**
  - `Array`
    - deprecate `cons` in favour of `prepend`
    - deprecate `snoc` in favour of `append`
  - `Ordering`
    - deprecate `invert` in favour of `reverse`
  - `ReadonlyMap`
    - deprecate `insertAt` in favour of `upsertAt`
  - `ReadonlyRecord`
    - deprecate `insertAt` in favour of `upsertAt`
  - `Map`
    - deprecate `insertAt` in favour of `upsertAt`
  - `NonEmptyArray`
    - deprecate `cons` in favour of `prepend`
    - deprecate `snoc` in favour of `append`
    - deprecate `uncons` in favour of `unprepend`
    - deprecate `unsnoc` in favour of `unappend`
  - `Record`
    - deprecate `insertAt` in favour of `upsertAt`
  - `ReadonlyArray`
    - deprecate `cons` in favour of `prepend`
    - deprecate `snoc` in favour of `append`
  - `ReadonlyNonEmptyArray`
    - deprecate `insertAt`, Use `ReadonlyArray`'s `insertAt` instead
    - deprecate `cons` in favour of `prepend`
    - deprecate `snoc` in favour of `append`
    - deprecate `uncons` in favour of `unprepend`
    - deprecate `unsnoc` in favour of `unappend`
  - `ReadonlySet`
    - deprecate `fromArray` in favour of `fromReadonlyArray`
  - `Set`
    - deprecate `subset` in favour of `isSubset`
- **New Feature**
  - `Array`
    - add `size`
    - better `unsafeInsertAt` signature
    - better `chunksOf` signature
    - add `getSemigroup`
  - `Map`
    - add `filterWithIndex` combinator
    - add `filterMapWithIndex` combinator
    - add `partitionWithIndex` combinator
    - add `partitionMapWithIndex` combinator
    - add `getTraversableWithIndex` instance
    - add `getFoldableWithIndex` instance
  - `NonEmptyArray`
    - add `isNonEmpty` guard
    - add `fromReadonlyNonEmptyArray` constructor
    - add `chainWithIndex` combinator
    - add `chop`
    - add `splitAt`
    - add `chunksOf`
  - `Ordering`
    - add `match`
  - `ReadonlyArray`
    - add `size`
    - better `unsafeInsertAt` signature
    - better `chunksOf` signature, closes #1407
  - `ReadonlyNonEmptyArray`
    - add `isNonEmpty`
    - add `chainWithIndex`
    - add `chop`
    - add `splitAt`
    - add `chunksOf`
  - `ReadonlySet`
    - add `isEmpty`
    - add `size`
    - add `toggle`
  - `Set`
    - add `isEmpty`
    - add `size`
  - `string`
    - add `empty`
    - add `isEmpty`
    - add `size`
- **Bug Fix**
  - `ReadonlyRecord` / `Record`: remove `extends string` constraints in `singleton` / `insertAt`, closes #1413

# 2.10.0-rc.2

- **Deprecations**
  - `Choice`
    - deprecate `splitChoice` in favour of `split`
    - deprecate `fanin` in favour of `fanIn`
  - `Strong`
    - deprecate `splitStrong` in favour of `split`
    - deprecate `fanout` in favour of `fanOut`
- **New Feature**
  - `Reader`
    - export `first`, `second`, `left`, `right`
  - `ReaderTask`
    - export `Chain` instance
    - export `Monad` instance
    - export `MonadIO` instance
    - export `MonadTask` instance
  - `ReaderTaskEither`
    - export `Chain` instance
    - export `Monad` instance
    - export `MonadIO` instance
    - export `MonadTask` instance
    - export `MonadThrow` instance
  - `StateReaderTaskEither`
    - export `MonadIO` instance
    - export `MonadTask` instance
    - export `MonadThrow` instance
  - `Task`
    - export `Chain` instance
    - export `Monad` instance
    - export `MonadIO` instance
    - export `MonadTask` instance
  - `TaskEither`
    - export `Chain` instance
    - export `Monad` instance
    - export `MonadIO` instance
    - export `MonadTask` instance
    - export `MonadThrow` instance
  - `TaskOption`
    - export `Chain` instance
    - export `Monad` instance
- **Polish**
  - `Compactable`
    - split `compact` / `separate` constraints

# 2.10.0-rc.1

- **Deprecations**
  - deprecate `pipeable` module, use the specific helpers instead
  - deprecate `ValidationT` module, use `EitherT` instead
  - deprecate "mega instances", use small, specific instances instead
  - deprecate the old monad transformers, use the specific helpers instead
  - `Applicative`
    - deprecate `getApplicativeComposition`, use `ap` helper instead
  - `Array`
    - deprecate `prependToAll`, use `prependAll` instead
  - `BooleanAlgebra`
    - deprecate `booleanAlgebraBoolean`, use `boolean.BooleanAlgebra` instead
    - deprecate `getFunctionBooleanAlgebra`, use `function.getBooleanAlgebra` instead
    - deprecate `getDualBooleanAlgebra`, use `reverse` instead
  - `Bounded`
    - deprecate `boundedNumber`, use `number.Bounded` instead
  - `Compactable`
    - deprecate `getCompactableComposition`, use `compact`, `separate` helpers instead
    - deprecate `Separated`, use `Separated.Separated` instead
  - `Either`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getValidationSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getValidationMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getValidation`, use `getApplicativeValidation` and `getAltValidation` instead
    - deprecate `Json` type, use the `Json` module instead
    - deprecate `parseJSON` type, use the `Json` module instead
    - deprecate `stringifyJSON` type, use the `Json` module instead
  - `Eq`
    - deprecate `eqBoolean`, use `boolean.Eq` instead
    - deprecate `eqString`, use `string.Eq` instead
    - deprecate `eqNumber`, use `number.Eq` instead
    - deprecate `eqDate`, use `Date.Eq` instead
    - deprecate `getStructEq`, use `struct` instead
    - deprecate `getTupleEq`, use `tuple` instead
  - `Filterable`
    - deprecate `getFilterableComposition`, use `filter`, `filterMap`, `partition`, `partitionMap` helpers instead
  - `Foldable`
    - deprecate `toArray` in favour of `toReadonlyArray`
    - deprecate `getFoldableComposition`, use `reduce`, `foldMap`, `reduceRight` helpers instead
  - `FoldableWithIndex`
    - deprecate `getFoldableWithIndexComposition`, use `reduceWithIndex`, `foldMapWithIndex`, `reduceRightWithIndex` helpers instead
  - `Functor`
    - deprecate `getFunctorComposition`, use `map` helper instead
  - `FunctorWithIndex`
    - deprecate `getFunctorWithIndexComposition`, use `mapWithIndex` helper instead
  - `IO`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `fromIO`
  - `IOEither`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getIOValidation`, use `getApplicativeIOValidation` and `getAltIOValidation` instead
  - `Monoid`
    - deprecate `monoidAll`, use `boolean.MonoidAll` instead
    - deprecate `monoidAny`, use `boolean.MonoidAny` instead
    - deprecate `getFunctionMonoid`, use `function.getMonoid` instead
    - deprecate `getEndomorphismMonoid`, use `function.getEndomorphismMonoid` instead (**Note**. The execution order in
      `function.getEndomorphismMonoid` is reversed)
    - deprecate `monoidString`, use `string.Monoid` instead
    - deprecate `monoidSum`, use `number.MonoidSum` instead
    - deprecate `monoidProduct`, use `number.MonoidProduct` instead
    - deprecate `fold`, use `concatAll` instead
    - deprecate `getMeetMonoid`, use `min` instead
    - deprecate `getJoinMonoid`, use `max` instead
    - deprecate `getDualMonoid`, use `reverse` instead
    - deprecate `getStructMonoid`, use `struct` instead
    - deprecate `getTupleMonoid`, use `tuple` instead
  - `NonEmptyArray`
    - deprecate `fold`, use `concatAll` instead
    - deprecate `prependToAll`, use `prependAll` instead
  - `Option`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
  - `Ord`
    - deprecate `ordBoolean`, use `boolean.Ord` instead
    - deprecate `ordString`, use `string.Ord` instead
    - deprecate `ordNumber`, use `number.Ord` instead
    - deprecate `ordDate`, use `Date.Ord` instead
    - deprecate `getDualOrd`, use `reverse` instead
    - deprecate `getTupleOrd`, use `tuple` instead
  - `Ordering`
    - deprecate `eqOrdering`, use `Eq` instead
    - deprecate `monoidOrdering`, use `Monoid` instead
  - `Ring`
    - deprecate `getFunctionRing`, use `function.getRing` instead
  - `Reader`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getMonoid` in favour of `Applicative.getApplicativeMonoid`
  - `ReaderEither`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getReaderValidation`, use `getApplicativeReaderValidation` and `getAltReaderValidation` instead
    - deprecate `local`, Use `Reader`'s `local` instead
  - `ReaderTask`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `run`
    - deprecate `local`, Use `Reader`'s `local` instead
  - `ReaderTaskEither`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getReaderTaskValidation`, use `getApplicativeReaderTaskValidation` and `getAltReaderTaskValidation` instead
    - deprecate `run`
    - deprecate `local`, Use `Reader`'s `local` instead
  - `ReaderTaskEither`
    - deprecate `run`
  - `ReadonlyArray`
    - deprecate `prependToAll`, use `prependAll` instead
  - `ReadonlyNonEmptyArray`
    - deprecate `fold`, use `concatAll` instead
    - deprecate `prependToAll`, use `prependAll` instead
  - `ReadonlyRecord`
    - deprecate `hasOwnProperty`, use `has` instead
  - `Record`
    - deprecate `hasOwnProperty`, use `has` instead
  - `Ring`
    - deprecate `getTupleRing`, use `tuple` instead
  - `Semigroup`
    - deprecate `semigroupAll`, use `boolean.SemigroupAll` instead
    - deprecate `semigroupAny`, use `boolean.SemigroupAny` instead
    - deprecate `getFunctionSemigroup`, use `function.getSemigroup` instead
    - deprecate `semigroupString`, use `string.Semigroup` instead
    - deprecate `semigroupSum`, use `number.SemigroupSum` instead
    - deprecate `semigroupProduct`, use `number.SemigroupProduct` instead
    - deprecate `fold`, use `concatAll` instead
    - deprecate `getIntercalateSemigroup`, use `intercalate` instead
    - deprecate `getMeetSemigroup`, use `min` instead
    - deprecate `getJoinSemigroup`, use `max` instead
    - deprecate `getDualSemigroup`, use `reverse` instead
    - deprecate `getStructSemigroup`, use `struct` instead
    - deprecate `getTupleSemigroup`, use `tuple` instead
    - deprecate `getFirstSemigroup`, use `first` instead
    - deprecate `getLastSemigroup`, use `last` instead
    - deprecate `getObjectSemigroup`, use `assign` instead
  - `Show`
    - deprecate `showBoolean`, use `boolean.Show` instead
    - deprecate `showString`, use `string.Show` instead
    - deprecate `showNumber`, use `number.Show` instead
    - deprecate `getStructShow`, use `struct` instead
    - deprecate `getTupleShow`, use `tuple` instead
  - `Task`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `fromTask`
  - `TaskEither`
    - deprecate `getApplySemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getApplyMonoid` in favour of `Applicative.getApplicativeMonoid`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `getTaskValidation`, use `getApplicativeTaskValidation` and `getAltTaskValidation` instead
  - `TaskThese`
    - deprecate `functorTaskThese` instance in favour of `Functor`
    - deprecate `bifunctorTaskThese` instance in favour of `Bifunctor`
    - deprecate `getSemigroup` in favour of `Apply.getApplySemigroup`
    - deprecate `toTuple` in favour of `toTuple2`
  - `These`
    - deprecate `toTuple` in favour of `toTuple2`
  - `Traversable`
    - deprecate `getTraversableComposition`, use `traverse`, `sequence` helpers instead
- **New Feature**
  - add `Pointed` type class
  - add `FromEither` type class
  - add `FromIO` type class
  - add `FromTask` type class
  - add `TaskOption` module
  - add `string` module
  - add `number` module
  - add `Separated` module (@YBogomolov)
  - add `Json` module
  - `Apply`
    - add `ap` helper
    - add `apS` helper
    - add `apFirst` helper
    - add `apSecond` helper
    - add `getApplySemigroup`
  - `Applicative`
    - add `getApplicativeMonoid`
  - `Array`
    - add `Pointed` instance
    - add `Apply` instance
    - add `matchLeft` alias
    - add `matchRight` alias
  - `boolean`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `Eq` instance
    - add `BooleanAlgebra` instance
    - add `SemigroupAll` instance
    - add `SemigroupAny` instance
    - add `MonoidAll` instance
    - add `MonoidAny` instance
    - add `Ord` instance
    - add `Show` instance
  - `BooleanAlgebra`
    - add `reverse`
  - `Chain`
    - add `bind` helper
    - add `chainFirst` helper
  - `Compactable`
    - add `compact` helper
    - add `separate` helper
    - add `separated` constructor
  - `Date`
    - add `Eq` instance
    - add `Ord` instance
  - `Filterable`
    - add `filter` helper
    - add `filterMap` helper
    - add `partition` helper
    - add `partitionMap` helper
  - `Foldable`
    - add `reduce` helper
    - add `foldaMap` helper
    - add `reduceRight` helper
  - `FoldableWithIndex`
    - add `reduceWithIndex` helper
    - add `foldaMapWithIndex` helper
    - add `reduceRightWithIndex` helper
  - `function`
    - add `getBooleanAlgebra`
    - add `getSemigroup`
    - add `getMonoid`
    - add `getSemiring`
    - add `getRing`
    - add `getEndomorphismMonoid`
  - `Functor`
    - add `map` helper
    - add `bindTo` helper
    - add `flap` derivable, #1393 (@williamareynolds)
  - `FunctorWithIndex`
    - add `mapWithIndex` helper
  - `Either`
    - add `Pointed` instance
    - add `Apply` instance
    - add `getCompactable`
    - add `getFilterable`
    - add `FromEither` instance
    - add `toUnion`, closes #1362
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `tryCatchK`
    - add `fromOptionK`
    - add `chainOptionK`
  - `EitherT`
    - split `getEitherM` into separated functions
  - `Eq`
    - add `getSemigroup`
    - add `struct`
    - add `tuple`
  - `Identity`
    - add `Pointed` instance
    - add `Apply` instance
  - `IO`
    - add `Pointed` instance
    - add `Apply` instance
    - add `FromIO` instance
  - `IOEither`
    - add `Pointed` instance
    - add `getCompactable`
    - add `FromIO` instance
    - add `FromEither` instance
    - add `tryCatchK` combinator
    - add `toUnion`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `fromOptionK`
    - add `chainOptionK`
  - `Monoid`
    - add `concatAll`
    - add `min`
    - add `max`
    - add `reverse`
    - add `struct`
    - add `tuple`
  - `NonEmptyArray`
    - add `Pointed` instance
    - add `Apply` instance
    - add `concatAll`
  - `Option`
    - add `Pointed` instance
    - add `Apply` instance
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `tryCatchK`
  - `OptionT`
    - split `getOptionM` into separated functions
  - `Ord`
    - add `equalsDefault`
    - add `reverse`
    - add `tuple`
  - `Ordering`
    - add `Eq` instance
    - add `Semigroup` instance
    - add `Monoid` instance
  - `Random`
    - add `randomElem`
  - `Reader`
    - add `Pointed` instance
    - add `Apply` instance
  - `ReaderT`
    - split `getReaderM` into separated functions
  - `ReaderEither`
    - add `Pointed` instance
    - add `Apply` instance
    - add `FromEither` instance
    - add `getCompactable`
    - add `getFilterable`
    - add `toUnion`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `fromOptionK`
    - add `chainOptionK`
  - `ReaderTask`
    - add `Pointed` instance
    - add `ApplyPar` instance
    - add `ApplySeq` instance
    - add `FromIO` instance
    - add `FromTask` instance
    - add `traverseSeqArrayWithIndex`
    - add `traverseSeqArray`
    - add `sequenceSeqArray`
  - `ReaderTaskEither`
    - add `Pointed` instance
    - add `ApplyPar` instance
    - add `ApplySeq` instance
    - add `FromIO` instance
    - add `FromTask` instance
    - add `FromEither` instance
    - add `getCompactable`
    - add `getFilterable`
    - add `toUnion`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `fromOptionK`
    - add `chainOptionK`
  - `ReadonlyArray`
    - add `Pointed` instance
    - add `Apply` instance
    - add `getSemigroup`
    - add `matchLeft` instance
    - add `matchRight` instance
  - `ReadonlyMap`
    - add `filterWithIndex` combinator
    - add `filterMapWithIndex` combinator
    - add `partitionWithIndex` combinator
    - add `partitionMapWithIndex` combinator
    - add `getFunctorWithIndex`
    - add `getFoldable`
    - add `getFoldableWithIndex`
    - add `getTraversable`
    - add `getTraversableWithIndex`
  - `ReadonlyNonEmptyArray`
    - add `Pointed` instance
    - add `Apply` instance
    - add `concatAll`
  - `Ring`
    - add `tuple`
  - `Semigroup`
    - add `constant`
    - add `concatAll`
    - add `intercalate`
    - add `min`
    - add `max`
    - add `struct`
    - add `tuple`
    - add `first`
    - add `last`
    - add `assign`
  - `Show`
    - add `struct`
    - add `tuple`
  - `State`
    - add `Pointed` instance
    - add `Apply` instance
  - `StateT`
    - split `getStateM` into separated functions
  - `StateReaderTaskEither`
    - add `Pointed` instance
    - add `Apply` instance
    - add `Monad` instance
    - add `FromIO` instance
    - add `FromTask` instance
    - add `FromEither` instance
    - add `fromOptionK`
    - add `chainOptionK`
  - `Task`
    - add `Pointed` instance
    - add `ApplyPar` instance
    - add `ApplySeq` instance
    - add `FromIO` instance
    - add `FromTask` instance
  - `TaskEither`
    - add `Pointed` instance
    - add `ApplyPar` instance
    - add `ApplySeq` instance
    - add `getCompactable`
    - add `FromIO` instance
    - add `FromTask` instance
    - add `FromEither` instance
    - add `toUnion`
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `orElseW`
    - add `fromOptionK`
    - add `chainOptionK`
  - `TaskThese`
    - add `Functor` instance
    - add `Pointed` instance
    - add `Bifunctor` instance
    - add `toTuple2`
    - add `getApply`
    - add `FromIO` instance
    - add `FromTask` instance
    - add `fromEither` function
    - add `FromEither` instance
    - add `getChain` instance
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `fromOption`
    - add `fromOptionK`
    - add `fromPredicate`
  - `These`
    - add `Pointed` instance
    - add `of` function
    - add `getApply`
    - add `toTuple2`
    - add `FromEither` instance
    - add `fromOption` constructor
    - add `foldW`
    - add `match` alias
    - add `matchW` alias
    - add `fromOptionK`
  - `TheseT`
    - split `getTheseM` into separated functions
  - `Traversable`
    - add `traverse` helper
    - add `sequence` helper
  - `Tree`
    - add `Pointed` instance
    - add `Apply` instance
  - `Writer`
    - add `getPointed`
    - add `getApply`
    - add `getApplicative`
    - add `getChain`

# 2.9.5

- **Polish**
  - `Array`, `ReadonlyArray`: change `scanLeft` and `scanRight` to return `NonEmptyArray`, `ReadonlyNonEmptyArray`, #1391 (@willheslam)

# 2.9.4

- **Bug Fix**
  - fix `filter` overloads in `NonEmptyArray` / `ReadonlyNonEmptyArray`, closes #1388 (@gcanti)

# 2.9.3

- **Polish**
  - add more `/*#__PURE__*/` comments to improve tree shaking, #1370 (@OliverJAsh)

# 2.9.2

- **Polish**
  - add more `/*#__PURE__*/` comments to improve tree shaking, #1368 (@OliverJAsh)

# 2.9.1

- **Polish**
  - `Array` / `ReadonlyArray`
    - `sort`: return the input when length <= 1, closes #1357 (@gcanti)
    - `uniq`: return the input when length <= 1 (@gcanti)

# 2.9.0

- **New Feature**

  - `Array`
    - add `altW` (@gcanti)
    - add `intersperse` (@marcotoniut)
    - add `prependToAll` (@marcotoniut)
    - add `every` (@gcanti)
    - add `some` (@gcanti)
    - add `Do` (@gcanti)
  - `Either`
    - add `altW` (@gcanti)
    - add `fromNullableK` (@gcanti)
    - add `chainNullableK` (@gcanti)
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
    - add `filterOrElseW` (@gcanti)
  - `Identity`
    - add `altW` (@gcanti)
    - add `Do` (@gcanti)
  - `IO`
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
  - `IOEither`
    - add `altW` (@gcanti)
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `traverseSeqArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseSeqArray` (@mohaalak @iamomiid)
    - add `sequenceSeqArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
    - add `filterOrElseW` (@gcanti)
  - `NonEmptyArray`
    - add `altW` (@gcanti)
    - add `uncons` (@marcotoniut)
    - add `unsnoc` (@marcotoniut)
    - add `intersperse` (@marcotoniut)
    - add `prependToAll` (@marcotoniut)
    - add `Do` (@gcanti)
  - `Option`
    - add `altW` (@gcanti)
    - add `fromNullableK` (@gcanti)
    - add `chainNullableK` (@gcanti)
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
  - `Reader`
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
  - `ReaderEither`
    - add `altW` (@gcanti)
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
    - add `filterOrElseW` (@gcanti)
  - `ReaderTask`
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `traverseSeqArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseSeqArray` (@mohaalak @iamomiid)
    - add `sequenceSeqArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
  - `ReaderTaskEither`
    - add `altW` (@gcanti)
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `traverseSeqArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseSeqArray` (@mohaalak @iamomiid)
    - add `sequenceSeqArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
    - add `filterOrElseW` (@gcanti)
  - `ReadonlyArray`
    - add `altW` (@gcanti)
    - add `intersperse` (@marcotoniut)
    - add `prependToAll` (@marcotoniut)
    - add `every` (@gcanti)
    - add `some` (@gcanti)
    - add `Do` (@gcanti)
  - `ReadonlyNonEmptyArray`
    - add `altW` (@gcanti)
    - add `uncons` (@marcotoniut)
    - add `unsnoc` (@marcotoniut)
    - add `intersperse` (@marcotoniut)
    - add `prependToAll` (@marcotoniut)
    - add `Do` (@gcanti)
  - `State`
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
  - `StateReaderTaskEither`
    - add `altW` (@gcanti)
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `filterOrElseW` (@gcanti)
  - `Task`
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `traverseSeqArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseSeqArray` (@mohaalak @iamomiid)
    - add `sequenceSeqArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
  - `TaskEither`
    - add `altW` (@gcanti)
    - add `traverseArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseArray` (@mohaalak @iamomiid)
    - add `sequenceArray` (@mohaalak @iamomiid)
    - add `traverseSeqArrayWithIndex` (@mohaalak @iamomiid)
    - add `traverseSeqArray` (@mohaalak @iamomiid)
    - add `sequenceSeqArray` (@mohaalak @iamomiid)
    - add `Do` (@gcanti)
    - add `filterOrElseW` (@gcanti)
  - `Tree`
    - add `Do` (@gcanti)

- **Deprecation**
  - `Option`
    - deprecate `mapNullable` in favour of `chainNullableK` (@gcanti)
  - `StateReaderTaskEither`
    - deprecate `stateReaderTaskEitherSeq` because is useless, `stateReaderTaskEither` is already sequential (@gcanti)

# 2.8.6

- **Bug Fix**
  - fix #1350 (@gcanti)

# 2.8.5

- **Polish**
  - `IOEither`
    - export missing `of` function (@gcanti)
  - `ReaderEither`
    - export missing `of` function (@gcanti)

# 2.8.4

- **Polish**
  - `IOEither`
    - add `ApplicativePar` instance (@gcanti)
    - add `ApplicativeSeq` instance (@gcanti)
- **Deprecation**
  - `IOEither`
    - deprecate `Applicative` in favour of `ApplicativePar` (@gcanti)

# 2.8.3

- **Polish**
  - `Reader`
    - export `Strong` instance (@urgent)
    - export `Choice` instance (@gcanti)

# 2.8.2

- **Polish**
  - increase the supported number of arguments of pipe function (@heka1024)
- **Bug fix**
  - revert `groupBy` change in #1286 (@gcanti)
- **Internal**
  - define all non-pipeable internal functions in terms of the corresponding pipeable versions (@gcanti)

# 2.8.1

- **Polish**
  - fix `HKT` typings duplication (which might break module augmentation)

# 2.8.0

- **New Feature**
  - expose `fp-ts` modules without lib/es6 prefix, #1241 (@StefanoMagrassi)
  - `Array`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
  - `Either`
    - add `apW` (@gcanti)
    - add `apS` (@gcanti)
    - add `apSW` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `bindW` (@gcanti)
    - add `chainFirstW`, #1273 (@leemhenson)
    - add `getFilterable` (@gcanti)
  - `Foldable`
    - add `toArray`, #1272 (@newswim)
    - add `reduceM` (@gcanti)
  - `Identity`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
  - `IO`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
  - `IOEither`
    - add `apS` (@gcanti)
    - add `apSW` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `bindW` (@gcanti)
    - add `chainFirstW`, #1273 (@leemhenson)
  - `NonEmptyArray`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - relax `group` signature (@gcanti)
    - relax `groupBy` signature (@gcanti)
    - relax `groupSort` signature (@gcanti)
    - relax `sort` signature (@gcanti)
  - `Option`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
  - `Reader`
    - add `apW` (@gcanti)
    - add `apS` (@gcanti)
    - add `apSW` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `bindW` (@gcanti)
  - `ReaderEither`
    - add `apS` (@gcanti)
    - add `apSW` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `bindW` (@gcanti)
    - add `chainFirstW`, #1273 (@leemhenson)
  - `ReaderTask`
    - add `apS` (@gcanti)
    - add `apSW` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `bindW` (@gcanti)
  - `ReaderTaskEither`
    - add `apS` (@gcanti)
    - add `apSW` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `bindW` (@gcanti)
    - add `chainFirstW`, #1273 (@leemhenson)
  - `ReadonlyArray`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
  - `ReadonlyNonEmptyArray`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - relax `group` signature (@gcanti)
    - relax `groupBy` signature (@gcanti)
    - relax `groupSort` signature (@gcanti)
    - relax `sort` signature (@gcanti)
  - `State`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `evaluate` (@gcanti)
    - add `execute` (@gcanti)
  - `StateReaderTaskEither`
    - add `apS` (@gcanti)
    - add `apSW` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `bindW` (@gcanti)
    - add `chainFirstW`, #1273 (@leemhenson)
    - add `evaluate` (@gcanti)
    - add `execute` (@gcanti)
  - `Task`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
  - `TaskEither`
    - add `apS` (@gcanti)
    - add `apSW` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
    - add `bindW` (@gcanti)
    - add `chainFirstW`, #1273 (@leemhenson)
  - `Tree`
    - add `apS` (@gcanti)
    - add `bind` (@gcanti)
    - add `bindTo` (@gcanti)
  - `Writer`
    - add `evaluate` (@gcanti)
    - add `execute` (@gcanti)
- **Deprecation**
  - `Foldable`
    - deprecate `foldM` in favour of `reduceM` (@gcanti)
  - `State`
    - deprecate `evalState` in favour of `evaluate` (@gcanti)
    - deprecate `execState` in favour of `execute` (@gcanti)
  - `StateReaderTaskEither`
    - deprecate `evalState` in favour of `evaluate` (@gcanti)
    - deprecate `execState` in favour of `execute` (@gcanti)
  - `Writer`
    - deprecate `evalWriter` in favour of `evaluate` (@gcanti)
    - deprecate `execWriter` in favour of `execute` (@gcanti)

# 2.7.1

- **Bug Fix**
  - `ReadonlyArray`
    - fix `FunctorWithIndex` instance name (@gcanti)
    - fix `Functor` instance name (@gcanti)
- **Polish**
  - `Array`
    - relax `sort` signature (@gcanti)
    - relax `sortBy` signature (@gcanti)
  - `Map`
    - export `mapWithIndex` (@gcanti)
  - `ReadonlyArray`
    - relax `sort` signature (@gcanti)
    - relax `sortBy` signature (@gcanti)
  - `ReadonlyMap`
    - export `mapWithIndex` (@gcanti)

# 2.7.0

- **Bug Fix**
  - `These`
    - fix `ap` implementation in `getMonad` function (@gcanti)
- **Polish**
  - improve performance of sequenceT and sequenceS, fix #1255 (@gcanti)
- **New Feature**
  - `function`
    - add `hole` (type hole simulation) (@gcanti)
  - `Array`
    - add `chainWithIndex`, #1256 (@OliverJAsh)
    - add `Functor` instance (@gcanti)
    - add `FunctorWithIndex` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Unfoldable` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `Alternative` instance (@gcanti)
    - add `Extend` instance (@gcanti)
    - add `Compactable` instance (@gcanti)
    - add `Filterable` instance (@gcanti)
    - add `FilterableWithIndex` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `FoldableWithIndex` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `TraversableWithIndex` instance (@gcanti)
    - add `Witherable` instance (@gcanti)
  - `Const`
    - add `Functor` instance (@gcanti)
    - add `Contravariant` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
  - `Either`
    - add `getApplicativeValidation` constrained instance (@gcanti)
    - add `getAltValidation` constrained instance (@gcanti)
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `Extend` instance (@gcanti)
    - add `ChainRec` instance (@gcanti)
    - add `MonadThrow` instance (@gcanti)
  - `Eq`
    - add `Contravariant` instance (@gcanti)
  - `Identity`
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `Comonad` instance (@gcanti)
    - add `ChainRec` instance (@gcanti)
  - `IO`
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `MonadIO` instance (@gcanti)
    - add `ChainRec` instance (@gcanti)
  - `IOEither`
    - add `getApplicativeIOValidation` constrained instance (@gcanti)
    - add `getAltIOValidation` constrained instance (@gcanti)
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `MonadIO` instance (@gcanti)
    - add `MonadThrow` instance (@gcanti)
  - `Map`
    - add `Functor` instance (@gcanti)
    - add `Compactable` instance (@gcanti)
    - add `Filterable` instance (@gcanti)
  - `NonEmptyArray`
    - add `Functor` instance (@gcanti)
    - add `FunctorWithIndex` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `FoldableWithIndex` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `TraversableWithIndex` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `Comonad` instance (@gcanti)
  - `Option`
    - add `Functor` instance (@gcanti)
    - add `Applicativ` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `Alternative` instance (@gcanti)
    - add `Extend` instance (@gcanti)
    - add `Compactable` instance (@gcanti)
    - add `Filterable` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `Witherable` instance (@gcanti)
    - add `MonadThrow` instance (@gcanti)
  - `Ord`
    - add `ContravariantOrd` instance (@gcanti)
  - `Reader`
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Profunctor` instance (@gcanti)
    - add `Category` instance (@gcanti)
    - add `String` instance (@gcanti)
    - add `Choice` instance (@gcanti)
  - `ReaderEither`
    - add `getApplicativeReaderValidation` constrained instance (@gcanti)
    - add `getAltReaderValidation` constrained instance (@gcanti)
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `MonadThrow` instance (@gcanti)
  - `ReaderTask`
    - add `Functor` instance (@gcanti)
    - add `ApplicativePar` instance (@gcanti)
    - add `ApplicativeSeq` instance (@gcanti)
  - `ReaderTaskEither`
    - add `getApplicativeReaderTaskValidation` constrained instance (@gcanti)
    - add `getAltReaderTaskValidation` constrained instance (@gcanti)
    - add `Functor` instance (@gcanti)
    - add `ApplicativePar` instance (@gcanti)
    - add `ApplicativeSeq` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Alt` instance (@gcanti)
  - `ReadonlyArray`
    - add `chainWithIndex`, #1256 (@OliverJAsh)
    - add `Functor` instance (@gcanti)
    - add `FunctorWithIndex` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Unfoldable` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `Alternative` instance (@gcanti)
    - add `Extend` instance (@gcanti)
    - add `Compactable` instance (@gcanti)
    - add `Filterable` instance (@gcanti)
    - add `FilterableWithIndex` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `FoldableWithIndex` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `TraversableWithIndex` instance (@gcanti)
    - add `Witherable` instance (@gcanti)
  - `ReadonlyMap`
    - add `Functor` instance (@gcanti)
    - add `Compactable` instance (@gcanti)
    - add `Filterable` instance (@gcanti)
  - `ReadonlyNonEmptyArray`
    - add `Functor` instance (@gcanti)
    - add `FunctorWithIndex` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `FoldableWithIndex` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `TraversableWithIndex` instance (@gcanti)
    - add `Alt` instance (@gcanti)
    - add `Comonad` instance (@gcanti)
  - `ReadonlyRecord`
    - add `Functor` instance (@gcanti)
    - add `FunctorWithIndex` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Compactable` instance (@gcanti)
    - add `Filterable` instance (@gcanti)
    - add `FilterableWithIndex` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `TraversableWithIndex` instance (@gcanti)
    - add `Witherable` instance (@gcanti)
  - `ReadonlyTuple`
    - add `Functor` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Semigroupoid` instance (@gcanti)
    - add `Comonad` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
  - `Record`
    - add `Functor` instance (@gcanti)
    - add `FunctorWithIndex` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Compactable` instance (@gcanti)
    - add `Filterable` instance (@gcanti)
    - add `FilterableWithIndex` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `TraversableWithIndex` instance (@gcanti)
    - add `Witherable` instance (@gcanti)
  - `State`
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
  - `Store`
    - add `Functor` instance (@gcanti)
    - add `Comonad` instance (@gcanti)
  - `StateReaderTaskEither`
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Alt` instance (@gcanti)
  - `Task`
    - add `Functor` instance (@gcanti)
    - add `ApplicativePar` instance (@gcanti)
    - add `ApplicativeSeq` instance (@gcanti)
  - `TaskEither`
    - add `getApplicativeTaskValidation` constrained instance (@gcanti)
    - add `getAltTaskValidation` constrained instance (@gcanti)
    - add `Functor` instance (@gcanti)
    - add `ApplicativePar` instance (@gcanti)
    - add `ApplicativeSeq` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Alt` instance (@gcanti)
  - `TaskThese`
    - add `Functor` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
  - `These`
    - add `getApplicative` constrained instance (@gcanti)
    - add `Functor` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
  - `Traced`
    - rename `traced` to `Functor` for consistency (@gcanti)
  - `Tree`
    - add `Functor` instance (@gcanti)
    - add `Applicative` instance (@gcanti)
    - add `Monad` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Traversable` instance (@gcanti)
    - add `Comonad` instance (@gcanti)
  - `Writer`
    - add `Functor` instance (@gcanti)
  - `Tuple`
    - add `Functor` instance (@gcanti)
    - add `Bifunctor` instance (@gcanti)
    - add `Semigroupoid` instance (@gcanti)
    - add `Comonad` instance (@gcanti)
    - add `Foldable` instance (@gcanti)
    - add `Traversable` instance (@gcanti)

# 2.6.7

- **Polish**
  - refine `Either.parseJSON` return type, #1252 (@OliverJAsh)
  - add missing `chainW` to `ReaderTask`, #1254 (@adameier)

# 2.6.6

- **Polish**
  - `Array`
    - export `unfold` (@gcanti)
    - make `lookup` data-last (@gcanti)
    - make `elem` data-last (@gcanti)
    - make `difference` data-last (@gcanti)
    - make `intersection` data-last (@gcanti)
    - make `union` data-last (@gcanti)
    - make `zip` data-last (@gcanti)
    - make `cons` data-last (@gcanti)
  - `Map`
    - make `member` data-last (@gcanti)
    - make `elem` data-last (@gcanti)
    - make `lookup` data-last (@gcanti)
    - make `lookupWithKey` data-last (@gcanti)
    - make `isSubmap` data-last (@gcanti)
  - `NonEmptyArray`
    - make `zip` data-last (@gcanti)
  - `ReadonlyArray`
    - export `unfold` (@gcanti)
    - make `lookup` data-last (@gcanti)
    - make `elem` data-last (@gcanti)
    - make `difference` data-last (@gcanti)
    - make `intersection` data-last (@gcanti)
    - make `union` data-last (@gcanti)
    - make `zip` data-last (@gcanti)
    - make `cons` data-last (@gcanti)
  - `ReadonlyMap`
    - make `member` data-last (@gcanti)
    - make `elem` data-last (@gcanti)
    - make `lookup` data-last (@gcanti)
    - make `lookupWithKey` data-last (@gcanti)
    - make `isSubmap` data-last (@gcanti)
  - `ReadonlyNonEmptyArray`
    - make `zip` data-last (@gcanti)
  - `ReadonlyRecord`
    - make `isSubrecord` data-last (@gcanti)
    - make `lookup` data-last (@gcanti)
    - make `elem` data-last (@gcanti)
  - `ReadonlySet`
    - make `isSubset` data-last (@gcanti)
    - make `elem` data-last (@gcanti)
    - make `union` data-last (@gcanti)
    - make `intersection` data-last (@gcanti)
    - make `difference` data-last (@gcanti)
  - `Record`
    - make `isSubrecord` data-last (@gcanti)
    - make `lookup` data-last (@gcanti)
    - make `elem` data-last (@gcanti)
  - `Set`
    - make `subset` data-last (@gcanti)
    - make `elem` data-last (@gcanti)
    - make `union` data-last (@gcanti)
    - make `intersection` data-last (@gcanti)
    - make `difference` data-last (@gcanti)
  - `Semigroup`
    - make `fold` data-last (@gcanti)

# 2.6.5

- **Polish**
  - export a pipeable `wither` function from all modules which admit a `Witherable` instance (@gcanti)
  - export a pipeable `wilt` function from all modules which admit a `Witherable` instance (@gcanti)

# 2.6.4

- **Bug Fix**
  - `ReadonlyMap`
    - `traverseWithIndex` should sort the keys (@gcanti)
  - `ReadonlyRecord`
    - `traverseWithIndex` should sort the keys (@gcanti)

# 2.6.3

- **Polish**
  - change `ReadonlyNonEmptyArray` definition to get better type inference (@gcanti)
  - move `pipe` to `function` module (@gcanti)
  - export `sequence` from all modules which admit a `Traversable` instance (@gcanti)
  - export a pipeable `traverse` function from all modules which admit a `Traversable` instance (@gcanti)
  - export a pipeable `traverseWithIndex` function from all modules which admit a `TraversableWithIndex` instance (@gcanti)
  - remove monad transformers imports from all modules (@gcanti)

# 2.6.2

The goal of this release is to make `fp-ts` more "tree shaking" friendly.

- **Polish**
  - add `/*@__PURE__*/` comments to pipeables (@gcanti)
  - add `/*@__PURE__*/` comments to transformers (@gcanti)
  - remove `pipeable.ts` module imports (@gcanti)

# 2.6.1

- **New Feature**
  - add W variants, closes #904 (@gcanti)
  - `Const`
    - add missing instances, #1201 (@gcanti)
  - `Date`
    - add `eqDate`, `eqMonth`, `eqYear` (@gcanti)
  - `Either`
    - add `getOrElseW` (@gcanti)
    - add `chainW` (@gcanti)
  - `Eq`
    - add `getMonoid` (@gcanti)
  - `IOEither`
    - add `getOrElseW` (@gcanti)
    - add `chainW` (@gcanti)
    - add `chainEitherKW` (@giogonzo)
  - `Option`
    - add `getOrElseW` (@gcanti)
  - `Reader`
    - add `chainW` (@gcanti)
  - `ReaderEither`
    - add `getOrElseW` (@gcanti)
    - add `chainW` (@gcanti)
    - add `chainEitherKW` (@giogonzo)
  - `ReaderTaskEither`
    - add `getOrElseW` (@gcanti)
    - add `chainW` (@gcanti)
    - add `chainEitherKW` (@giogonzo)
    - add `chainTaskEitherKW` (@giogonzo)
    - add `chainIOEitherKW` (@giogonzo)
  - `StateReaderTaskEither`
    - add `chainW` (@gcanti)
    - add `chainEitherKW` (@giogonzo)
    - add `chainTaskEitherKW` (@giogonzo)
    - add `chainReaderTaskEitherKW` (@giogonzo)
    - add `chainIOEitherKW` (@giogonzo)
  - `TaskEither`
    - add `getOrElseW` (@gcanti)
    - add `chainW` (@gcanti)
    - add `chainEitherKW` (@giogonzo)
    - add `chainIOEitherKW` (@giogonzo)
  - `Tree`
    - add `fold` function (@gcanti)

# 2.5.4

- **Polish**
  - `StateT`
    - add missing `StateM2C` and `StateM3C` (@qlonik)

# 2.5.3

- **Polish**
  - `Either`
    - add missing instances to `getValidation` (@gcanti)
  - `IOEither`
    - relax `Bifunctor2C` to `Bifunctor2` in `getIOValidation` (@gcanti)
  - `ReaderEither`
    - relax `Bifunctor3C` to `Bifunctor3` in `getReaderValidation` (@gcanti)
  - `ReaderTaskEither`
    - relax `Bifunctor3C` to `Bifunctor3` in `getReaderTaskValidation` (@gcanti)
  - `TaskEither`
    - relax `Bifunctor2C` to `Bifunctor2` in `getTaskValidation` (@gcanti)

# 2.5.1

- **New Feature**
  - `Eq`
    - add `eqStrict`, closes #965 (@gcanti)
  - `NonEmptyArray`
    - add `fold` (@vicrac)
    - add `zip`, `zipWith` and `unzip`, closes #1109 (@gcanti)
  - `Semigroup`
    - add `getIntercalateSemigroup` (@gcanti)
  - `Set`
    - add `toggle` (@ryota-ka)
  - `TaskEither`
    - add `tryCatchK` (@DenisFrezzato)
  - `These`
    - add missing `MonadThrow` instance (@gcanti)
  - `ReaderTaskEither`
    - add missing `leftReaderTask`, `rightReaderTask` functions (@gcanti)
  - `StateReaderTaskEither`
    - add missing `Bifunctor`, `Alt` instances (@gcanti)
- **Experimental**
  - add `ReadonlyArray` module (@gcanti)
  - add `ReadonlyNonEmptyArray` module (@gcanti)
  - add `ReadonlySet` module (@gcanti)
  - add `ReadonlyMap` module (@gcanti)
  - add `ReadonlyRecord` module (@gcanti)
  - add `ReadonlyTuple` module (@gcanti)

# 2.4.4

- **Polish**
  - add missing `MonadIO4` (@mlegenhausen)
  - add missing `MonadTask4` (@mlegenhausen)
  - `StateReaderTaskEither`
    - add missing `MonadTask4` instance (@mlegenhausen)
    - add missing `filterOrElse`, `fromPredicate` combinators (@mlegenhausen)

# 2.4.3

- **Bug Fix**
  - don't set `target: es6` in `tsconfig.build-es6.json`, fix #1110 (@gcanti)

# 2.4.2

- **Bug Fix**
  - fix `Invariant` definition (@gcanti)

# 2.4.1

- **Polish**
  - `NonEmptyArray`
    - add overloading to `group` managing non empty arrays, closes #831 (@gcanti)
    - `foldMap` and `foldMapWithIndex` now require a `Semigroup` instead of a `Monoid` (@gcanti)

# 2.4.0

- **New Feature**
  - add `WriterT` module, closes #1050 (@gcanti)
  - add `TheseT` module (@gcanti)
  - add `TaskThese` module (@gcanti)
  - `function`
    - add `tupled`, `untupled` functions, closes #1062 (@gcanti)
  - `IOEither`
    - add `fromEitherK`, `chainEitherK` (@gcanti)
  - `ReaderEither`
    - add `fromEitherK`, `chainEitherK` (@gcanti)
  - `ReaderTask`
    - add `run` (@gcanti)
    - add `fromIOK`, `chainIOK`, `fromTaskK`, `chainTaskK` (@gcanti)
  - `ReaderTaskEither`
    - add `fromEitherK`, `chainEitherK`, `fromIOEitherK`, `chainIOEitherK`, `fromTaskEitherK`, `chainTaskEitherK` (@gcanti)
  - `These`
    - add `swap` (@gcanti)
  - `Ord`
    - add `getMonoid` (@vicrac)
  - `Ordering`
    - add `monoidOrdering` (@gcanti)
  - `StateReaderTaskEither`
    - add `fromEitherK`, `chainEitherK`, `fromIOEitherK`, `chainIOEitherK`, `fromTaskEitherK`, `chainTaskEitherK`, `fromReaderTaskEitherK`, `chainReaderTaskEitherK` (@gcanti)
  - `Task`
    - add `fromIOK`, `chainIOK` (@gcanti)
  - `TaskEither`
    - add `fromEitherK`, `chainEitherK`, `fromIOEitherK`, `chainIOEitherK` (@gcanti)
- **Deprecation**
  - `Ord`
    - deprecate `getSemigroup` in favor of `getMonoid` (@gcanti)
  - `Ordering`
    - deprecate `semigroupOrdering` in favor of `monoidOrdering` (@gcanti)
- **Internal**
  - use native `Promise.race` in `Task.getRaceMonoid` (@gcanti)

# 2.3.1

- **Bug Fix**
  - `Array.ts`
    - fix `sortBy` failing on empty list of ords, #1046 (@vicrac)

# 2.3.0

- **New Feature**
  - add `ReaderTask` module (@sledorze)
  - `ReaderTaskEither`
    - add `getReaderTaskValidation` (@sledorze)
  - `ReaderEither`
    - add `getReaderValidation` (@gcanti)
  - `TaskEither`
    - improve `getTaskValidation` (@gcanti)
  - `IOEither`
    - improve `getIOValidation` (@gcanti)

# 2.2.0

- **New Feature**
  - add `boolean` module, closes #930 (@giogonzo)
  - add `ChainRec` instance to `IO` (@gcanti)
  - `NonEmptyArray`
    - add `init` (@steida)
    - add `Alt` instance (@gcanti)
- **Internal**
  - add new 3C variants and related overloads (@sledorze)

# 2.1.2

- **Bug Fix**
  - `fromNullable` now uses `NonNullable` in its return type, fixes #1004 (@gcanti)

# 2.1.1

- **Bug Fix**
  - add `sequenceT` and `sequenceS` overload signatures for `Kind4`, fixes #969 (@pfgray)

# 2.1.0

- **New Feature**
  - add constrained `Filterable` instance to `IOEither`, `TaskEither`, #959 (@giogonzo)

# 2.0.5

- **Bug Fix**
  - fix `PipeableApply2C` definition (@gcanti)

# 2.0.4

- **Polish**
  - `ReaderTaskEither`
    - add missing `bracket` function (@mlegenhausen)

# 2.0.3

- **Bug Fix**
  - fix `sequenceT`, `sequenceS` implementations, closes #914 (@gcanti)

# 2.0.2

- **Bug Fix**
  - add `reduce` to `FoldableComposition2C1` (@anilanar)

# 2.0.1

- **Bug Fix**
  - fix `PipeableBifunctor` definition (@gcanti)
  - fix `chunksOf` implementation, #897 (@gcanti)

# 2.0.0

- **Breaking Change**
  - remove deprecated APIs (@gcanti)
  - remove classes (@gcanti)
  - remove all phantom fields (@gcanti)
