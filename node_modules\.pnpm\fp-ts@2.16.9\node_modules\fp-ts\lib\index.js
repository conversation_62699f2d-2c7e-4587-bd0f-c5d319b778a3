"use strict";
/**
 * @since 2.0.0
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ioOption = exports.ioEither = exports.io = exports.invariant = exports.identity = exports.hkt = exports.heytingAlgebra = exports.group = exports.functorWithIndex = exports.functor = exports.function = exports.fromThese = exports.fromTask = exports.fromState = exports.fromReader = exports.fromIO = exports.fromEither = exports.foldableWithIndex = exports.foldable = exports.filterableWithIndex = exports.filterable = exports.field = exports.extend = exports.endomorphism = exports.eitherT = exports.either = exports.distributiveLattice = exports.date = exports.contravariant = exports.const = exports.console = exports.compactable = exports.comonad = exports.choice = exports.chainRec = exports.chain = exports.category = exports.boundedMeetSemilattice = exports.boundedLattice = exports.boundedJoinSemilattice = exports.boundedDistributiveLattice = exports.bounded = exports.booleanAlgebra = exports.boolean = exports.bifunctor = exports.array = exports.apply = exports.applicative = exports.alternative = exports.alt = void 0;
exports.store = exports.stateT = exports.stateReaderTaskEither = exports.state = exports.show = exports.eq = exports.set = exports.separated = exports.semiring = exports.semigroupoid = exports.semigroup = exports.ring = exports.refinement = exports.record = exports.readerTask = exports.readonlyTuple = exports.readonlySet = exports.readonlyRecord = exports.readonlyNonEmptyArray = exports.readonlyMap = exports.readonlyArray = exports.readerTaskEither = exports.readerT = exports.readerIO = exports.readerEither = exports.reader = exports.random = exports.profunctor = exports.predicate = exports.pointed = exports.pipeable = exports.ordering = exports.ord = exports.optionT = exports.option = exports.number = exports.nonEmptyArray = exports.naturalTransformation = exports.monoid = exports.monadThrow = exports.monadTask = exports.monadIO = exports.monad = exports.meetSemilattice = exports.map = exports.magma = exports.lattice = exports.json = exports.joinSemilattice = exports.ioRef = void 0;
exports.zero = exports.writerT = exports.writer = exports.witherable = exports.void = exports.validationT = exports.unfoldable = exports.tuple = exports.tree = exports.traversableWithIndex = exports.traversable = exports.traced = exports.theseT = exports.these = exports.taskThese = exports.taskOption = exports.taskEither = exports.task = exports.struct = exports.strong = exports.string = void 0;
var alt = __importStar(require("./Alt"));
exports.alt = alt;
var alternative = __importStar(require("./Alternative"));
exports.alternative = alternative;
var applicative = __importStar(require("./Applicative"));
exports.applicative = applicative;
var apply = __importStar(require("./Apply"));
exports.apply = apply;
var array = __importStar(require("./Array"));
exports.array = array;
var bifunctor = __importStar(require("./Bifunctor"));
exports.bifunctor = bifunctor;
var boolean = __importStar(require("./boolean"));
exports.boolean = boolean;
var booleanAlgebra = __importStar(require("./BooleanAlgebra"));
exports.booleanAlgebra = booleanAlgebra;
var bounded = __importStar(require("./Bounded"));
exports.bounded = bounded;
var boundedDistributiveLattice = __importStar(require("./BoundedDistributiveLattice"));
exports.boundedDistributiveLattice = boundedDistributiveLattice;
var boundedJoinSemilattice = __importStar(require("./BoundedJoinSemilattice"));
exports.boundedJoinSemilattice = boundedJoinSemilattice;
var boundedLattice = __importStar(require("./BoundedLattice"));
exports.boundedLattice = boundedLattice;
var boundedMeetSemilattice = __importStar(require("./BoundedMeetSemilattice"));
exports.boundedMeetSemilattice = boundedMeetSemilattice;
var category = __importStar(require("./Category"));
exports.category = category;
var chain = __importStar(require("./Chain"));
exports.chain = chain;
var chainRec = __importStar(require("./ChainRec"));
exports.chainRec = chainRec;
var choice = __importStar(require("./Choice"));
exports.choice = choice;
var comonad = __importStar(require("./Comonad"));
exports.comonad = comonad;
var compactable = __importStar(require("./Compactable"));
exports.compactable = compactable;
var console = __importStar(require("./Console"));
exports.console = console;
var const_ = __importStar(require("./Const"));
exports.const = const_;
var contravariant = __importStar(require("./Contravariant"));
exports.contravariant = contravariant;
var date = __importStar(require("./Date"));
exports.date = date;
var distributiveLattice = __importStar(require("./DistributiveLattice"));
exports.distributiveLattice = distributiveLattice;
var either = __importStar(require("./Either"));
exports.either = either;
var eitherT = __importStar(require("./EitherT"));
exports.eitherT = eitherT;
var endomorphism = __importStar(require("./Endomorphism"));
exports.endomorphism = endomorphism;
var eq = __importStar(require("./Eq"));
exports.eq = eq;
var extend = __importStar(require("./Extend"));
exports.extend = extend;
var field = __importStar(require("./Field"));
exports.field = field;
var filterable = __importStar(require("./Filterable"));
exports.filterable = filterable;
var filterableWithIndex = __importStar(require("./FilterableWithIndex"));
exports.filterableWithIndex = filterableWithIndex;
var foldable = __importStar(require("./Foldable"));
exports.foldable = foldable;
var foldableWithIndex = __importStar(require("./FoldableWithIndex"));
exports.foldableWithIndex = foldableWithIndex;
var fromEither = __importStar(require("./FromEither"));
exports.fromEither = fromEither;
var fromIO = __importStar(require("./FromIO"));
exports.fromIO = fromIO;
var fromReader = __importStar(require("./FromReader"));
exports.fromReader = fromReader;
var fromState = __importStar(require("./FromState"));
exports.fromState = fromState;
var fromTask = __importStar(require("./FromTask"));
exports.fromTask = fromTask;
var fromThese = __importStar(require("./FromThese"));
exports.fromThese = fromThese;
var function_ = __importStar(require("./function"));
exports.function = function_;
var functor = __importStar(require("./Functor"));
exports.functor = functor;
var functorWithIndex = __importStar(require("./FunctorWithIndex"));
exports.functorWithIndex = functorWithIndex;
var group = __importStar(require("./Group"));
exports.group = group;
var heytingAlgebra = __importStar(require("./HeytingAlgebra"));
exports.heytingAlgebra = heytingAlgebra;
var hkt = __importStar(require("./HKT"));
exports.hkt = hkt;
var identity = __importStar(require("./Identity"));
exports.identity = identity;
var invariant = __importStar(require("./Invariant"));
exports.invariant = invariant;
var io = __importStar(require("./IO"));
exports.io = io;
var ioEither = __importStar(require("./IOEither"));
exports.ioEither = ioEither;
var ioOption = __importStar(require("./IOOption"));
exports.ioOption = ioOption;
var ioRef = __importStar(require("./IORef"));
exports.ioRef = ioRef;
var joinSemilattice = __importStar(require("./JoinSemilattice"));
exports.joinSemilattice = joinSemilattice;
var json = __importStar(require("./Json"));
exports.json = json;
var lattice = __importStar(require("./Lattice"));
exports.lattice = lattice;
var magma = __importStar(require("./Magma"));
exports.magma = magma;
var map = __importStar(require("./Map"));
exports.map = map;
var meetSemilattice = __importStar(require("./MeetSemilattice"));
exports.meetSemilattice = meetSemilattice;
var monad = __importStar(require("./Monad"));
exports.monad = monad;
var monadIO = __importStar(require("./MonadIO"));
exports.monadIO = monadIO;
var monadTask = __importStar(require("./MonadTask"));
exports.monadTask = monadTask;
var monadThrow = __importStar(require("./MonadThrow"));
exports.monadThrow = monadThrow;
var monoid = __importStar(require("./Monoid"));
exports.monoid = monoid;
var naturalTransformation = __importStar(require("./NaturalTransformation"));
exports.naturalTransformation = naturalTransformation;
var nonEmptyArray = __importStar(require("./NonEmptyArray"));
exports.nonEmptyArray = nonEmptyArray;
var number = __importStar(require("./number"));
exports.number = number;
var option = __importStar(require("./Option"));
exports.option = option;
var optionT = __importStar(require("./OptionT"));
exports.optionT = optionT;
var ord = __importStar(require("./Ord"));
exports.ord = ord;
var ordering = __importStar(require("./Ordering"));
exports.ordering = ordering;
var pipeable = __importStar(require("./pipeable"));
exports.pipeable = pipeable;
var pointed = __importStar(require("./Pointed"));
exports.pointed = pointed;
var predicate = __importStar(require("./Predicate"));
exports.predicate = predicate;
var profunctor = __importStar(require("./Profunctor"));
exports.profunctor = profunctor;
var random = __importStar(require("./Random"));
exports.random = random;
var reader = __importStar(require("./Reader"));
exports.reader = reader;
var readerEither = __importStar(require("./ReaderEither"));
exports.readerEither = readerEither;
var readerIO = __importStar(require("./ReaderIO"));
exports.readerIO = readerIO;
var readerT = __importStar(require("./ReaderT"));
exports.readerT = readerT;
var readerTask = __importStar(require("./ReaderTask"));
exports.readerTask = readerTask;
var readerTaskEither = __importStar(require("./ReaderTaskEither"));
exports.readerTaskEither = readerTaskEither;
var readonlyArray = __importStar(require("./ReadonlyArray"));
exports.readonlyArray = readonlyArray;
var readonlyMap = __importStar(require("./ReadonlyMap"));
exports.readonlyMap = readonlyMap;
var readonlyNonEmptyArray = __importStar(require("./ReadonlyNonEmptyArray"));
exports.readonlyNonEmptyArray = readonlyNonEmptyArray;
var readonlyRecord = __importStar(require("./ReadonlyRecord"));
exports.readonlyRecord = readonlyRecord;
var readonlySet = __importStar(require("./ReadonlySet"));
exports.readonlySet = readonlySet;
var readonlyTuple = __importStar(require("./ReadonlyTuple"));
exports.readonlyTuple = readonlyTuple;
var record = __importStar(require("./Record"));
exports.record = record;
var refinement = __importStar(require("./Refinement"));
exports.refinement = refinement;
var ring = __importStar(require("./Ring"));
exports.ring = ring;
var semigroup = __importStar(require("./Semigroup"));
exports.semigroup = semigroup;
var semigroupoid = __importStar(require("./Semigroupoid"));
exports.semigroupoid = semigroupoid;
var semiring = __importStar(require("./Semiring"));
exports.semiring = semiring;
var separated = __importStar(require("./Separated"));
exports.separated = separated;
var set = __importStar(require("./Set"));
exports.set = set;
var show = __importStar(require("./Show"));
exports.show = show;
var state = __importStar(require("./State"));
exports.state = state;
var stateReaderTaskEither = __importStar(require("./StateReaderTaskEither"));
exports.stateReaderTaskEither = stateReaderTaskEither;
var stateT = __importStar(require("./StateT"));
exports.stateT = stateT;
var store = __importStar(require("./Store"));
exports.store = store;
var string = __importStar(require("./string"));
exports.string = string;
var strong = __importStar(require("./Strong"));
exports.strong = strong;
var struct = __importStar(require("./struct"));
exports.struct = struct;
var task = __importStar(require("./Task"));
exports.task = task;
var taskEither = __importStar(require("./TaskEither"));
exports.taskEither = taskEither;
var taskOption = __importStar(require("./TaskOption"));
exports.taskOption = taskOption;
var taskThese = __importStar(require("./TaskThese"));
exports.taskThese = taskThese;
var these = __importStar(require("./These"));
exports.these = these;
var theseT = __importStar(require("./TheseT"));
exports.theseT = theseT;
var traced = __importStar(require("./Traced"));
exports.traced = traced;
var traversable = __importStar(require("./Traversable"));
exports.traversable = traversable;
var traversableWithIndex = __importStar(require("./TraversableWithIndex"));
exports.traversableWithIndex = traversableWithIndex;
var tree = __importStar(require("./Tree"));
exports.tree = tree;
var tuple = __importStar(require("./Tuple"));
exports.tuple = tuple;
var unfoldable = __importStar(require("./Unfoldable"));
exports.unfoldable = unfoldable;
var validationT = __importStar(require("./ValidationT"));
exports.validationT = validationT;
var void_ = __importStar(require("./void"));
exports.void = void_;
var witherable = __importStar(require("./Witherable"));
exports.witherable = witherable;
var writer = __importStar(require("./Writer"));
exports.writer = writer;
var writerT = __importStar(require("./WriterT"));
exports.writerT = writerT;
var zero = __importStar(require("./Zero"));
exports.zero = zero;
