/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 5.27.1
 * source: proto/wcf.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as pb_1 from "google-protobuf";
export namespace wcf {
    export enum Functions {
        FUNC_RESERVED = 0,
        FUNC_IS_LOGIN = 1,
        FUNC_GET_SELF_WXID = 16,
        FUNC_GET_MSG_TYPES = 17,
        FUNC_GET_CONTACTS = 18,
        FUNC_GET_DB_NAMES = 19,
        FUNC_GET_DB_TABLES = 20,
        FUNC_GET_USER_INFO = 21,
        FUNC_GET_AUDIO_MSG = 22,
        FUNC_SEND_TXT = 32,
        FUNC_SEND_IMG = 33,
        FUNC_SEND_FILE = 34,
        FUNC_SEND_XML = 35,
        FUNC_SEND_EMOTION = 36,
        FUNC_SEND_RICH_TXT = 37,
        FUNC_SEND_PAT_MSG = 38,
        FUNC_FORWARD_MSG = 39,
        FUNC_ENABLE_RECV_TXT = 48,
        FUNC_DISABLE_RECV_TXT = 64,
        FUNC_EXEC_DB_QUERY = 80,
        FUNC_ACCEPT_FRIEND = 81,
        FUNC_RECV_TRANSFER = 82,
        FUNC_REFRESH_PYQ = 83,
        FUNC_DOWNLOAD_ATTACH = 84,
        FUNC_GET_CONTACT_INFO = 85,
        FUNC_REVOKE_MSG = 86,
        FUNC_REFRESH_QRCODE = 87,
        FUNC_DECRYPT_IMAGE = 96,
        FUNC_EXEC_OCR = 97,
        FUNC_ADD_ROOM_MEMBERS = 112,
        FUNC_DEL_ROOM_MEMBERS = 113,
        FUNC_INV_ROOM_MEMBERS = 114
    }
    export class Request extends pb_1.Message {
        #one_of_decls: number[][] = [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]];
        constructor(data?: any[] | ({
            func?: Functions;
        } & (({
            empty?: Empty;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: string;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: TextMsg;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: PathMsg;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: DbQuery;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: Verification;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: MemberMgmt;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: XmlMsg;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: DecPath;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: Transfer;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: string;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: boolean;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: AttachMsg;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: AudioMsg;
            rt?: never;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: RichText;
            pm?: never;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: PatMsg;
            fm?: never;
        } | {
            empty?: never;
            str?: never;
            txt?: never;
            file?: never;
            query?: never;
            v?: never;
            m?: never;
            xml?: never;
            dec?: never;
            tf?: never;
            ui64?: never;
            flag?: never;
            att?: never;
            am?: never;
            rt?: never;
            pm?: never;
            fm?: ForwardMsg;
        })))) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("func" in data && data.func != undefined) {
                    this.func = data.func;
                }
                if ("empty" in data && data.empty != undefined) {
                    this.empty = data.empty;
                }
                if ("str" in data && data.str != undefined) {
                    this.str = data.str;
                }
                if ("txt" in data && data.txt != undefined) {
                    this.txt = data.txt;
                }
                if ("file" in data && data.file != undefined) {
                    this.file = data.file;
                }
                if ("query" in data && data.query != undefined) {
                    this.query = data.query;
                }
                if ("v" in data && data.v != undefined) {
                    this.v = data.v;
                }
                if ("m" in data && data.m != undefined) {
                    this.m = data.m;
                }
                if ("xml" in data && data.xml != undefined) {
                    this.xml = data.xml;
                }
                if ("dec" in data && data.dec != undefined) {
                    this.dec = data.dec;
                }
                if ("tf" in data && data.tf != undefined) {
                    this.tf = data.tf;
                }
                if ("ui64" in data && data.ui64 != undefined) {
                    this.ui64 = data.ui64;
                }
                if ("flag" in data && data.flag != undefined) {
                    this.flag = data.flag;
                }
                if ("att" in data && data.att != undefined) {
                    this.att = data.att;
                }
                if ("am" in data && data.am != undefined) {
                    this.am = data.am;
                }
                if ("rt" in data && data.rt != undefined) {
                    this.rt = data.rt;
                }
                if ("pm" in data && data.pm != undefined) {
                    this.pm = data.pm;
                }
                if ("fm" in data && data.fm != undefined) {
                    this.fm = data.fm;
                }
            }
        }
        get func() {
            return pb_1.Message.getFieldWithDefault(this, 1, Functions.FUNC_RESERVED) as Functions;
        }
        set func(value: Functions) {
            pb_1.Message.setField(this, 1, value);
        }
        get empty() {
            return pb_1.Message.getWrapperField(this, Empty, 2) as Empty;
        }
        set empty(value: Empty) {
            pb_1.Message.setOneofWrapperField(this, 2, this.#one_of_decls[0], value);
        }
        get has_empty() {
            return pb_1.Message.getField(this, 2) != null;
        }
        get str() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set str(value: string) {
            pb_1.Message.setOneofField(this, 3, this.#one_of_decls[0], value);
        }
        get has_str() {
            return pb_1.Message.getField(this, 3) != null;
        }
        get txt() {
            return pb_1.Message.getWrapperField(this, TextMsg, 4) as TextMsg;
        }
        set txt(value: TextMsg) {
            pb_1.Message.setOneofWrapperField(this, 4, this.#one_of_decls[0], value);
        }
        get has_txt() {
            return pb_1.Message.getField(this, 4) != null;
        }
        get file() {
            return pb_1.Message.getWrapperField(this, PathMsg, 5) as PathMsg;
        }
        set file(value: PathMsg) {
            pb_1.Message.setOneofWrapperField(this, 5, this.#one_of_decls[0], value);
        }
        get has_file() {
            return pb_1.Message.getField(this, 5) != null;
        }
        get query() {
            return pb_1.Message.getWrapperField(this, DbQuery, 6) as DbQuery;
        }
        set query(value: DbQuery) {
            pb_1.Message.setOneofWrapperField(this, 6, this.#one_of_decls[0], value);
        }
        get has_query() {
            return pb_1.Message.getField(this, 6) != null;
        }
        get v() {
            return pb_1.Message.getWrapperField(this, Verification, 7) as Verification;
        }
        set v(value: Verification) {
            pb_1.Message.setOneofWrapperField(this, 7, this.#one_of_decls[0], value);
        }
        get has_v() {
            return pb_1.Message.getField(this, 7) != null;
        }
        get m() {
            return pb_1.Message.getWrapperField(this, MemberMgmt, 8) as MemberMgmt;
        }
        set m(value: MemberMgmt) {
            pb_1.Message.setOneofWrapperField(this, 8, this.#one_of_decls[0], value);
        }
        get has_m() {
            return pb_1.Message.getField(this, 8) != null;
        }
        get xml() {
            return pb_1.Message.getWrapperField(this, XmlMsg, 9) as XmlMsg;
        }
        set xml(value: XmlMsg) {
            pb_1.Message.setOneofWrapperField(this, 9, this.#one_of_decls[0], value);
        }
        get has_xml() {
            return pb_1.Message.getField(this, 9) != null;
        }
        get dec() {
            return pb_1.Message.getWrapperField(this, DecPath, 10) as DecPath;
        }
        set dec(value: DecPath) {
            pb_1.Message.setOneofWrapperField(this, 10, this.#one_of_decls[0], value);
        }
        get has_dec() {
            return pb_1.Message.getField(this, 10) != null;
        }
        get tf() {
            return pb_1.Message.getWrapperField(this, Transfer, 11) as Transfer;
        }
        set tf(value: Transfer) {
            pb_1.Message.setOneofWrapperField(this, 11, this.#one_of_decls[0], value);
        }
        get has_tf() {
            return pb_1.Message.getField(this, 11) != null;
        }
        get ui64() {
            return pb_1.Message.getFieldWithDefault(this, 12, "0") as string;
        }
        set ui64(value: string) {
            pb_1.Message.setOneofField(this, 12, this.#one_of_decls[0], value);
        }
        get has_ui64() {
            return pb_1.Message.getField(this, 12) != null;
        }
        get flag() {
            return pb_1.Message.getFieldWithDefault(this, 13, false) as boolean;
        }
        set flag(value: boolean) {
            pb_1.Message.setOneofField(this, 13, this.#one_of_decls[0], value);
        }
        get has_flag() {
            return pb_1.Message.getField(this, 13) != null;
        }
        get att() {
            return pb_1.Message.getWrapperField(this, AttachMsg, 14) as AttachMsg;
        }
        set att(value: AttachMsg) {
            pb_1.Message.setOneofWrapperField(this, 14, this.#one_of_decls[0], value);
        }
        get has_att() {
            return pb_1.Message.getField(this, 14) != null;
        }
        get am() {
            return pb_1.Message.getWrapperField(this, AudioMsg, 15) as AudioMsg;
        }
        set am(value: AudioMsg) {
            pb_1.Message.setOneofWrapperField(this, 15, this.#one_of_decls[0], value);
        }
        get has_am() {
            return pb_1.Message.getField(this, 15) != null;
        }
        get rt() {
            return pb_1.Message.getWrapperField(this, RichText, 16) as RichText;
        }
        set rt(value: RichText) {
            pb_1.Message.setOneofWrapperField(this, 16, this.#one_of_decls[0], value);
        }
        get has_rt() {
            return pb_1.Message.getField(this, 16) != null;
        }
        get pm() {
            return pb_1.Message.getWrapperField(this, PatMsg, 17) as PatMsg;
        }
        set pm(value: PatMsg) {
            pb_1.Message.setOneofWrapperField(this, 17, this.#one_of_decls[0], value);
        }
        get has_pm() {
            return pb_1.Message.getField(this, 17) != null;
        }
        get fm() {
            return pb_1.Message.getWrapperField(this, ForwardMsg, 18) as ForwardMsg;
        }
        set fm(value: ForwardMsg) {
            pb_1.Message.setOneofWrapperField(this, 18, this.#one_of_decls[0], value);
        }
        get has_fm() {
            return pb_1.Message.getField(this, 18) != null;
        }
        get msg() {
            const cases: {
                [index: number]: "none" | "empty" | "str" | "txt" | "file" | "query" | "v" | "m" | "xml" | "dec" | "tf" | "ui64" | "flag" | "att" | "am" | "rt" | "pm" | "fm";
            } = {
                0: "none",
                2: "empty",
                3: "str",
                4: "txt",
                5: "file",
                6: "query",
                7: "v",
                8: "m",
                9: "xml",
                10: "dec",
                11: "tf",
                12: "ui64",
                13: "flag",
                14: "att",
                15: "am",
                16: "rt",
                17: "pm",
                18: "fm"
            };
            return cases[pb_1.Message.computeOneofCase(this, [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18])];
        }
        static fromObject(data: {
            func?: Functions;
            empty?: ReturnType<typeof Empty.prototype.toObject>;
            str?: string;
            txt?: ReturnType<typeof TextMsg.prototype.toObject>;
            file?: ReturnType<typeof PathMsg.prototype.toObject>;
            query?: ReturnType<typeof DbQuery.prototype.toObject>;
            v?: ReturnType<typeof Verification.prototype.toObject>;
            m?: ReturnType<typeof MemberMgmt.prototype.toObject>;
            xml?: ReturnType<typeof XmlMsg.prototype.toObject>;
            dec?: ReturnType<typeof DecPath.prototype.toObject>;
            tf?: ReturnType<typeof Transfer.prototype.toObject>;
            ui64?: string;
            flag?: boolean;
            att?: ReturnType<typeof AttachMsg.prototype.toObject>;
            am?: ReturnType<typeof AudioMsg.prototype.toObject>;
            rt?: ReturnType<typeof RichText.prototype.toObject>;
            pm?: ReturnType<typeof PatMsg.prototype.toObject>;
            fm?: ReturnType<typeof ForwardMsg.prototype.toObject>;
        }): Request {
            const message = new Request({});
            if (data.func != null) {
                message.func = data.func;
            }
            if (data.empty != null) {
                message.empty = Empty.fromObject(data.empty);
            }
            if (data.str != null) {
                message.str = data.str;
            }
            if (data.txt != null) {
                message.txt = TextMsg.fromObject(data.txt);
            }
            if (data.file != null) {
                message.file = PathMsg.fromObject(data.file);
            }
            if (data.query != null) {
                message.query = DbQuery.fromObject(data.query);
            }
            if (data.v != null) {
                message.v = Verification.fromObject(data.v);
            }
            if (data.m != null) {
                message.m = MemberMgmt.fromObject(data.m);
            }
            if (data.xml != null) {
                message.xml = XmlMsg.fromObject(data.xml);
            }
            if (data.dec != null) {
                message.dec = DecPath.fromObject(data.dec);
            }
            if (data.tf != null) {
                message.tf = Transfer.fromObject(data.tf);
            }
            if (data.ui64 != null) {
                message.ui64 = data.ui64;
            }
            if (data.flag != null) {
                message.flag = data.flag;
            }
            if (data.att != null) {
                message.att = AttachMsg.fromObject(data.att);
            }
            if (data.am != null) {
                message.am = AudioMsg.fromObject(data.am);
            }
            if (data.rt != null) {
                message.rt = RichText.fromObject(data.rt);
            }
            if (data.pm != null) {
                message.pm = PatMsg.fromObject(data.pm);
            }
            if (data.fm != null) {
                message.fm = ForwardMsg.fromObject(data.fm);
            }
            return message;
        }
        toObject() {
            const data: {
                func?: Functions;
                empty?: ReturnType<typeof Empty.prototype.toObject>;
                str?: string;
                txt?: ReturnType<typeof TextMsg.prototype.toObject>;
                file?: ReturnType<typeof PathMsg.prototype.toObject>;
                query?: ReturnType<typeof DbQuery.prototype.toObject>;
                v?: ReturnType<typeof Verification.prototype.toObject>;
                m?: ReturnType<typeof MemberMgmt.prototype.toObject>;
                xml?: ReturnType<typeof XmlMsg.prototype.toObject>;
                dec?: ReturnType<typeof DecPath.prototype.toObject>;
                tf?: ReturnType<typeof Transfer.prototype.toObject>;
                ui64?: string;
                flag?: boolean;
                att?: ReturnType<typeof AttachMsg.prototype.toObject>;
                am?: ReturnType<typeof AudioMsg.prototype.toObject>;
                rt?: ReturnType<typeof RichText.prototype.toObject>;
                pm?: ReturnType<typeof PatMsg.prototype.toObject>;
                fm?: ReturnType<typeof ForwardMsg.prototype.toObject>;
            } = {};
            if (this.func != null) {
                data.func = this.func;
            }
            if (this.empty != null) {
                data.empty = this.empty.toObject();
            }
            if (this.str != null) {
                data.str = this.str;
            }
            if (this.txt != null) {
                data.txt = this.txt.toObject();
            }
            if (this.file != null) {
                data.file = this.file.toObject();
            }
            if (this.query != null) {
                data.query = this.query.toObject();
            }
            if (this.v != null) {
                data.v = this.v.toObject();
            }
            if (this.m != null) {
                data.m = this.m.toObject();
            }
            if (this.xml != null) {
                data.xml = this.xml.toObject();
            }
            if (this.dec != null) {
                data.dec = this.dec.toObject();
            }
            if (this.tf != null) {
                data.tf = this.tf.toObject();
            }
            if (this.ui64 != null) {
                data.ui64 = this.ui64;
            }
            if (this.flag != null) {
                data.flag = this.flag;
            }
            if (this.att != null) {
                data.att = this.att.toObject();
            }
            if (this.am != null) {
                data.am = this.am.toObject();
            }
            if (this.rt != null) {
                data.rt = this.rt.toObject();
            }
            if (this.pm != null) {
                data.pm = this.pm.toObject();
            }
            if (this.fm != null) {
                data.fm = this.fm.toObject();
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.func != Functions.FUNC_RESERVED)
                writer.writeEnum(1, this.func);
            if (this.has_empty)
                writer.writeMessage(2, this.empty, () => this.empty.serialize(writer));
            if (this.has_str)
                writer.writeString(3, this.str);
            if (this.has_txt)
                writer.writeMessage(4, this.txt, () => this.txt.serialize(writer));
            if (this.has_file)
                writer.writeMessage(5, this.file, () => this.file.serialize(writer));
            if (this.has_query)
                writer.writeMessage(6, this.query, () => this.query.serialize(writer));
            if (this.has_v)
                writer.writeMessage(7, this.v, () => this.v.serialize(writer));
            if (this.has_m)
                writer.writeMessage(8, this.m, () => this.m.serialize(writer));
            if (this.has_xml)
                writer.writeMessage(9, this.xml, () => this.xml.serialize(writer));
            if (this.has_dec)
                writer.writeMessage(10, this.dec, () => this.dec.serialize(writer));
            if (this.has_tf)
                writer.writeMessage(11, this.tf, () => this.tf.serialize(writer));
            if (this.has_ui64)
                writer.writeUint64String(12, this.ui64);
            if (this.has_flag)
                writer.writeBool(13, this.flag);
            if (this.has_att)
                writer.writeMessage(14, this.att, () => this.att.serialize(writer));
            if (this.has_am)
                writer.writeMessage(15, this.am, () => this.am.serialize(writer));
            if (this.has_rt)
                writer.writeMessage(16, this.rt, () => this.rt.serialize(writer));
            if (this.has_pm)
                writer.writeMessage(17, this.pm, () => this.pm.serialize(writer));
            if (this.has_fm)
                writer.writeMessage(18, this.fm, () => this.fm.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Request {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Request();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.func = reader.readEnum();
                        break;
                    case 2:
                        reader.readMessage(message.empty, () => message.empty = Empty.deserialize(reader));
                        break;
                    case 3:
                        message.str = reader.readString();
                        break;
                    case 4:
                        reader.readMessage(message.txt, () => message.txt = TextMsg.deserialize(reader));
                        break;
                    case 5:
                        reader.readMessage(message.file, () => message.file = PathMsg.deserialize(reader));
                        break;
                    case 6:
                        reader.readMessage(message.query, () => message.query = DbQuery.deserialize(reader));
                        break;
                    case 7:
                        reader.readMessage(message.v, () => message.v = Verification.deserialize(reader));
                        break;
                    case 8:
                        reader.readMessage(message.m, () => message.m = MemberMgmt.deserialize(reader));
                        break;
                    case 9:
                        reader.readMessage(message.xml, () => message.xml = XmlMsg.deserialize(reader));
                        break;
                    case 10:
                        reader.readMessage(message.dec, () => message.dec = DecPath.deserialize(reader));
                        break;
                    case 11:
                        reader.readMessage(message.tf, () => message.tf = Transfer.deserialize(reader));
                        break;
                    case 12:
                        message.ui64 = reader.readUint64String();
                        break;
                    case 13:
                        message.flag = reader.readBool();
                        break;
                    case 14:
                        reader.readMessage(message.att, () => message.att = AttachMsg.deserialize(reader));
                        break;
                    case 15:
                        reader.readMessage(message.am, () => message.am = AudioMsg.deserialize(reader));
                        break;
                    case 16:
                        reader.readMessage(message.rt, () => message.rt = RichText.deserialize(reader));
                        break;
                    case 17:
                        reader.readMessage(message.pm, () => message.pm = PatMsg.deserialize(reader));
                        break;
                    case 18:
                        reader.readMessage(message.fm, () => message.fm = ForwardMsg.deserialize(reader));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Request {
            return Request.deserialize(bytes);
        }
    }
    export class Response extends pb_1.Message {
        #one_of_decls: number[][] = [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11]];
        constructor(data?: any[] | ({
            func?: Functions;
        } & (({
            status?: number;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: string;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: WxMsg;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: MsgTypes;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: RpcContacts;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: DbNames;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: DbTables;
            rows?: never;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: DbRows;
            ui?: never;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: UserInfo;
            ocr?: never;
        } | {
            status?: never;
            str?: never;
            wxmsg?: never;
            types?: never;
            contacts?: never;
            dbs?: never;
            tables?: never;
            rows?: never;
            ui?: never;
            ocr?: OcrMsg;
        })))) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("func" in data && data.func != undefined) {
                    this.func = data.func;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("str" in data && data.str != undefined) {
                    this.str = data.str;
                }
                if ("wxmsg" in data && data.wxmsg != undefined) {
                    this.wxmsg = data.wxmsg;
                }
                if ("types" in data && data.types != undefined) {
                    this.types = data.types;
                }
                if ("contacts" in data && data.contacts != undefined) {
                    this.contacts = data.contacts;
                }
                if ("dbs" in data && data.dbs != undefined) {
                    this.dbs = data.dbs;
                }
                if ("tables" in data && data.tables != undefined) {
                    this.tables = data.tables;
                }
                if ("rows" in data && data.rows != undefined) {
                    this.rows = data.rows;
                }
                if ("ui" in data && data.ui != undefined) {
                    this.ui = data.ui;
                }
                if ("ocr" in data && data.ocr != undefined) {
                    this.ocr = data.ocr;
                }
            }
        }
        get func() {
            return pb_1.Message.getFieldWithDefault(this, 1, Functions.FUNC_RESERVED) as Functions;
        }
        set func(value: Functions) {
            pb_1.Message.setField(this, 1, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set status(value: number) {
            pb_1.Message.setOneofField(this, 2, this.#one_of_decls[0], value);
        }
        get has_status() {
            return pb_1.Message.getField(this, 2) != null;
        }
        get str() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set str(value: string) {
            pb_1.Message.setOneofField(this, 3, this.#one_of_decls[0], value);
        }
        get has_str() {
            return pb_1.Message.getField(this, 3) != null;
        }
        get wxmsg() {
            return pb_1.Message.getWrapperField(this, WxMsg, 4) as WxMsg;
        }
        set wxmsg(value: WxMsg) {
            pb_1.Message.setOneofWrapperField(this, 4, this.#one_of_decls[0], value);
        }
        get has_wxmsg() {
            return pb_1.Message.getField(this, 4) != null;
        }
        get types() {
            return pb_1.Message.getWrapperField(this, MsgTypes, 5) as MsgTypes;
        }
        set types(value: MsgTypes) {
            pb_1.Message.setOneofWrapperField(this, 5, this.#one_of_decls[0], value);
        }
        get has_types() {
            return pb_1.Message.getField(this, 5) != null;
        }
        get contacts() {
            return pb_1.Message.getWrapperField(this, RpcContacts, 6) as RpcContacts;
        }
        set contacts(value: RpcContacts) {
            pb_1.Message.setOneofWrapperField(this, 6, this.#one_of_decls[0], value);
        }
        get has_contacts() {
            return pb_1.Message.getField(this, 6) != null;
        }
        get dbs() {
            return pb_1.Message.getWrapperField(this, DbNames, 7) as DbNames;
        }
        set dbs(value: DbNames) {
            pb_1.Message.setOneofWrapperField(this, 7, this.#one_of_decls[0], value);
        }
        get has_dbs() {
            return pb_1.Message.getField(this, 7) != null;
        }
        get tables() {
            return pb_1.Message.getWrapperField(this, DbTables, 8) as DbTables;
        }
        set tables(value: DbTables) {
            pb_1.Message.setOneofWrapperField(this, 8, this.#one_of_decls[0], value);
        }
        get has_tables() {
            return pb_1.Message.getField(this, 8) != null;
        }
        get rows() {
            return pb_1.Message.getWrapperField(this, DbRows, 9) as DbRows;
        }
        set rows(value: DbRows) {
            pb_1.Message.setOneofWrapperField(this, 9, this.#one_of_decls[0], value);
        }
        get has_rows() {
            return pb_1.Message.getField(this, 9) != null;
        }
        get ui() {
            return pb_1.Message.getWrapperField(this, UserInfo, 10) as UserInfo;
        }
        set ui(value: UserInfo) {
            pb_1.Message.setOneofWrapperField(this, 10, this.#one_of_decls[0], value);
        }
        get has_ui() {
            return pb_1.Message.getField(this, 10) != null;
        }
        get ocr() {
            return pb_1.Message.getWrapperField(this, OcrMsg, 11) as OcrMsg;
        }
        set ocr(value: OcrMsg) {
            pb_1.Message.setOneofWrapperField(this, 11, this.#one_of_decls[0], value);
        }
        get has_ocr() {
            return pb_1.Message.getField(this, 11) != null;
        }
        get msg() {
            const cases: {
                [index: number]: "none" | "status" | "str" | "wxmsg" | "types" | "contacts" | "dbs" | "tables" | "rows" | "ui" | "ocr";
            } = {
                0: "none",
                2: "status",
                3: "str",
                4: "wxmsg",
                5: "types",
                6: "contacts",
                7: "dbs",
                8: "tables",
                9: "rows",
                10: "ui",
                11: "ocr"
            };
            return cases[pb_1.Message.computeOneofCase(this, [2, 3, 4, 5, 6, 7, 8, 9, 10, 11])];
        }
        static fromObject(data: {
            func?: Functions;
            status?: number;
            str?: string;
            wxmsg?: ReturnType<typeof WxMsg.prototype.toObject>;
            types?: ReturnType<typeof MsgTypes.prototype.toObject>;
            contacts?: ReturnType<typeof RpcContacts.prototype.toObject>;
            dbs?: ReturnType<typeof DbNames.prototype.toObject>;
            tables?: ReturnType<typeof DbTables.prototype.toObject>;
            rows?: ReturnType<typeof DbRows.prototype.toObject>;
            ui?: ReturnType<typeof UserInfo.prototype.toObject>;
            ocr?: ReturnType<typeof OcrMsg.prototype.toObject>;
        }): Response {
            const message = new Response({});
            if (data.func != null) {
                message.func = data.func;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.str != null) {
                message.str = data.str;
            }
            if (data.wxmsg != null) {
                message.wxmsg = WxMsg.fromObject(data.wxmsg);
            }
            if (data.types != null) {
                message.types = MsgTypes.fromObject(data.types);
            }
            if (data.contacts != null) {
                message.contacts = RpcContacts.fromObject(data.contacts);
            }
            if (data.dbs != null) {
                message.dbs = DbNames.fromObject(data.dbs);
            }
            if (data.tables != null) {
                message.tables = DbTables.fromObject(data.tables);
            }
            if (data.rows != null) {
                message.rows = DbRows.fromObject(data.rows);
            }
            if (data.ui != null) {
                message.ui = UserInfo.fromObject(data.ui);
            }
            if (data.ocr != null) {
                message.ocr = OcrMsg.fromObject(data.ocr);
            }
            return message;
        }
        toObject() {
            const data: {
                func?: Functions;
                status?: number;
                str?: string;
                wxmsg?: ReturnType<typeof WxMsg.prototype.toObject>;
                types?: ReturnType<typeof MsgTypes.prototype.toObject>;
                contacts?: ReturnType<typeof RpcContacts.prototype.toObject>;
                dbs?: ReturnType<typeof DbNames.prototype.toObject>;
                tables?: ReturnType<typeof DbTables.prototype.toObject>;
                rows?: ReturnType<typeof DbRows.prototype.toObject>;
                ui?: ReturnType<typeof UserInfo.prototype.toObject>;
                ocr?: ReturnType<typeof OcrMsg.prototype.toObject>;
            } = {};
            if (this.func != null) {
                data.func = this.func;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.str != null) {
                data.str = this.str;
            }
            if (this.wxmsg != null) {
                data.wxmsg = this.wxmsg.toObject();
            }
            if (this.types != null) {
                data.types = this.types.toObject();
            }
            if (this.contacts != null) {
                data.contacts = this.contacts.toObject();
            }
            if (this.dbs != null) {
                data.dbs = this.dbs.toObject();
            }
            if (this.tables != null) {
                data.tables = this.tables.toObject();
            }
            if (this.rows != null) {
                data.rows = this.rows.toObject();
            }
            if (this.ui != null) {
                data.ui = this.ui.toObject();
            }
            if (this.ocr != null) {
                data.ocr = this.ocr.toObject();
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.func != Functions.FUNC_RESERVED)
                writer.writeEnum(1, this.func);
            if (this.has_status)
                writer.writeInt32(2, this.status);
            if (this.has_str)
                writer.writeString(3, this.str);
            if (this.has_wxmsg)
                writer.writeMessage(4, this.wxmsg, () => this.wxmsg.serialize(writer));
            if (this.has_types)
                writer.writeMessage(5, this.types, () => this.types.serialize(writer));
            if (this.has_contacts)
                writer.writeMessage(6, this.contacts, () => this.contacts.serialize(writer));
            if (this.has_dbs)
                writer.writeMessage(7, this.dbs, () => this.dbs.serialize(writer));
            if (this.has_tables)
                writer.writeMessage(8, this.tables, () => this.tables.serialize(writer));
            if (this.has_rows)
                writer.writeMessage(9, this.rows, () => this.rows.serialize(writer));
            if (this.has_ui)
                writer.writeMessage(10, this.ui, () => this.ui.serialize(writer));
            if (this.has_ocr)
                writer.writeMessage(11, this.ocr, () => this.ocr.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Response {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Response();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.func = reader.readEnum();
                        break;
                    case 2:
                        message.status = reader.readInt32();
                        break;
                    case 3:
                        message.str = reader.readString();
                        break;
                    case 4:
                        reader.readMessage(message.wxmsg, () => message.wxmsg = WxMsg.deserialize(reader));
                        break;
                    case 5:
                        reader.readMessage(message.types, () => message.types = MsgTypes.deserialize(reader));
                        break;
                    case 6:
                        reader.readMessage(message.contacts, () => message.contacts = RpcContacts.deserialize(reader));
                        break;
                    case 7:
                        reader.readMessage(message.dbs, () => message.dbs = DbNames.deserialize(reader));
                        break;
                    case 8:
                        reader.readMessage(message.tables, () => message.tables = DbTables.deserialize(reader));
                        break;
                    case 9:
                        reader.readMessage(message.rows, () => message.rows = DbRows.deserialize(reader));
                        break;
                    case 10:
                        reader.readMessage(message.ui, () => message.ui = UserInfo.deserialize(reader));
                        break;
                    case 11:
                        reader.readMessage(message.ocr, () => message.ocr = OcrMsg.deserialize(reader));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Response {
            return Response.deserialize(bytes);
        }
    }
    export class Empty extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {}) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") { }
        }
        static fromObject(data: {}): Empty {
            const message = new Empty({});
            return message;
        }
        toObject() {
            const data: {} = {};
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Empty {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Empty();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Empty {
            return Empty.deserialize(bytes);
        }
    }
    export class WxMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            is_self?: boolean;
            is_group?: boolean;
            id?: string;
            type?: number;
            ts?: number;
            roomid?: string;
            content?: string;
            sender?: string;
            sign?: string;
            thumb?: string;
            extra?: string;
            xml?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("is_self" in data && data.is_self != undefined) {
                    this.is_self = data.is_self;
                }
                if ("is_group" in data && data.is_group != undefined) {
                    this.is_group = data.is_group;
                }
                if ("id" in data && data.id != undefined) {
                    this.id = data.id;
                }
                if ("type" in data && data.type != undefined) {
                    this.type = data.type;
                }
                if ("ts" in data && data.ts != undefined) {
                    this.ts = data.ts;
                }
                if ("roomid" in data && data.roomid != undefined) {
                    this.roomid = data.roomid;
                }
                if ("content" in data && data.content != undefined) {
                    this.content = data.content;
                }
                if ("sender" in data && data.sender != undefined) {
                    this.sender = data.sender;
                }
                if ("sign" in data && data.sign != undefined) {
                    this.sign = data.sign;
                }
                if ("thumb" in data && data.thumb != undefined) {
                    this.thumb = data.thumb;
                }
                if ("extra" in data && data.extra != undefined) {
                    this.extra = data.extra;
                }
                if ("xml" in data && data.xml != undefined) {
                    this.xml = data.xml;
                }
            }
        }
        get is_self() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set is_self(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        get is_group() {
            return pb_1.Message.getFieldWithDefault(this, 2, false) as boolean;
        }
        set is_group(value: boolean) {
            pb_1.Message.setField(this, 2, value);
        }
        get id() {
            return pb_1.Message.getFieldWithDefault(this, 3, "0") as string;
        }
        set id(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get type() {
            return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
        }
        set type(value: number) {
            pb_1.Message.setField(this, 4, value);
        }
        get ts() {
            return pb_1.Message.getFieldWithDefault(this, 5, 0) as number;
        }
        set ts(value: number) {
            pb_1.Message.setField(this, 5, value);
        }
        get roomid() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set roomid(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get content() {
            return pb_1.Message.getFieldWithDefault(this, 7, "") as string;
        }
        set content(value: string) {
            pb_1.Message.setField(this, 7, value);
        }
        get sender() {
            return pb_1.Message.getFieldWithDefault(this, 8, "") as string;
        }
        set sender(value: string) {
            pb_1.Message.setField(this, 8, value);
        }
        get sign() {
            return pb_1.Message.getFieldWithDefault(this, 9, "") as string;
        }
        set sign(value: string) {
            pb_1.Message.setField(this, 9, value);
        }
        get thumb() {
            return pb_1.Message.getFieldWithDefault(this, 10, "") as string;
        }
        set thumb(value: string) {
            pb_1.Message.setField(this, 10, value);
        }
        get extra() {
            return pb_1.Message.getFieldWithDefault(this, 11, "") as string;
        }
        set extra(value: string) {
            pb_1.Message.setField(this, 11, value);
        }
        get xml() {
            return pb_1.Message.getFieldWithDefault(this, 12, "") as string;
        }
        set xml(value: string) {
            pb_1.Message.setField(this, 12, value);
        }
        static fromObject(data: {
            is_self?: boolean;
            is_group?: boolean;
            id?: string;
            type?: number;
            ts?: number;
            roomid?: string;
            content?: string;
            sender?: string;
            sign?: string;
            thumb?: string;
            extra?: string;
            xml?: string;
        }): WxMsg {
            const message = new WxMsg({});
            if (data.is_self != null) {
                message.is_self = data.is_self;
            }
            if (data.is_group != null) {
                message.is_group = data.is_group;
            }
            if (data.id != null) {
                message.id = data.id;
            }
            if (data.type != null) {
                message.type = data.type;
            }
            if (data.ts != null) {
                message.ts = data.ts;
            }
            if (data.roomid != null) {
                message.roomid = data.roomid;
            }
            if (data.content != null) {
                message.content = data.content;
            }
            if (data.sender != null) {
                message.sender = data.sender;
            }
            if (data.sign != null) {
                message.sign = data.sign;
            }
            if (data.thumb != null) {
                message.thumb = data.thumb;
            }
            if (data.extra != null) {
                message.extra = data.extra;
            }
            if (data.xml != null) {
                message.xml = data.xml;
            }
            return message;
        }
        toObject() {
            const data: {
                is_self?: boolean;
                is_group?: boolean;
                id?: string;
                type?: number;
                ts?: number;
                roomid?: string;
                content?: string;
                sender?: string;
                sign?: string;
                thumb?: string;
                extra?: string;
                xml?: string;
            } = {};
            if (this.is_self != null) {
                data.is_self = this.is_self;
            }
            if (this.is_group != null) {
                data.is_group = this.is_group;
            }
            if (this.id != null) {
                data.id = this.id;
            }
            if (this.type != null) {
                data.type = this.type;
            }
            if (this.ts != null) {
                data.ts = this.ts;
            }
            if (this.roomid != null) {
                data.roomid = this.roomid;
            }
            if (this.content != null) {
                data.content = this.content;
            }
            if (this.sender != null) {
                data.sender = this.sender;
            }
            if (this.sign != null) {
                data.sign = this.sign;
            }
            if (this.thumb != null) {
                data.thumb = this.thumb;
            }
            if (this.extra != null) {
                data.extra = this.extra;
            }
            if (this.xml != null) {
                data.xml = this.xml;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.is_self != false)
                writer.writeBool(1, this.is_self);
            if (this.is_group != false)
                writer.writeBool(2, this.is_group);
            if (this.id != "0")
                writer.writeUint64String(3, this.id);
            if (this.type != 0)
                writer.writeUint32(4, this.type);
            if (this.ts != 0)
                writer.writeUint32(5, this.ts);
            if (this.roomid.length)
                writer.writeString(6, this.roomid);
            if (this.content.length)
                writer.writeString(7, this.content);
            if (this.sender.length)
                writer.writeString(8, this.sender);
            if (this.sign.length)
                writer.writeString(9, this.sign);
            if (this.thumb.length)
                writer.writeString(10, this.thumb);
            if (this.extra.length)
                writer.writeString(11, this.extra);
            if (this.xml.length)
                writer.writeString(12, this.xml);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): WxMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new WxMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.is_self = reader.readBool();
                        break;
                    case 2:
                        message.is_group = reader.readBool();
                        break;
                    case 3:
                        message.id = reader.readUint64String();
                        break;
                    case 4:
                        message.type = reader.readUint32();
                        break;
                    case 5:
                        message.ts = reader.readUint32();
                        break;
                    case 6:
                        message.roomid = reader.readString();
                        break;
                    case 7:
                        message.content = reader.readString();
                        break;
                    case 8:
                        message.sender = reader.readString();
                        break;
                    case 9:
                        message.sign = reader.readString();
                        break;
                    case 10:
                        message.thumb = reader.readString();
                        break;
                    case 11:
                        message.extra = reader.readString();
                        break;
                    case 12:
                        message.xml = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): WxMsg {
            return WxMsg.deserialize(bytes);
        }
    }
    export class TextMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            msg?: string;
            receiver?: string;
            aters?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("msg" in data && data.msg != undefined) {
                    this.msg = data.msg;
                }
                if ("receiver" in data && data.receiver != undefined) {
                    this.receiver = data.receiver;
                }
                if ("aters" in data && data.aters != undefined) {
                    this.aters = data.aters;
                }
            }
        }
        get msg() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set msg(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get receiver() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set receiver(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get aters() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set aters(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            msg?: string;
            receiver?: string;
            aters?: string;
        }): TextMsg {
            const message = new TextMsg({});
            if (data.msg != null) {
                message.msg = data.msg;
            }
            if (data.receiver != null) {
                message.receiver = data.receiver;
            }
            if (data.aters != null) {
                message.aters = data.aters;
            }
            return message;
        }
        toObject() {
            const data: {
                msg?: string;
                receiver?: string;
                aters?: string;
            } = {};
            if (this.msg != null) {
                data.msg = this.msg;
            }
            if (this.receiver != null) {
                data.receiver = this.receiver;
            }
            if (this.aters != null) {
                data.aters = this.aters;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.msg.length)
                writer.writeString(1, this.msg);
            if (this.receiver.length)
                writer.writeString(2, this.receiver);
            if (this.aters.length)
                writer.writeString(3, this.aters);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): TextMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new TextMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.msg = reader.readString();
                        break;
                    case 2:
                        message.receiver = reader.readString();
                        break;
                    case 3:
                        message.aters = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): TextMsg {
            return TextMsg.deserialize(bytes);
        }
    }
    export class PathMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            path?: string;
            receiver?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("path" in data && data.path != undefined) {
                    this.path = data.path;
                }
                if ("receiver" in data && data.receiver != undefined) {
                    this.receiver = data.receiver;
                }
            }
        }
        get path() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set path(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get receiver() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set receiver(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            path?: string;
            receiver?: string;
        }): PathMsg {
            const message = new PathMsg({});
            if (data.path != null) {
                message.path = data.path;
            }
            if (data.receiver != null) {
                message.receiver = data.receiver;
            }
            return message;
        }
        toObject() {
            const data: {
                path?: string;
                receiver?: string;
            } = {};
            if (this.path != null) {
                data.path = this.path;
            }
            if (this.receiver != null) {
                data.receiver = this.receiver;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.path.length)
                writer.writeString(1, this.path);
            if (this.receiver.length)
                writer.writeString(2, this.receiver);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PathMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PathMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.path = reader.readString();
                        break;
                    case 2:
                        message.receiver = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PathMsg {
            return PathMsg.deserialize(bytes);
        }
    }
    export class XmlMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            receiver?: string;
            content?: string;
            path?: string;
            type?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("receiver" in data && data.receiver != undefined) {
                    this.receiver = data.receiver;
                }
                if ("content" in data && data.content != undefined) {
                    this.content = data.content;
                }
                if ("path" in data && data.path != undefined) {
                    this.path = data.path;
                }
                if ("type" in data && data.type != undefined) {
                    this.type = data.type;
                }
            }
        }
        get receiver() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set receiver(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get content() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set content(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get path() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set path(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get type() {
            return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
        }
        set type(value: number) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            receiver?: string;
            content?: string;
            path?: string;
            type?: number;
        }): XmlMsg {
            const message = new XmlMsg({});
            if (data.receiver != null) {
                message.receiver = data.receiver;
            }
            if (data.content != null) {
                message.content = data.content;
            }
            if (data.path != null) {
                message.path = data.path;
            }
            if (data.type != null) {
                message.type = data.type;
            }
            return message;
        }
        toObject() {
            const data: {
                receiver?: string;
                content?: string;
                path?: string;
                type?: number;
            } = {};
            if (this.receiver != null) {
                data.receiver = this.receiver;
            }
            if (this.content != null) {
                data.content = this.content;
            }
            if (this.path != null) {
                data.path = this.path;
            }
            if (this.type != null) {
                data.type = this.type;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.receiver.length)
                writer.writeString(1, this.receiver);
            if (this.content.length)
                writer.writeString(2, this.content);
            if (this.path.length)
                writer.writeString(3, this.path);
            if (this.type != 0)
                writer.writeUint64(4, this.type);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): XmlMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new XmlMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.receiver = reader.readString();
                        break;
                    case 2:
                        message.content = reader.readString();
                        break;
                    case 3:
                        message.path = reader.readString();
                        break;
                    case 4:
                        message.type = reader.readUint64();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): XmlMsg {
            return XmlMsg.deserialize(bytes);
        }
    }
    export class MsgTypes extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            types?: Map<number, string>;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("types" in data && data.types != undefined) {
                    this.types = data.types;
                }
            }
            if (!this.types)
                this.types = new Map();
        }
        get types() {
            return pb_1.Message.getField(this, 1) as any as Map<number, string>;
        }
        set types(value: Map<number, string>) {
            pb_1.Message.setField(this, 1, value as any);
        }
        static fromObject(data: {
            types?: {
                [key: number]: string;
            };
        }): MsgTypes {
            const message = new MsgTypes({});
            if (typeof data.types == "object") {
                message.types = new Map(Object.entries(data.types).map(([key, value]) => [Number(key), value]));
            }
            return message;
        }
        toObject() {
            const data: {
                types?: {
                    [key: number]: string;
                };
            } = {};
            if (this.types != null) {
                data.types = (Object.fromEntries)(this.types);
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            for (const [key, value] of this.types) {
                writer.writeMessage(1, this.types, () => {
                    writer.writeInt32(1, key);
                    writer.writeString(2, value);
                });
            }
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MsgTypes {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new MsgTypes();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message, () => pb_1.Map.deserializeBinary(message.types as any, reader, reader.readInt32, reader.readString));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): MsgTypes {
            return MsgTypes.deserialize(bytes);
        }
    }
    export class RpcContact extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            wxid?: string;
            code?: string;
            remark?: string;
            name?: string;
            country?: string;
            province?: string;
            city?: string;
            gender?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("wxid" in data && data.wxid != undefined) {
                    this.wxid = data.wxid;
                }
                if ("code" in data && data.code != undefined) {
                    this.code = data.code;
                }
                if ("remark" in data && data.remark != undefined) {
                    this.remark = data.remark;
                }
                if ("name" in data && data.name != undefined) {
                    this.name = data.name;
                }
                if ("country" in data && data.country != undefined) {
                    this.country = data.country;
                }
                if ("province" in data && data.province != undefined) {
                    this.province = data.province;
                }
                if ("city" in data && data.city != undefined) {
                    this.city = data.city;
                }
                if ("gender" in data && data.gender != undefined) {
                    this.gender = data.gender;
                }
            }
        }
        get wxid() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set wxid(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get code() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set code(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get remark() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set remark(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get name() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set name(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get country() {
            return pb_1.Message.getFieldWithDefault(this, 5, "") as string;
        }
        set country(value: string) {
            pb_1.Message.setField(this, 5, value);
        }
        get province() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set province(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get city() {
            return pb_1.Message.getFieldWithDefault(this, 7, "") as string;
        }
        set city(value: string) {
            pb_1.Message.setField(this, 7, value);
        }
        get gender() {
            return pb_1.Message.getFieldWithDefault(this, 8, 0) as number;
        }
        set gender(value: number) {
            pb_1.Message.setField(this, 8, value);
        }
        static fromObject(data: {
            wxid?: string;
            code?: string;
            remark?: string;
            name?: string;
            country?: string;
            province?: string;
            city?: string;
            gender?: number;
        }): RpcContact {
            const message = new RpcContact({});
            if (data.wxid != null) {
                message.wxid = data.wxid;
            }
            if (data.code != null) {
                message.code = data.code;
            }
            if (data.remark != null) {
                message.remark = data.remark;
            }
            if (data.name != null) {
                message.name = data.name;
            }
            if (data.country != null) {
                message.country = data.country;
            }
            if (data.province != null) {
                message.province = data.province;
            }
            if (data.city != null) {
                message.city = data.city;
            }
            if (data.gender != null) {
                message.gender = data.gender;
            }
            return message;
        }
        toObject() {
            const data: {
                wxid?: string;
                code?: string;
                remark?: string;
                name?: string;
                country?: string;
                province?: string;
                city?: string;
                gender?: number;
            } = {};
            if (this.wxid != null) {
                data.wxid = this.wxid;
            }
            if (this.code != null) {
                data.code = this.code;
            }
            if (this.remark != null) {
                data.remark = this.remark;
            }
            if (this.name != null) {
                data.name = this.name;
            }
            if (this.country != null) {
                data.country = this.country;
            }
            if (this.province != null) {
                data.province = this.province;
            }
            if (this.city != null) {
                data.city = this.city;
            }
            if (this.gender != null) {
                data.gender = this.gender;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.wxid.length)
                writer.writeString(1, this.wxid);
            if (this.code.length)
                writer.writeString(2, this.code);
            if (this.remark.length)
                writer.writeString(3, this.remark);
            if (this.name.length)
                writer.writeString(4, this.name);
            if (this.country.length)
                writer.writeString(5, this.country);
            if (this.province.length)
                writer.writeString(6, this.province);
            if (this.city.length)
                writer.writeString(7, this.city);
            if (this.gender != 0)
                writer.writeInt32(8, this.gender);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RpcContact {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new RpcContact();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.wxid = reader.readString();
                        break;
                    case 2:
                        message.code = reader.readString();
                        break;
                    case 3:
                        message.remark = reader.readString();
                        break;
                    case 4:
                        message.name = reader.readString();
                        break;
                    case 5:
                        message.country = reader.readString();
                        break;
                    case 6:
                        message.province = reader.readString();
                        break;
                    case 7:
                        message.city = reader.readString();
                        break;
                    case 8:
                        message.gender = reader.readInt32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): RpcContact {
            return RpcContact.deserialize(bytes);
        }
    }
    export class RpcContacts extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            contacts?: RpcContact[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("contacts" in data && data.contacts != undefined) {
                    this.contacts = data.contacts;
                }
            }
        }
        get contacts() {
            return pb_1.Message.getRepeatedWrapperField(this, RpcContact, 1) as RpcContact[];
        }
        set contacts(value: RpcContact[]) {
            pb_1.Message.setRepeatedWrapperField(this, 1, value);
        }
        static fromObject(data: {
            contacts?: ReturnType<typeof RpcContact.prototype.toObject>[];
        }): RpcContacts {
            const message = new RpcContacts({});
            if (data.contacts != null) {
                message.contacts = data.contacts.map(item => RpcContact.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                contacts?: ReturnType<typeof RpcContact.prototype.toObject>[];
            } = {};
            if (this.contacts != null) {
                data.contacts = this.contacts.map((item: RpcContact) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.contacts.length)
                writer.writeRepeatedMessage(1, this.contacts, (item: RpcContact) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RpcContacts {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new RpcContacts();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.contacts, () => pb_1.Message.addToRepeatedWrapperField(message, 1, RpcContact.deserialize(reader), RpcContact));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): RpcContacts {
            return RpcContacts.deserialize(bytes);
        }
    }
    export class DbNames extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            names?: string[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("names" in data && data.names != undefined) {
                    this.names = data.names;
                }
            }
        }
        get names() {
            return pb_1.Message.getFieldWithDefault(this, 1, []) as string[];
        }
        set names(value: string[]) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            names?: string[];
        }): DbNames {
            const message = new DbNames({});
            if (data.names != null) {
                message.names = data.names;
            }
            return message;
        }
        toObject() {
            const data: {
                names?: string[];
            } = {};
            if (this.names != null) {
                data.names = this.names;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.names.length)
                writer.writeRepeatedString(1, this.names);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbNames {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DbNames();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        pb_1.Message.addToRepeatedField(message, 1, reader.readString());
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DbNames {
            return DbNames.deserialize(bytes);
        }
    }
    export class DbTable extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            name?: string;
            sql?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("name" in data && data.name != undefined) {
                    this.name = data.name;
                }
                if ("sql" in data && data.sql != undefined) {
                    this.sql = data.sql;
                }
            }
        }
        get name() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set name(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get sql() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set sql(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            name?: string;
            sql?: string;
        }): DbTable {
            const message = new DbTable({});
            if (data.name != null) {
                message.name = data.name;
            }
            if (data.sql != null) {
                message.sql = data.sql;
            }
            return message;
        }
        toObject() {
            const data: {
                name?: string;
                sql?: string;
            } = {};
            if (this.name != null) {
                data.name = this.name;
            }
            if (this.sql != null) {
                data.sql = this.sql;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.name.length)
                writer.writeString(1, this.name);
            if (this.sql.length)
                writer.writeString(2, this.sql);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbTable {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DbTable();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.name = reader.readString();
                        break;
                    case 2:
                        message.sql = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DbTable {
            return DbTable.deserialize(bytes);
        }
    }
    export class DbTables extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            tables?: DbTable[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("tables" in data && data.tables != undefined) {
                    this.tables = data.tables;
                }
            }
        }
        get tables() {
            return pb_1.Message.getRepeatedWrapperField(this, DbTable, 1) as DbTable[];
        }
        set tables(value: DbTable[]) {
            pb_1.Message.setRepeatedWrapperField(this, 1, value);
        }
        static fromObject(data: {
            tables?: ReturnType<typeof DbTable.prototype.toObject>[];
        }): DbTables {
            const message = new DbTables({});
            if (data.tables != null) {
                message.tables = data.tables.map(item => DbTable.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                tables?: ReturnType<typeof DbTable.prototype.toObject>[];
            } = {};
            if (this.tables != null) {
                data.tables = this.tables.map((item: DbTable) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.tables.length)
                writer.writeRepeatedMessage(1, this.tables, (item: DbTable) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbTables {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DbTables();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.tables, () => pb_1.Message.addToRepeatedWrapperField(message, 1, DbTable.deserialize(reader), DbTable));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DbTables {
            return DbTables.deserialize(bytes);
        }
    }
    export class DbQuery extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            db?: string;
            sql?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("db" in data && data.db != undefined) {
                    this.db = data.db;
                }
                if ("sql" in data && data.sql != undefined) {
                    this.sql = data.sql;
                }
            }
        }
        get db() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set db(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get sql() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set sql(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            db?: string;
            sql?: string;
        }): DbQuery {
            const message = new DbQuery({});
            if (data.db != null) {
                message.db = data.db;
            }
            if (data.sql != null) {
                message.sql = data.sql;
            }
            return message;
        }
        toObject() {
            const data: {
                db?: string;
                sql?: string;
            } = {};
            if (this.db != null) {
                data.db = this.db;
            }
            if (this.sql != null) {
                data.sql = this.sql;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.db.length)
                writer.writeString(1, this.db);
            if (this.sql.length)
                writer.writeString(2, this.sql);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbQuery {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DbQuery();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.db = reader.readString();
                        break;
                    case 2:
                        message.sql = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DbQuery {
            return DbQuery.deserialize(bytes);
        }
    }
    export class DbField extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            type?: number;
            column?: string;
            content?: Uint8Array;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("type" in data && data.type != undefined) {
                    this.type = data.type;
                }
                if ("column" in data && data.column != undefined) {
                    this.column = data.column;
                }
                if ("content" in data && data.content != undefined) {
                    this.content = data.content;
                }
            }
        }
        get type() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set type(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get column() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set column(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get content() {
            return pb_1.Message.getFieldWithDefault(this, 3, new Uint8Array(0)) as Uint8Array;
        }
        set content(value: Uint8Array) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            type?: number;
            column?: string;
            content?: Uint8Array;
        }): DbField {
            const message = new DbField({});
            if (data.type != null) {
                message.type = data.type;
            }
            if (data.column != null) {
                message.column = data.column;
            }
            if (data.content != null) {
                message.content = data.content;
            }
            return message;
        }
        toObject() {
            const data: {
                type?: number;
                column?: string;
                content?: Uint8Array;
            } = {};
            if (this.type != null) {
                data.type = this.type;
            }
            if (this.column != null) {
                data.column = this.column;
            }
            if (this.content != null) {
                data.content = this.content;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.type != 0)
                writer.writeInt32(1, this.type);
            if (this.column.length)
                writer.writeString(2, this.column);
            if (this.content.length)
                writer.writeBytes(3, this.content);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbField {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DbField();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.type = reader.readInt32();
                        break;
                    case 2:
                        message.column = reader.readString();
                        break;
                    case 3:
                        message.content = reader.readBytes();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DbField {
            return DbField.deserialize(bytes);
        }
    }
    export class DbRow extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            fields?: DbField[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("fields" in data && data.fields != undefined) {
                    this.fields = data.fields;
                }
            }
        }
        get fields() {
            return pb_1.Message.getRepeatedWrapperField(this, DbField, 1) as DbField[];
        }
        set fields(value: DbField[]) {
            pb_1.Message.setRepeatedWrapperField(this, 1, value);
        }
        static fromObject(data: {
            fields?: ReturnType<typeof DbField.prototype.toObject>[];
        }): DbRow {
            const message = new DbRow({});
            if (data.fields != null) {
                message.fields = data.fields.map(item => DbField.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                fields?: ReturnType<typeof DbField.prototype.toObject>[];
            } = {};
            if (this.fields != null) {
                data.fields = this.fields.map((item: DbField) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.fields.length)
                writer.writeRepeatedMessage(1, this.fields, (item: DbField) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbRow {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DbRow();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.fields, () => pb_1.Message.addToRepeatedWrapperField(message, 1, DbField.deserialize(reader), DbField));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DbRow {
            return DbRow.deserialize(bytes);
        }
    }
    export class DbRows extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            rows?: DbRow[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("rows" in data && data.rows != undefined) {
                    this.rows = data.rows;
                }
            }
        }
        get rows() {
            return pb_1.Message.getRepeatedWrapperField(this, DbRow, 1) as DbRow[];
        }
        set rows(value: DbRow[]) {
            pb_1.Message.setRepeatedWrapperField(this, 1, value);
        }
        static fromObject(data: {
            rows?: ReturnType<typeof DbRow.prototype.toObject>[];
        }): DbRows {
            const message = new DbRows({});
            if (data.rows != null) {
                message.rows = data.rows.map(item => DbRow.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                rows?: ReturnType<typeof DbRow.prototype.toObject>[];
            } = {};
            if (this.rows != null) {
                data.rows = this.rows.map((item: DbRow) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.rows.length)
                writer.writeRepeatedMessage(1, this.rows, (item: DbRow) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DbRows {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DbRows();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.rows, () => pb_1.Message.addToRepeatedWrapperField(message, 1, DbRow.deserialize(reader), DbRow));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DbRows {
            return DbRows.deserialize(bytes);
        }
    }
    export class Verification extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            v3?: string;
            v4?: string;
            scene?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("v3" in data && data.v3 != undefined) {
                    this.v3 = data.v3;
                }
                if ("v4" in data && data.v4 != undefined) {
                    this.v4 = data.v4;
                }
                if ("scene" in data && data.scene != undefined) {
                    this.scene = data.scene;
                }
            }
        }
        get v3() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set v3(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get v4() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set v4(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get scene() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set scene(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            v3?: string;
            v4?: string;
            scene?: number;
        }): Verification {
            const message = new Verification({});
            if (data.v3 != null) {
                message.v3 = data.v3;
            }
            if (data.v4 != null) {
                message.v4 = data.v4;
            }
            if (data.scene != null) {
                message.scene = data.scene;
            }
            return message;
        }
        toObject() {
            const data: {
                v3?: string;
                v4?: string;
                scene?: number;
            } = {};
            if (this.v3 != null) {
                data.v3 = this.v3;
            }
            if (this.v4 != null) {
                data.v4 = this.v4;
            }
            if (this.scene != null) {
                data.scene = this.scene;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.v3.length)
                writer.writeString(1, this.v3);
            if (this.v4.length)
                writer.writeString(2, this.v4);
            if (this.scene != 0)
                writer.writeInt32(3, this.scene);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Verification {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Verification();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.v3 = reader.readString();
                        break;
                    case 2:
                        message.v4 = reader.readString();
                        break;
                    case 3:
                        message.scene = reader.readInt32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Verification {
            return Verification.deserialize(bytes);
        }
    }
    export class MemberMgmt extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            roomid?: string;
            wxids?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("roomid" in data && data.roomid != undefined) {
                    this.roomid = data.roomid;
                }
                if ("wxids" in data && data.wxids != undefined) {
                    this.wxids = data.wxids;
                }
            }
        }
        get roomid() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set roomid(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get wxids() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set wxids(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            roomid?: string;
            wxids?: string;
        }): MemberMgmt {
            const message = new MemberMgmt({});
            if (data.roomid != null) {
                message.roomid = data.roomid;
            }
            if (data.wxids != null) {
                message.wxids = data.wxids;
            }
            return message;
        }
        toObject() {
            const data: {
                roomid?: string;
                wxids?: string;
            } = {};
            if (this.roomid != null) {
                data.roomid = this.roomid;
            }
            if (this.wxids != null) {
                data.wxids = this.wxids;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.roomid.length)
                writer.writeString(1, this.roomid);
            if (this.wxids.length)
                writer.writeString(2, this.wxids);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MemberMgmt {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new MemberMgmt();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.roomid = reader.readString();
                        break;
                    case 2:
                        message.wxids = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): MemberMgmt {
            return MemberMgmt.deserialize(bytes);
        }
    }
    export class UserInfo extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            wxid?: string;
            name?: string;
            mobile?: string;
            home?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("wxid" in data && data.wxid != undefined) {
                    this.wxid = data.wxid;
                }
                if ("name" in data && data.name != undefined) {
                    this.name = data.name;
                }
                if ("mobile" in data && data.mobile != undefined) {
                    this.mobile = data.mobile;
                }
                if ("home" in data && data.home != undefined) {
                    this.home = data.home;
                }
            }
        }
        get wxid() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set wxid(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get name() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set name(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get mobile() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set mobile(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get home() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set home(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            wxid?: string;
            name?: string;
            mobile?: string;
            home?: string;
        }): UserInfo {
            const message = new UserInfo({});
            if (data.wxid != null) {
                message.wxid = data.wxid;
            }
            if (data.name != null) {
                message.name = data.name;
            }
            if (data.mobile != null) {
                message.mobile = data.mobile;
            }
            if (data.home != null) {
                message.home = data.home;
            }
            return message;
        }
        toObject() {
            const data: {
                wxid?: string;
                name?: string;
                mobile?: string;
                home?: string;
            } = {};
            if (this.wxid != null) {
                data.wxid = this.wxid;
            }
            if (this.name != null) {
                data.name = this.name;
            }
            if (this.mobile != null) {
                data.mobile = this.mobile;
            }
            if (this.home != null) {
                data.home = this.home;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.wxid.length)
                writer.writeString(1, this.wxid);
            if (this.name.length)
                writer.writeString(2, this.name);
            if (this.mobile.length)
                writer.writeString(3, this.mobile);
            if (this.home.length)
                writer.writeString(4, this.home);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): UserInfo {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new UserInfo();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.wxid = reader.readString();
                        break;
                    case 2:
                        message.name = reader.readString();
                        break;
                    case 3:
                        message.mobile = reader.readString();
                        break;
                    case 4:
                        message.home = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): UserInfo {
            return UserInfo.deserialize(bytes);
        }
    }
    export class DecPath extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            src?: string;
            dst?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("src" in data && data.src != undefined) {
                    this.src = data.src;
                }
                if ("dst" in data && data.dst != undefined) {
                    this.dst = data.dst;
                }
            }
        }
        get src() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set src(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get dst() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set dst(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            src?: string;
            dst?: string;
        }): DecPath {
            const message = new DecPath({});
            if (data.src != null) {
                message.src = data.src;
            }
            if (data.dst != null) {
                message.dst = data.dst;
            }
            return message;
        }
        toObject() {
            const data: {
                src?: string;
                dst?: string;
            } = {};
            if (this.src != null) {
                data.src = this.src;
            }
            if (this.dst != null) {
                data.dst = this.dst;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.src.length)
                writer.writeString(1, this.src);
            if (this.dst.length)
                writer.writeString(2, this.dst);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DecPath {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DecPath();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.src = reader.readString();
                        break;
                    case 2:
                        message.dst = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DecPath {
            return DecPath.deserialize(bytes);
        }
    }
    export class Transfer extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            wxid?: string;
            tfid?: string;
            taid?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("wxid" in data && data.wxid != undefined) {
                    this.wxid = data.wxid;
                }
                if ("tfid" in data && data.tfid != undefined) {
                    this.tfid = data.tfid;
                }
                if ("taid" in data && data.taid != undefined) {
                    this.taid = data.taid;
                }
            }
        }
        get wxid() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set wxid(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get tfid() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set tfid(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get taid() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set taid(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            wxid?: string;
            tfid?: string;
            taid?: string;
        }): Transfer {
            const message = new Transfer({});
            if (data.wxid != null) {
                message.wxid = data.wxid;
            }
            if (data.tfid != null) {
                message.tfid = data.tfid;
            }
            if (data.taid != null) {
                message.taid = data.taid;
            }
            return message;
        }
        toObject() {
            const data: {
                wxid?: string;
                tfid?: string;
                taid?: string;
            } = {};
            if (this.wxid != null) {
                data.wxid = this.wxid;
            }
            if (this.tfid != null) {
                data.tfid = this.tfid;
            }
            if (this.taid != null) {
                data.taid = this.taid;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.wxid.length)
                writer.writeString(1, this.wxid);
            if (this.tfid.length)
                writer.writeString(2, this.tfid);
            if (this.taid.length)
                writer.writeString(3, this.taid);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Transfer {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Transfer();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.wxid = reader.readString();
                        break;
                    case 2:
                        message.tfid = reader.readString();
                        break;
                    case 3:
                        message.taid = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Transfer {
            return Transfer.deserialize(bytes);
        }
    }
    export class AttachMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            id?: string;
            thumb?: string;
            extra?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("id" in data && data.id != undefined) {
                    this.id = data.id;
                }
                if ("thumb" in data && data.thumb != undefined) {
                    this.thumb = data.thumb;
                }
                if ("extra" in data && data.extra != undefined) {
                    this.extra = data.extra;
                }
            }
        }
        get id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "0") as string;
        }
        set id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get thumb() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set thumb(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get extra() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set extra(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            id?: string;
            thumb?: string;
            extra?: string;
        }): AttachMsg {
            const message = new AttachMsg({});
            if (data.id != null) {
                message.id = data.id;
            }
            if (data.thumb != null) {
                message.thumb = data.thumb;
            }
            if (data.extra != null) {
                message.extra = data.extra;
            }
            return message;
        }
        toObject() {
            const data: {
                id?: string;
                thumb?: string;
                extra?: string;
            } = {};
            if (this.id != null) {
                data.id = this.id;
            }
            if (this.thumb != null) {
                data.thumb = this.thumb;
            }
            if (this.extra != null) {
                data.extra = this.extra;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.id != "0")
                writer.writeUint64String(1, this.id);
            if (this.thumb.length)
                writer.writeString(2, this.thumb);
            if (this.extra.length)
                writer.writeString(3, this.extra);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): AttachMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new AttachMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.id = reader.readUint64String();
                        break;
                    case 2:
                        message.thumb = reader.readString();
                        break;
                    case 3:
                        message.extra = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): AttachMsg {
            return AttachMsg.deserialize(bytes);
        }
    }
    export class AudioMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            id?: string;
            dir?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("id" in data && data.id != undefined) {
                    this.id = data.id;
                }
                if ("dir" in data && data.dir != undefined) {
                    this.dir = data.dir;
                }
            }
        }
        get id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "0") as string;
        }
        set id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get dir() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set dir(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            id?: string;
            dir?: string;
        }): AudioMsg {
            const message = new AudioMsg({});
            if (data.id != null) {
                message.id = data.id;
            }
            if (data.dir != null) {
                message.dir = data.dir;
            }
            return message;
        }
        toObject() {
            const data: {
                id?: string;
                dir?: string;
            } = {};
            if (this.id != null) {
                data.id = this.id;
            }
            if (this.dir != null) {
                data.dir = this.dir;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.id != "0")
                writer.writeUint64String(1, this.id);
            if (this.dir.length)
                writer.writeString(2, this.dir);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): AudioMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new AudioMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.id = reader.readUint64String();
                        break;
                    case 2:
                        message.dir = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): AudioMsg {
            return AudioMsg.deserialize(bytes);
        }
    }
    export class RichText extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            name?: string;
            account?: string;
            title?: string;
            digest?: string;
            url?: string;
            thumburl?: string;
            receiver?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("name" in data && data.name != undefined) {
                    this.name = data.name;
                }
                if ("account" in data && data.account != undefined) {
                    this.account = data.account;
                }
                if ("title" in data && data.title != undefined) {
                    this.title = data.title;
                }
                if ("digest" in data && data.digest != undefined) {
                    this.digest = data.digest;
                }
                if ("url" in data && data.url != undefined) {
                    this.url = data.url;
                }
                if ("thumburl" in data && data.thumburl != undefined) {
                    this.thumburl = data.thumburl;
                }
                if ("receiver" in data && data.receiver != undefined) {
                    this.receiver = data.receiver;
                }
            }
        }
        get name() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set name(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get account() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set account(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get title() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set title(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get digest() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set digest(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get url() {
            return pb_1.Message.getFieldWithDefault(this, 5, "") as string;
        }
        set url(value: string) {
            pb_1.Message.setField(this, 5, value);
        }
        get thumburl() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set thumburl(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get receiver() {
            return pb_1.Message.getFieldWithDefault(this, 7, "") as string;
        }
        set receiver(value: string) {
            pb_1.Message.setField(this, 7, value);
        }
        static fromObject(data: {
            name?: string;
            account?: string;
            title?: string;
            digest?: string;
            url?: string;
            thumburl?: string;
            receiver?: string;
        }): RichText {
            const message = new RichText({});
            if (data.name != null) {
                message.name = data.name;
            }
            if (data.account != null) {
                message.account = data.account;
            }
            if (data.title != null) {
                message.title = data.title;
            }
            if (data.digest != null) {
                message.digest = data.digest;
            }
            if (data.url != null) {
                message.url = data.url;
            }
            if (data.thumburl != null) {
                message.thumburl = data.thumburl;
            }
            if (data.receiver != null) {
                message.receiver = data.receiver;
            }
            return message;
        }
        toObject() {
            const data: {
                name?: string;
                account?: string;
                title?: string;
                digest?: string;
                url?: string;
                thumburl?: string;
                receiver?: string;
            } = {};
            if (this.name != null) {
                data.name = this.name;
            }
            if (this.account != null) {
                data.account = this.account;
            }
            if (this.title != null) {
                data.title = this.title;
            }
            if (this.digest != null) {
                data.digest = this.digest;
            }
            if (this.url != null) {
                data.url = this.url;
            }
            if (this.thumburl != null) {
                data.thumburl = this.thumburl;
            }
            if (this.receiver != null) {
                data.receiver = this.receiver;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.name.length)
                writer.writeString(1, this.name);
            if (this.account.length)
                writer.writeString(2, this.account);
            if (this.title.length)
                writer.writeString(3, this.title);
            if (this.digest.length)
                writer.writeString(4, this.digest);
            if (this.url.length)
                writer.writeString(5, this.url);
            if (this.thumburl.length)
                writer.writeString(6, this.thumburl);
            if (this.receiver.length)
                writer.writeString(7, this.receiver);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RichText {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new RichText();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.name = reader.readString();
                        break;
                    case 2:
                        message.account = reader.readString();
                        break;
                    case 3:
                        message.title = reader.readString();
                        break;
                    case 4:
                        message.digest = reader.readString();
                        break;
                    case 5:
                        message.url = reader.readString();
                        break;
                    case 6:
                        message.thumburl = reader.readString();
                        break;
                    case 7:
                        message.receiver = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): RichText {
            return RichText.deserialize(bytes);
        }
    }
    export class PatMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            roomid?: string;
            wxid?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("roomid" in data && data.roomid != undefined) {
                    this.roomid = data.roomid;
                }
                if ("wxid" in data && data.wxid != undefined) {
                    this.wxid = data.wxid;
                }
            }
        }
        get roomid() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set roomid(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get wxid() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set wxid(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            roomid?: string;
            wxid?: string;
        }): PatMsg {
            const message = new PatMsg({});
            if (data.roomid != null) {
                message.roomid = data.roomid;
            }
            if (data.wxid != null) {
                message.wxid = data.wxid;
            }
            return message;
        }
        toObject() {
            const data: {
                roomid?: string;
                wxid?: string;
            } = {};
            if (this.roomid != null) {
                data.roomid = this.roomid;
            }
            if (this.wxid != null) {
                data.wxid = this.wxid;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.roomid.length)
                writer.writeString(1, this.roomid);
            if (this.wxid.length)
                writer.writeString(2, this.wxid);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PatMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PatMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.roomid = reader.readString();
                        break;
                    case 2:
                        message.wxid = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PatMsg {
            return PatMsg.deserialize(bytes);
        }
    }
    export class OcrMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            status?: number;
            result?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("result" in data && data.result != undefined) {
                    this.result = data.result;
                }
            }
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set status(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get result() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set result(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            status?: number;
            result?: string;
        }): OcrMsg {
            const message = new OcrMsg({});
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.result != null) {
                message.result = data.result;
            }
            return message;
        }
        toObject() {
            const data: {
                status?: number;
                result?: string;
            } = {};
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.result != null) {
                data.result = this.result;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.status != 0)
                writer.writeInt32(1, this.status);
            if (this.result.length)
                writer.writeString(2, this.result);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): OcrMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new OcrMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.status = reader.readInt32();
                        break;
                    case 2:
                        message.result = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): OcrMsg {
            return OcrMsg.deserialize(bytes);
        }
    }
    export class ForwardMsg extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            id?: string;
            receiver?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("id" in data && data.id != undefined) {
                    this.id = data.id;
                }
                if ("receiver" in data && data.receiver != undefined) {
                    this.receiver = data.receiver;
                }
            }
        }
        get id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "0") as string;
        }
        set id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get receiver() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set receiver(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            id?: string;
            receiver?: string;
        }): ForwardMsg {
            const message = new ForwardMsg({});
            if (data.id != null) {
                message.id = data.id;
            }
            if (data.receiver != null) {
                message.receiver = data.receiver;
            }
            return message;
        }
        toObject() {
            const data: {
                id?: string;
                receiver?: string;
            } = {};
            if (this.id != null) {
                data.id = this.id;
            }
            if (this.receiver != null) {
                data.receiver = this.receiver;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.id != "0")
                writer.writeUint64String(1, this.id);
            if (this.receiver.length)
                writer.writeString(2, this.receiver);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ForwardMsg {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ForwardMsg();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.id = reader.readUint64String();
                        break;
                    case 2:
                        message.receiver = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): ForwardMsg {
            return ForwardMsg.deserialize(bytes);
        }
    }
    export class RoomData extends pb_1.Message {
        #one_of_decls: number[][] = [[2], [4], [6]];
        constructor(data?: any[] | ({
            members?: RoomData.RoomMember[];
            field_3?: number;
            capacity?: number;
            field_7?: number;
            field_8?: number;
            admins?: string[];
        } & (({
            field_2?: number;
        }) | ({
            field_4?: number;
        }) | ({
            field_6?: string;
        })))) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1, 9], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("members" in data && data.members != undefined) {
                    this.members = data.members;
                }
                if ("field_2" in data && data.field_2 != undefined) {
                    this.field_2 = data.field_2;
                }
                if ("field_3" in data && data.field_3 != undefined) {
                    this.field_3 = data.field_3;
                }
                if ("field_4" in data && data.field_4 != undefined) {
                    this.field_4 = data.field_4;
                }
                if ("capacity" in data && data.capacity != undefined) {
                    this.capacity = data.capacity;
                }
                if ("field_6" in data && data.field_6 != undefined) {
                    this.field_6 = data.field_6;
                }
                if ("field_7" in data && data.field_7 != undefined) {
                    this.field_7 = data.field_7;
                }
                if ("field_8" in data && data.field_8 != undefined) {
                    this.field_8 = data.field_8;
                }
                if ("admins" in data && data.admins != undefined) {
                    this.admins = data.admins;
                }
            }
        }
        get members() {
            return pb_1.Message.getRepeatedWrapperField(this, RoomData.RoomMember, 1) as RoomData.RoomMember[];
        }
        set members(value: RoomData.RoomMember[]) {
            pb_1.Message.setRepeatedWrapperField(this, 1, value);
        }
        get field_2() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set field_2(value: number) {
            pb_1.Message.setOneofField(this, 2, this.#one_of_decls[0], value);
        }
        get has_field_2() {
            return pb_1.Message.getField(this, 2) != null;
        }
        get field_3() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set field_3(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get field_4() {
            return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
        }
        set field_4(value: number) {
            pb_1.Message.setOneofField(this, 4, this.#one_of_decls[1], value);
        }
        get has_field_4() {
            return pb_1.Message.getField(this, 4) != null;
        }
        get capacity() {
            return pb_1.Message.getFieldWithDefault(this, 5, 0) as number;
        }
        set capacity(value: number) {
            pb_1.Message.setField(this, 5, value);
        }
        get field_6() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set field_6(value: string) {
            pb_1.Message.setOneofField(this, 6, this.#one_of_decls[2], value);
        }
        get has_field_6() {
            return pb_1.Message.getField(this, 6) != null;
        }
        get field_7() {
            return pb_1.Message.getFieldWithDefault(this, 7, 0) as number;
        }
        set field_7(value: number) {
            pb_1.Message.setField(this, 7, value);
        }
        get field_8() {
            return pb_1.Message.getFieldWithDefault(this, 8, 0) as number;
        }
        set field_8(value: number) {
            pb_1.Message.setField(this, 8, value);
        }
        get admins() {
            return pb_1.Message.getFieldWithDefault(this, 9, []) as string[];
        }
        set admins(value: string[]) {
            pb_1.Message.setField(this, 9, value);
        }
        get _field_2() {
            const cases: {
                [index: number]: "none" | "field_2";
            } = {
                0: "none",
                2: "field_2"
            };
            return cases[pb_1.Message.computeOneofCase(this, [2])];
        }
        get _field_4() {
            const cases: {
                [index: number]: "none" | "field_4";
            } = {
                0: "none",
                4: "field_4"
            };
            return cases[pb_1.Message.computeOneofCase(this, [4])];
        }
        get _field_6() {
            const cases: {
                [index: number]: "none" | "field_6";
            } = {
                0: "none",
                6: "field_6"
            };
            return cases[pb_1.Message.computeOneofCase(this, [6])];
        }
        static fromObject(data: {
            members?: ReturnType<typeof RoomData.RoomMember.prototype.toObject>[];
            field_2?: number;
            field_3?: number;
            field_4?: number;
            capacity?: number;
            field_6?: string;
            field_7?: number;
            field_8?: number;
            admins?: string[];
        }): RoomData {
            const message = new RoomData({});
            if (data.members != null) {
                message.members = data.members.map(item => RoomData.RoomMember.fromObject(item));
            }
            if (data.field_2 != null) {
                message.field_2 = data.field_2;
            }
            if (data.field_3 != null) {
                message.field_3 = data.field_3;
            }
            if (data.field_4 != null) {
                message.field_4 = data.field_4;
            }
            if (data.capacity != null) {
                message.capacity = data.capacity;
            }
            if (data.field_6 != null) {
                message.field_6 = data.field_6;
            }
            if (data.field_7 != null) {
                message.field_7 = data.field_7;
            }
            if (data.field_8 != null) {
                message.field_8 = data.field_8;
            }
            if (data.admins != null) {
                message.admins = data.admins;
            }
            return message;
        }
        toObject() {
            const data: {
                members?: ReturnType<typeof RoomData.RoomMember.prototype.toObject>[];
                field_2?: number;
                field_3?: number;
                field_4?: number;
                capacity?: number;
                field_6?: string;
                field_7?: number;
                field_8?: number;
                admins?: string[];
            } = {};
            if (this.members != null) {
                data.members = this.members.map((item: RoomData.RoomMember) => item.toObject());
            }
            if (this.field_2 != null) {
                data.field_2 = this.field_2;
            }
            if (this.field_3 != null) {
                data.field_3 = this.field_3;
            }
            if (this.field_4 != null) {
                data.field_4 = this.field_4;
            }
            if (this.capacity != null) {
                data.capacity = this.capacity;
            }
            if (this.field_6 != null) {
                data.field_6 = this.field_6;
            }
            if (this.field_7 != null) {
                data.field_7 = this.field_7;
            }
            if (this.field_8 != null) {
                data.field_8 = this.field_8;
            }
            if (this.admins != null) {
                data.admins = this.admins;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.members.length)
                writer.writeRepeatedMessage(1, this.members, (item: RoomData.RoomMember) => item.serialize(writer));
            if (this.has_field_2)
                writer.writeInt32(2, this.field_2);
            if (this.field_3 != 0)
                writer.writeInt32(3, this.field_3);
            if (this.has_field_4)
                writer.writeInt32(4, this.field_4);
            if (this.capacity != 0)
                writer.writeInt32(5, this.capacity);
            if (this.has_field_6)
                writer.writeString(6, this.field_6);
            if (this.field_7 != 0)
                writer.writeInt32(7, this.field_7);
            if (this.field_8 != 0)
                writer.writeInt32(8, this.field_8);
            if (this.admins.length)
                writer.writeRepeatedString(9, this.admins);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RoomData {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new RoomData();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.members, () => pb_1.Message.addToRepeatedWrapperField(message, 1, RoomData.RoomMember.deserialize(reader), RoomData.RoomMember));
                        break;
                    case 2:
                        message.field_2 = reader.readInt32();
                        break;
                    case 3:
                        message.field_3 = reader.readInt32();
                        break;
                    case 4:
                        message.field_4 = reader.readInt32();
                        break;
                    case 5:
                        message.capacity = reader.readInt32();
                        break;
                    case 6:
                        message.field_6 = reader.readString();
                        break;
                    case 7:
                        message.field_7 = reader.readInt32();
                        break;
                    case 8:
                        message.field_8 = reader.readInt32();
                        break;
                    case 9:
                        pb_1.Message.addToRepeatedField(message, 9, reader.readString());
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): RoomData {
            return RoomData.deserialize(bytes);
        }
    }
    export namespace RoomData {
        export class RoomMember extends pb_1.Message {
            #one_of_decls: number[][] = [[2]];
            constructor(data?: any[] | ({
                wxid?: string;
                state?: number;
            } & (({
                name?: string;
            })))) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("wxid" in data && data.wxid != undefined) {
                        this.wxid = data.wxid;
                    }
                    if ("name" in data && data.name != undefined) {
                        this.name = data.name;
                    }
                    if ("state" in data && data.state != undefined) {
                        this.state = data.state;
                    }
                }
            }
            get wxid() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set wxid(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get name() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set name(value: string) {
                pb_1.Message.setOneofField(this, 2, this.#one_of_decls[0], value);
            }
            get has_name() {
                return pb_1.Message.getField(this, 2) != null;
            }
            get state() {
                return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
            }
            set state(value: number) {
                pb_1.Message.setField(this, 3, value);
            }
            get _name() {
                const cases: {
                    [index: number]: "none" | "name";
                } = {
                    0: "none",
                    2: "name"
                };
                return cases[pb_1.Message.computeOneofCase(this, [2])];
            }
            static fromObject(data: {
                wxid?: string;
                name?: string;
                state?: number;
            }): RoomMember {
                const message = new RoomMember({});
                if (data.wxid != null) {
                    message.wxid = data.wxid;
                }
                if (data.name != null) {
                    message.name = data.name;
                }
                if (data.state != null) {
                    message.state = data.state;
                }
                return message;
            }
            toObject() {
                const data: {
                    wxid?: string;
                    name?: string;
                    state?: number;
                } = {};
                if (this.wxid != null) {
                    data.wxid = this.wxid;
                }
                if (this.name != null) {
                    data.name = this.name;
                }
                if (this.state != null) {
                    data.state = this.state;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.wxid.length)
                    writer.writeString(1, this.wxid);
                if (this.has_name)
                    writer.writeString(2, this.name);
                if (this.state != 0)
                    writer.writeInt32(3, this.state);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RoomMember {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new RoomMember();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.wxid = reader.readString();
                            break;
                        case 2:
                            message.name = reader.readString();
                            break;
                        case 3:
                            message.state = reader.readInt32();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): RoomMember {
                return RoomMember.deserialize(bytes);
            }
        }
    }
}
