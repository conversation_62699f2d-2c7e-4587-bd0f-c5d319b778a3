/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor .glyph-margin {
	position: absolute;
	top: 0;
}

/*
	Keeping name short for faster parsing.
	cgmr = core glyph margin rendering (div)
*/
.monaco-editor .glyph-margin-widgets .cgmr {
	position: absolute;
	display: flex;
	align-items: center;
	justify-content: center;
}

/*
	Ensure spinning icons are pixel-perfectly centered and avoid wobble.
	This is only applied to icons that spin to avoid unnecessary
	GPU layers and blurry subpixel AA.
*/
.monaco-editor .glyph-margin-widgets .cgmr.codicon-modifier-spin::before  {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
