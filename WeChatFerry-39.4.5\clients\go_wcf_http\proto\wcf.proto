syntax = "proto3";

package wcf;
option java_package = "com.iamteer";
option go_package = "../wcf";

enum Functions {
    FUNC_RESERVED         = 0x00;
    FUNC_IS_LOGIN         = 0x01;
    FUNC_GET_SELF_WXID    = 0x10;
    FUNC_GET_MSG_TYPES    = 0x11;
    FUNC_GET_CONTACTS     = 0x12;
    FUNC_GET_DB_NAMES     = 0x13;
    FUNC_GET_DB_TABLES    = 0x14;
    FUNC_GET_USER_INFO    = 0x15;
    FUNC_GET_AUDIO_MSG    = 0x16;
    FUNC_SEND_TXT         = 0x20;
    FUNC_SEND_IMG         = 0x21;
    FUNC_SEND_FILE        = 0x22;
    FUNC_SEND_XML         = 0x23;
    FUNC_SEND_EMOTION     = 0x24;
    FUNC_SEND_RICH_TXT    = 0x25;
    FUNC_SEND_PAT_MSG     = 0x26;
    FUNC_FORWARD_MSG      = 0x27;
    FUNC_ENABLE_RECV_TXT  = 0x30;
    FUNC_DISABLE_RECV_TXT = 0x40;
    FUNC_EXEC_DB_QUERY    = 0x50;
    FUNC_ACCEPT_FRIEND    = 0x51;
    FUNC_RECV_TRANSFER    = 0x52;
    FUNC_REFRESH_PYQ      = 0x53;
    FUNC_DOWNLOAD_ATTACH  = 0x54;
    FUNC_GET_CONTACT_INFO = 0x55;
    FUNC_REVOKE_MSG       = 0x56;
    FUNC_DECRYPT_IMAGE    = 0x60;
    FUNC_EXEC_OCR         = 0x61;
    FUNC_ADD_ROOM_MEMBERS = 0x70;
    FUNC_DEL_ROOM_MEMBERS = 0x71;
    FUNC_INV_ROOM_MEMBERS = 0x72;
}

message Request
{
    Functions func = 1;
    oneof msg
    {
        Empty empty    = 2;
        string str     = 3;
        TextMsg txt    = 4;
        PathMsg file   = 5;
        DbQuery query  = 6;
        Verification v = 7;
        MemberMgmt m   = 8;  // 群成员管理，添加、删除、邀请
        XmlMsg xml     = 9;
        DecPath dec    = 10;
        Transfer tf    = 11;
        uint64 ui64    = 12; // 64 位整数，通用
        bool flag      = 13;
        AttachMsg att  = 14;
        AudioMsg am    = 15;
        RichText rt    = 16;
        PatMsg pm      = 17;
        ForwardMsg fm  = 18;
    }
}

message Response
{
    Functions func = 1;
    oneof msg
    {
        int32 status         = 2;  // Int 状态，通用
        string str           = 3;  // 字符串
        WxMsg wxmsg          = 4;  // 微信消息
        MsgTypes types       = 5;  // 消息类型
        RpcContacts contacts = 6;  // 联系人
        DbNames dbs          = 7;  // 数据库列表
        DbTables tables      = 8;  // 表列表
        DbRows rows          = 9;  // 行列表
        UserInfo ui          = 10; // 个人信息
        OcrMsg ocr           = 11; // OCR 结果
    };
}

message Empty { }

message WxMsg
{
    bool is_self   = 1;  // 是否自己发送的
    bool is_group  = 2;  // 是否群消息
    uint64 id      = 3;  // 消息 id
    uint32 type    = 4;  // 消息类型
    uint32 ts      = 5;  // 消息类型
    string roomid  = 6;  // 群 id（如果是群消息的话）
    string content = 7;  // 消息内容
    string sender  = 8;  // 消息发送者
    string sign    = 9;  // Sign
    string thumb   = 10; // 缩略图
    string extra   = 11; // 附加内容
    string xml     = 12; // 消息 xml
}

message TextMsg
{
    string msg      = 1; // 要发送的消息内容
    string receiver = 2; // 消息接收人，当为群时可@
    string aters    = 3; // 要@的人列表，逗号分隔
}

message PathMsg
{
    string path     = 1; // 要发送的图片的路径
    string receiver = 2; // 消息接收人
}

message XmlMsg
{
    string receiver = 1; // 消息接收人
    string content  = 2; // xml 内容
    string path     = 3; // 图片路径
    int32 type      = 4; // 消息类型
}

message MsgTypes { map<int32, string> types = 1; }

message RpcContact
{
    string wxid     = 1; // 微信 id
    string code     = 2; // 微信号
    string remark   = 3; // 备注
    string name     = 4; // 微信昵称
    string country  = 5; // 国家
    string province = 6; // 省/州
    string city     = 7; // 城市
    int32 gender    = 8; // 性别
}
message RpcContacts { repeated RpcContact contacts = 1; }

message DbNames { repeated string names = 1; }

message DbTable
{
    string name = 1; // 表名
    string sql  = 2; // 建表 SQL
}
message DbTables { repeated DbTable tables = 1; }

message DbQuery
{
    string db  = 1; // 目标数据库
    string sql = 2; // 查询 SQL
}

message DbField
{
    int32 type    = 1; // 字段类型
    string column = 2; // 字段名称
    bytes content = 3; // 字段内容
}
message DbRow { repeated DbField fields = 1; }
message DbRows { repeated DbRow rows = 1; }

message Verification
{
    string v3   = 1; // 加密的用户名
    string v4   = 2; // Ticket
    int32 scene = 3; // 添加方式：17 名片，30 扫码
}

message MemberMgmt
{
    string roomid = 1; // 要加的群ID
    string wxids  = 2; // 要加群的人列表，逗号分隔
}

message UserInfo
{
    string wxid   = 1; // 微信ID
    string name   = 2; // 昵称
    string mobile = 3; // 手机号
    string home   = 4; // 文件/图片等父路径
}

message DecPath
{
    string src = 1; // 源路径
    string dst = 2; // 目标路径
}

message Transfer
{
    string wxid = 1; // 转账人
    string tfid = 2; // 转账id transferid
    string taid = 3; // Transaction id
}

message AttachMsg
{
    uint64 id    = 1; // 消息 id
    string thumb = 2; // 消息中的 thumb
    string extra = 3; // 消息中的 extra
}

message AudioMsg
{
    uint64 id  = 1; // 语音消息 id
    string dir = 2; // 存放目录
}

message RichText
{
    string name     = 1; // 显示名字
    string account  = 2; // 公众号 id
    string title    = 3; // 标题
    string digest   = 4; // 摘要
    string url      = 5; // 链接
    string thumburl = 6; // 缩略图
    string receiver = 7; // 接收人
}

message PatMsg
{
    string roomid = 1; // 群 id
    string wxid   = 2; // wxid
}

message OcrMsg
{
    int32 status  = 1; // 状态
    string result = 2; // 结果
}

message ForwardMsg
{
    uint64 id       = 1; // 待转发消息 ID
    string receiver = 2; // 转发接收目标，群为 roomId，个人为 wxid
}
