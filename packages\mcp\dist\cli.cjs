'use strict';

const stdio_js = require('@modelcontextprotocol/sdk/server/stdio.js');
const index = require('./index.cjs');
const wechaty = require('wechaty');
const puppet = require('@wechatferry/puppet');
require('@modelcontextprotocol/sdk/server/mcp.js');
require('zod');

process.on("SIGINT", async () => {
  process.stdin.setRawMode(false);
  process.stdin.pause();
});
async function main() {
  const puppet$1 = new puppet.WechatferryPuppet();
  const wechaty$1 = wechaty.WechatyBuilder.build({
    puppet: puppet$1
  });
  await wechaty$1.start();
  const server = new index.WechatFerryServer({
    wechaty: wechaty$1
  });
  await wechaty$1.ready();
  const transport = new stdio_js.StdioServerTransport();
  await server.connect(transport);
}

exports.main = main;
