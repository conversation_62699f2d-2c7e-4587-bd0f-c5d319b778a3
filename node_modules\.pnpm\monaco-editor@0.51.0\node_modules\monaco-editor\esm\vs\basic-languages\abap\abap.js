/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/


// src/basic-languages/abap/abap.ts
var conf = {
  comments: {
    lineComment: "*"
  },
  brackets: [
    ["[", "]"],
    ["(", ")"]
  ]
};
var language = {
  defaultToken: "invalid",
  ignoreCase: true,
  tokenPostfix: ".abap",
  keywords: [
    "abap-source",
    "abbreviated",
    "abstract",
    "accept",
    "accepting",
    "according",
    "activation",
    "actual",
    "add",
    "add-corresponding",
    "adjacent",
    "after",
    "alias",
    "aliases",
    "align",
    "all",
    "allocate",
    "alpha",
    "analysis",
    "analyzer",
    "and",
    // also an operator
    "append",
    "appendage",
    "appending",
    "application",
    "archive",
    "area",
    "arithmetic",
    "as",
    "ascending",
    "aspect",
    "assert",
    "assign",
    "assigned",
    "assigning",
    "association",
    "asynchronous",
    "at",
    "attributes",
    "authority",
    "authority-check",
    "avg",
    "back",
    "background",
    "backup",
    "backward",
    "badi",
    "base",
    "before",
    "begin",
    "between",
    // also an operator
    "big",
    "binary",
    "bintohex",
    "bit",
    "black",
    "blank",
    "blanks",
    "blob",
    "block",
    "blocks",
    "blue",
    "bound",
    "boundaries",
    "bounds",
    "boxed",
    "break-point",
    "buffer",
    "by",
    "bypassing",
    "byte",
    "byte-order",
    "call",
    "calling",
    "case",
    "cast",
    "casting",
    "catch",
    "center",
    "centered",
    "chain",
    "chain-input",
    "chain-request",
    "change",
    "changing",
    "channels",
    "character",
    "char-to-hex",
    "check",
    "checkbox",
    "ci_",
    "circular",
    "class",
    "class-coding",
    "class-data",
    "class-events",
    "class-methods",
    "class-pool",
    "cleanup",
    "clear",
    "client",
    "clob",
    "clock",
    "close",
    "coalesce",
    "code",
    "coding",
    "col_background",
    "col_group",
    "col_heading",
    "col_key",
    "col_negative",
    "col_normal",
    "col_positive",
    "col_total",
    "collect",
    "color",
    "column",
    "columns",
    "comment",
    "comments",
    "commit",
    "common",
    "communication",
    "comparing",
    "component",
    "components",
    "compression",
    "compute",
    "concat",
    "concat_with_space",
    "concatenate",
    "cond",
    "condense",
    // also a built-in
    "condition",
    "connect",
    "connection",
    "constants",
    "context",
    "contexts",
    "continue",
    "control",
    "controls",
    "conv",
    "conversion",
    "convert",
    "copies",
    "copy",
    "corresponding",
    "country",
    "cover",
    "cpi",
    "create",
    "creating",
    "critical",
    "currency",
    "currency_conversion",
    "current",
    "cursor",
    "cursor-selection",
    "customer",
    "customer-function",
    "dangerous",
    "data",
    "database",
    "datainfo",
    "dataset",
    "date",
    "dats_add_days",
    "dats_add_months",
    "dats_days_between",
    "dats_is_valid",
    "daylight",
    "dd/mm/yy",
    "dd/mm/yyyy",
    "ddmmyy",
    "deallocate",
    "decimal_shift",
    "decimals",
    "declarations",
    "deep",
    "default",
    "deferred",
    "define",
    "defining",
    "definition",
    "delete",
    "deleting",
    "demand",
    "department",
    "descending",
    "describe",
    "destination",
    "detail",
    "dialog",
    "directory",
    "disconnect",
    "display",
    "display-mode",
    "distinct",
    "divide",
    "divide-corresponding",
    "division",
    "do",
    "dummy",
    "duplicate",
    "duplicates",
    "duration",
    "during",
    "dynamic",
    "dynpro",
    "edit",
    "editor-call",
    "else",
    "elseif",
    "empty",
    "enabled",
    "enabling",
    "encoding",
    "end",
    "endat",
    "endcase",
    "endcatch",
    "endchain",
    "endclass",
    "enddo",
    "endenhancement",
    "end-enhancement-section",
    "endexec",
    "endform",
    "endfunction",
    "endian",
    "endif",
    "ending",
    "endinterface",
    "end-lines",
    "endloop",
    "endmethod",
    "endmodule",
    "end-of-definition",
    "end-of-editing",
    "end-of-file",
    "end-of-page",
    "end-of-selection",
    "endon",
    "endprovide",
    "endselect",
    "end-test-injection",
    "end-test-seam",
    "endtry",
    "endwhile",
    "endwith",
    "engineering",
    "enhancement",
    "enhancement-point",
    "enhancements",
    "enhancement-section",
    "entries",
    "entry",
    "enum",
    "environment",
    "equiv",
    // also an operator
    "errormessage",
    "errors",
    "escaping",
    "event",
    "events",
    "exact",
    "except",
    "exception",
    "exceptions",
    "exception-table",
    "exclude",
    "excluding",
    "exec",
    "execute",
    "exists",
    "exit",
    "exit-command",
    "expand",
    "expanding",
    "expiration",
    "explicit",
    "exponent",
    "export",
    "exporting",
    "extend",
    "extended",
    "extension",
    "extract",
    "fail",
    "fetch",
    "field",
    "field-groups",
    "fields",
    "field-symbol",
    "field-symbols",
    "file",
    "filter",
    "filters",
    "filter-table",
    "final",
    "find",
    // also a built-in
    "first",
    "first-line",
    "fixed-point",
    "fkeq",
    "fkge",
    "flush",
    "font",
    "for",
    "form",
    "format",
    "forward",
    "found",
    "frame",
    "frames",
    "free",
    "friends",
    "from",
    "function",
    "functionality",
    "function-pool",
    "further",
    "gaps",
    "generate",
    "get",
    "giving",
    "gkeq",
    "gkge",
    "global",
    "grant",
    "green",
    "group",
    "groups",
    "handle",
    "handler",
    "harmless",
    "hashed",
    // also a table type
    "having",
    "hdb",
    "header",
    "headers",
    "heading",
    "head-lines",
    "help-id",
    "help-request",
    "hextobin",
    "hide",
    "high",
    "hint",
    "hold",
    "hotspot",
    "icon",
    "id",
    "identification",
    "identifier",
    "ids",
    "if",
    "ignore",
    "ignoring",
    "immediately",
    "implementation",
    "implementations",
    "implemented",
    "implicit",
    "import",
    "importing",
    "in",
    // also an operator
    "inactive",
    "incl",
    "include",
    "includes",
    "including",
    "increment",
    "index",
    // also a table type
    "index-line",
    "infotypes",
    "inheriting",
    "init",
    "initial",
    "initialization",
    "inner",
    "inout",
    "input",
    "insert",
    // also a built-in
    "instance",
    "instances",
    "instr",
    "intensified",
    "interface",
    "interface-pool",
    "interfaces",
    "internal",
    "intervals",
    "into",
    "inverse",
    "inverted-date",
    "is",
    "iso",
    "job",
    "join",
    "keep",
    "keeping",
    "kernel",
    "key",
    "keys",
    "keywords",
    "kind",
    "language",
    "last",
    "late",
    "layout",
    "leading",
    "leave",
    "left",
    "left-justified",
    "leftplus",
    "leftspace",
    "legacy",
    "length",
    "let",
    "level",
    "levels",
    "like",
    "line",
    "lines",
    // also a built-in
    "line-count",
    "linefeed",
    "line-selection",
    "line-size",
    "list",
    "listbox",
    "list-processing",
    "little",
    "llang",
    "load",
    "load-of-program",
    "lob",
    "local",
    "locale",
    "locator",
    "logfile",
    "logical",
    "log-point",
    "long",
    "loop",
    "low",
    "lower",
    "lpad",
    "lpi",
    "ltrim",
    "mail",
    "main",
    "major-id",
    "mapping",
    "margin",
    "mark",
    "mask",
    "match",
    // also a built-in
    "matchcode",
    "max",
    "maximum",
    "medium",
    "members",
    "memory",
    "mesh",
    "message",
    "message-id",
    "messages",
    "messaging",
    "method",
    "methods",
    "min",
    "minimum",
    "minor-id",
    "mm/dd/yy",
    "mm/dd/yyyy",
    "mmddyy",
    "mode",
    "modif",
    "modifier",
    "modify",
    "module",
    "move",
    "move-corresponding",
    "multiply",
    "multiply-corresponding",
    "name",
    "nametab",
    "native",
    "nested",
    "nesting",
    "new",
    "new-line",
    "new-page",
    "new-section",
    "next",
    "no",
    "no-display",
    "no-extension",
    "no-gap",
    "no-gaps",
    "no-grouping",
    "no-heading",
    "no-scrolling",
    "no-sign",
    "no-title",
    "no-topofpage",
    "no-zero",
    "node",
    "nodes",
    "non-unicode",
    "non-unique",
    "not",
    // also an operator
    "null",
    "number",
    "object",
    // also a data type
    "objects",
    "obligatory",
    "occurrence",
    "occurrences",
    "occurs",
    "of",
    "off",
    "offset",
    "ole",
    "on",
    "only",
    "open",
    "option",
    "optional",
    "options",
    "or",
    // also an operator
    "order",
    "other",
    "others",
    "out",
    "outer",
    "output",
    "output-length",
    "overflow",
    "overlay",
    "pack",
    "package",
    "pad",
    "padding",
    "page",
    "pages",
    "parameter",
    "parameters",
    "parameter-table",
    "part",
    "partially",
    "pattern",
    "percentage",
    "perform",
    "performing",
    "person",
    "pf1",
    "pf10",
    "pf11",
    "pf12",
    "pf13",
    "pf14",
    "pf15",
    "pf2",
    "pf3",
    "pf4",
    "pf5",
    "pf6",
    "pf7",
    "pf8",
    "pf9",
    "pf-status",
    "pink",
    "places",
    "pool",
    "pos_high",
    "pos_low",
    "position",
    "pragmas",
    "precompiled",
    "preferred",
    "preserving",
    "primary",
    "print",
    "print-control",
    "priority",
    "private",
    "procedure",
    "process",
    "program",
    "property",
    "protected",
    "provide",
    "public",
    "push",
    "pushbutton",
    "put",
    "queue-only",
    "quickinfo",
    "radiobutton",
    "raise",
    "raising",
    "range",
    "ranges",
    "read",
    "reader",
    "read-only",
    "receive",
    "received",
    "receiver",
    "receiving",
    "red",
    "redefinition",
    "reduce",
    "reduced",
    "ref",
    "reference",
    "refresh",
    "regex",
    "reject",
    "remote",
    "renaming",
    "replace",
    // also a built-in
    "replacement",
    "replacing",
    "report",
    "request",
    "requested",
    "reserve",
    "reset",
    "resolution",
    "respecting",
    "responsible",
    "result",
    "results",
    "resumable",
    "resume",
    "retry",
    "return",
    "returncode",
    "returning",
    "returns",
    "right",
    "right-justified",
    "rightplus",
    "rightspace",
    "risk",
    "rmc_communication_failure",
    "rmc_invalid_status",
    "rmc_system_failure",
    "role",
    "rollback",
    "rows",
    "rpad",
    "rtrim",
    "run",
    "sap",
    "sap-spool",
    "saving",
    "scale_preserving",
    "scale_preserving_scientific",
    "scan",
    "scientific",
    "scientific_with_leading_zero",
    "scroll",
    "scroll-boundary",
    "scrolling",
    "search",
    "secondary",
    "seconds",
    "section",
    "select",
    "selection",
    "selections",
    "selection-screen",
    "selection-set",
    "selection-sets",
    "selection-table",
    "select-options",
    "send",
    "separate",
    "separated",
    "set",
    "shared",
    "shift",
    "short",
    "shortdump-id",
    "sign_as_postfix",
    "single",
    "size",
    "skip",
    "skipping",
    "smart",
    "some",
    "sort",
    "sortable",
    "sorted",
    // also a table type
    "source",
    "specified",
    "split",
    "spool",
    "spots",
    "sql",
    "sqlscript",
    "stable",
    "stamp",
    "standard",
    // also a table type
    "starting",
    "start-of-editing",
    "start-of-selection",
    "state",
    "statement",
    "statements",
    "static",
    "statics",
    "statusinfo",
    "step-loop",
    "stop",
    "structure",
    "structures",
    "style",
    "subkey",
    "submatches",
    "submit",
    "subroutine",
    "subscreen",
    "subtract",
    "subtract-corresponding",
    "suffix",
    "sum",
    "summary",
    "summing",
    "supplied",
    "supply",
    "suppress",
    "switch",
    "switchstates",
    "symbol",
    "syncpoints",
    "syntax",
    "syntax-check",
    "syntax-trace",
    "system-call",
    "system-exceptions",
    "system-exit",
    "tab",
    "tabbed",
    "table",
    "tables",
    "tableview",
    "tabstrip",
    "target",
    "task",
    "tasks",
    "test",
    "testing",
    "test-injection",
    "test-seam",
    "text",
    "textpool",
    "then",
    "throw",
    "time",
    "times",
    "timestamp",
    "timezone",
    "tims_is_valid",
    "title",
    "titlebar",
    "title-lines",
    "to",
    "tokenization",
    "tokens",
    "top-lines",
    "top-of-page",
    "trace-file",
    "trace-table",
    "trailing",
    "transaction",
    "transfer",
    "transformation",
    "translate",
    // also a built-in
    "transporting",
    "trmac",
    "truncate",
    "truncation",
    "try",
    "tstmp_add_seconds",
    "tstmp_current_utctimestamp",
    "tstmp_is_valid",
    "tstmp_seconds_between",
    "type",
    "type-pool",
    "type-pools",
    "types",
    "uline",
    "unassign",
    "under",
    "unicode",
    "union",
    "unique",
    "unit_conversion",
    "unix",
    "unpack",
    "until",
    "unwind",
    "up",
    "update",
    "upper",
    "user",
    "user-command",
    "using",
    "utf-8",
    "valid",
    "value",
    "value-request",
    "values",
    "vary",
    "varying",
    "verification-message",
    "version",
    "via",
    "view",
    "visible",
    "wait",
    "warning",
    "when",
    "whenever",
    "where",
    "while",
    "width",
    "window",
    "windows",
    "with",
    "with-heading",
    "without",
    "with-title",
    "word",
    "work",
    "write",
    "writer",
    "xml",
    "xsd",
    "yellow",
    "yes",
    "yymmdd",
    "zero",
    "zone",
    // since 7.55:
    "abap_system_timezone",
    "abap_user_timezone",
    "access",
    "action",
    "adabas",
    "adjust_numbers",
    "allow_precision_loss",
    "allowed",
    "amdp",
    "applicationuser",
    "as_geo_json",
    "as400",
    "associations",
    "balance",
    "behavior",
    "breakup",
    "bulk",
    "cds",
    "cds_client",
    "check_before_save",
    "child",
    "clients",
    "corr",
    "corr_spearman",
    "cross",
    "cycles",
    "datn_add_days",
    "datn_add_months",
    "datn_days_between",
    "dats_from_datn",
    "dats_tims_to_tstmp",
    "dats_to_datn",
    "db2",
    "db6",
    "ddl",
    "dense_rank",
    "depth",
    "deterministic",
    "discarding",
    "entities",
    "entity",
    "error",
    "failed",
    "finalize",
    "first_value",
    "fltp_to_dec",
    "following",
    "fractional",
    "full",
    "graph",
    "grouping",
    "hierarchy",
    "hierarchy_ancestors",
    "hierarchy_ancestors_aggregate",
    "hierarchy_descendants",
    "hierarchy_descendants_aggregate",
    "hierarchy_siblings",
    "incremental",
    "indicators",
    "lag",
    "last_value",
    "lead",
    "leaves",
    "like_regexpr",
    "link",
    "locale_sap",
    "lock",
    "locks",
    "many",
    "mapped",
    "matched",
    "measures",
    "median",
    "mssqlnt",
    "multiple",
    "nodetype",
    "ntile",
    "nulls",
    "occurrences_regexpr",
    "one",
    "operations",
    "oracle",
    "orphans",
    "over",
    "parent",
    "parents",
    "partition",
    "pcre",
    "period",
    "pfcg_mapping",
    "preceding",
    "privileged",
    "product",
    "projection",
    "rank",
    "redirected",
    "replace_regexpr",
    "reported",
    "response",
    "responses",
    "root",
    "row",
    "row_number",
    "sap_system_date",
    "save",
    "schema",
    "session",
    "sets",
    "shortdump",
    "siblings",
    "spantree",
    "start",
    "stddev",
    "string_agg",
    "subtotal",
    "sybase",
    "tims_from_timn",
    "tims_to_timn",
    "to_blob",
    "to_clob",
    "total",
    "trace-entry",
    "tstmp_to_dats",
    "tstmp_to_dst",
    "tstmp_to_tims",
    "tstmpl_from_utcl",
    "tstmpl_to_utcl",
    "unbounded",
    "utcl_add_seconds",
    "utcl_current",
    "utcl_seconds_between",
    "uuid",
    "var",
    "verbatim"
  ],
  //
  // Built-in Functions
  //
  // Functions that are also statements have been moved to keywords
  //
  builtinFunctions: [
    "abs",
    "acos",
    "asin",
    "atan",
    "bit-set",
    "boolc",
    "boolx",
    "ceil",
    "char_off",
    "charlen",
    "cmax",
    "cmin",
    "concat_lines_of",
    // 'condense', // moved to keywords
    "contains",
    "contains_any_not_of",
    "contains_any_of",
    "cos",
    "cosh",
    "count",
    "count_any_not_of",
    "count_any_of",
    "dbmaxlen",
    "distance",
    "escape",
    "exp",
    // 'find', // moved to keywords
    "find_any_not_of",
    "find_any_of",
    "find_end",
    "floor",
    "frac",
    "from_mixed",
    // 'insert', // moved to keywords
    "ipow",
    "line_exists",
    "line_index",
    // 'lines', // moved to keywords
    "log",
    "log10",
    // 'match', // moved to keywords
    "matches",
    "nmax",
    "nmin",
    "numofchar",
    "repeat",
    // 'replace', // moved to keywords
    "rescale",
    "reverse",
    "round",
    "segment",
    "shift_left",
    "shift_right",
    "sign",
    "sin",
    "sinh",
    "sqrt",
    "strlen",
    "substring",
    "substring_after",
    "substring_before",
    "substring_from",
    "substring_to",
    "tan",
    "tanh",
    "to_lower",
    "to_mixed",
    "to_upper",
    // 'translate', // moved to keywords
    "trunc",
    "utclong_add",
    // since 7.54
    "utclong_current",
    // since 7.54
    "utclong_diff",
    // since 7.54
    "xsdbool",
    "xstrlen"
  ],
  //
  // Data Types
  //
  // Data types that are also part of statements have been moved to keywords
  //
  typeKeywords: [
    // built-in abap types
    "b",
    "c",
    "d",
    "decfloat16",
    "decfloat34",
    "f",
    "i",
    "int8",
    // since 7.54
    "n",
    "p",
    "s",
    "string",
    "t",
    "utclong",
    // since 7.54
    "x",
    "xstring",
    // generic data types
    "any",
    "clike",
    "csequence",
    "decfloat",
    // 'object', // moved to keywords
    "numeric",
    "simple",
    "xsequence",
    // ddic/sql data types
    "accp",
    "char",
    "clnt",
    "cuky",
    "curr",
    "datn",
    // since 7.55
    "dats",
    "d16d",
    // since 7.55
    "d16n",
    // since 7.55
    "d16r",
    // since 7.55
    "d34d",
    // since 7.55
    "d34n",
    // since 7.55
    "d34r",
    // since 7.55
    "dec",
    "df16_dec",
    "df16_raw",
    "df34_dec",
    "df34_raw",
    "fltp",
    "geom_ewkb",
    // since 7.55
    "int1",
    "int2",
    "int4",
    "lang",
    "lchr",
    "lraw",
    "numc",
    "quan",
    "raw",
    "rawstring",
    "sstring",
    "timn",
    // since 7.55
    "tims",
    "unit",
    "utcl",
    // since 7.55
    // ddic data types (obsolete)
    "df16_scl",
    "df34_scl",
    "prec",
    "varc",
    // special data types and constants
    "abap_bool",
    "abap_false",
    "abap_true",
    "abap_undefined",
    "me",
    "screen",
    "space",
    "super",
    "sy",
    "syst",
    "table_line",
    // obsolete data object
    "*sys*"
  ],
  builtinMethods: ["class_constructor", "constructor"],
  derivedTypes: [
    "%CID",
    "%CID_REF",
    "%CONTROL",
    "%DATA",
    "%ELEMENT",
    "%FAIL",
    "%KEY",
    "%MSG",
    "%PARAM",
    "%PID",
    "%PID_ASSOC",
    "%PID_PARENT",
    "%_HINTS"
  ],
  cdsLanguage: [
    "@AbapAnnotation",
    "@AbapCatalog",
    "@AccessControl",
    "@API",
    "@ClientDependent",
    "@ClientHandling",
    "@CompatibilityContract",
    "@DataAging",
    "@EndUserText",
    "@Environment",
    "@LanguageDependency",
    "@MappingRole",
    "@Metadata",
    "@MetadataExtension",
    "@ObjectModel",
    "@Scope",
    "@Semantics",
    "$EXTENSION",
    "$SELF"
  ],
  selectors: ["->", "->*", "=>", "~", "~*"],
  //
  // Operators
  //
  // Operators that can be part of statements have been moved to keywords
  //
  operators: [
    // arithmetic operators
    " +",
    " -",
    "/",
    "*",
    "**",
    "div",
    "mod",
    // assignment operators
    "=",
    "#",
    "@",
    "+=",
    "-=",
    "*=",
    "/=",
    "**=",
    "&&=",
    // casting operator
    "?=",
    // concat operators
    "&",
    "&&",
    // bit operators
    "bit-and",
    "bit-not",
    "bit-or",
    "bit-xor",
    "m",
    "o",
    "z",
    // boolean operators
    // 'and', // moved to keywords
    // 'equiv', // moved to keywords
    // 'not', // moved to keywords
    // 'or', // moved to keywords
    // comparison operators
    "<",
    " >",
    // todo: separate from -> and =>
    "<=",
    ">=",
    "<>",
    "><",
    // obsolete
    "=<",
    // obsolete
    "=>",
    // obsolete
    // 'between', // moved to keywords
    "bt",
    "byte-ca",
    "byte-cn",
    "byte-co",
    "byte-cs",
    "byte-na",
    "byte-ns",
    "ca",
    "cn",
    "co",
    "cp",
    "cs",
    "eq",
    // obsolete
    "ge",
    // obsolete
    "gt",
    // obsolete
    // 'in', // moved to keywords
    "le",
    // obsolete
    "lt",
    // obsolete
    "na",
    "nb",
    "ne",
    // obsolete
    "np",
    "ns",
    // cds
    "*/",
    "*:",
    "--",
    "/*",
    "//"
  ],
  symbols: /[=><!~?&+\-*\/\^%#@]+/,
  tokenizer: {
    root: [
      [
        /[a-z_\/$%@]([\w\/$%]|-(?!>))*/,
        // exclude '->' selector
        {
          cases: {
            "@typeKeywords": "type",
            "@keywords": "keyword",
            "@cdsLanguage": "annotation",
            "@derivedTypes": "type",
            "@builtinFunctions": "type",
            "@builtinMethods": "type",
            "@operators": "key",
            "@default": "identifier"
          }
        }
      ],
      [/<[\w]+>/, "identifier"],
      // field symbols
      [/##[\w|_]+/, "comment"],
      // pragmas
      { include: "@whitespace" },
      [/[:,.]/, "delimiter"],
      [/[{}()\[\]]/, "@brackets"],
      [
        /@symbols/,
        {
          cases: {
            "@selectors": "tag",
            "@operators": "key",
            "@default": ""
          }
        }
      ],
      [/'/, { token: "string", bracket: "@open", next: "@stringquote" }],
      [/`/, { token: "string", bracket: "@open", next: "@stringping" }],
      [/\|/, { token: "string", bracket: "@open", next: "@stringtemplate" }],
      [/\d+/, "number"]
    ],
    stringtemplate: [
      [/[^\\\|]+/, "string"],
      [/\\\|/, "string"],
      [/\|/, { token: "string", bracket: "@close", next: "@pop" }]
    ],
    stringping: [
      [/[^\\`]+/, "string"],
      [/`/, { token: "string", bracket: "@close", next: "@pop" }]
    ],
    stringquote: [
      [/[^\\']+/, "string"],
      [/'/, { token: "string", bracket: "@close", next: "@pop" }]
    ],
    whitespace: [
      [/[ \t\r\n]+/, ""],
      [/^\*.*$/, "comment"],
      [/\".*$/, "comment"]
    ]
  }
};
export {
  conf,
  language
};
