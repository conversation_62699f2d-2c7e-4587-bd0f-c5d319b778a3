{"name": "@unocss/inspector", "type": "module", "version": "0.65.4", "description": "The inspector UI for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/inspector#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/inspector"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "inspector", "debugger"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"colorette": "^2.0.20", "gzip-size": "^6.0.0", "sirv": "^3.0.0", "vue-flow-layout": "^0.1.1", "@unocss/core": "0.65.4", "@unocss/rule-utils": "0.65.4"}, "scripts": {"build": "unbuild", "build-post": "vite build", "stub": "unbuild --stub", "dev": "nr stub && vite", "update-post": "vite build", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}